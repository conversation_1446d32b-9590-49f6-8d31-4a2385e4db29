/*
 * @Author: 焦质晔
 * @Date: 2022-06-03 19:11:05
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-18 13:20:44
 */
import React from 'react';
import classNames from 'classnames';
import localforage from 'localforage';
import { isEqual } from 'lodash-es';
import { useSelector, useDispatch } from '@/store';
import {
  createWorkbench,
  createWorkbenchList,
  createMicroMenu,
  createIframeMenu,
  createTabMenu,
} from '@/store/actions';
import { getUserInfo } from '@/utils/cookies';
import { getGroupCode } from '@/utils';
import { useTool } from '@/hooks';
import config from '@/config';

import type { AppState, IWorkbench } from '@/store/reducers/app';

import { ReactSortable } from 'react-sortablejs';
import { Dropdown } from '@jiaozhiye/qm-design-react';
import { MenuCutIcon, CheckOutlined, HolderOutlined } from '@/icons';

import './index.less';

const WorkbenchSetting: React.FC = () => {
  const { workbenchList, microMenus, tabMenus } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { openView } = useTool();

  const initialRef = React.useRef<boolean>(false);

  const [visible, setVisible] = React.useState<boolean>(false);
  const disabled = React.useMemo(() => !workbenchList.length, [workbenchList]);

  React.useLayoutEffect(() => {
    if (initialRef.current) return;
    if (workbenchList.length) {
      initialHandle();
      initialRef.current = true;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [workbenchList]);

  const initialHandle = async () => {
    const list = await getLocalWbList();
    if (list) {
      const results: IWorkbench[] = [];
      list.forEach((x) => {
        const t = workbenchList.find((k) => k.id === x.id);
        if (!t) return;
        results.push(t);
      });
      workbenchList.forEach((x) => {
        if (results.some((k) => k.id === x.id)) return;
        results.push(x);
      });
      dispatch(createWorkbenchList(results));
    }
  };

  const openWorkbench = (code: string) => {
    if (code === getGroupCode()) return;
    dispatch(createWorkbench(code));
    openView('/home');
    microMenus.forEach((x) => {
      dispatch(createMicroMenu(x.key, 'remove'));
      dispatch(createIframeMenu(x.key, 'remove'));
    });
    if (tabMenus.length >= config.maxCacheNum) {
      dispatch(createTabMenu(tabMenus[0].path, 'remove'));
    }
    setVisible(false);
  };

  const getLocalWbList = async (): Promise<any[] | null> => {
    try {
      return await localforage.getItem(`${getUserInfo().id}_wb_list`);
    } catch (err) {
      // ...
    }
    return null;
  };

  const setLocalWbList = async (list) => {
    dispatch(createWorkbenchList(list));
    try {
      await localforage.setItem(`${getUserInfo().id}_wb_list`, list);
    } catch {
      // ...
    }
  };

  const popupRender = () => {
    return (
      <div className={classNames('ant-dropdown-menu', 'content')}>
        <ReactSortable
          itemKey="id"
          handle=".handle"
          tag="ul"
          animation={200}
          list={workbenchList}
          setList={(list: IWorkbench[]) => {
            const fns1: string[] = list.map((x) => x.id);
            const fns2: string[] = workbenchList.map((x) => x.id);
            if (isEqual(fns1, fns2)) return;
            setLocalWbList(list.map((x) => ({ id: x.id, title: x.title, code: x.code })));
          }}
        >
          {workbenchList.map((x) => {
            return (
              <li
                key={x.id}
                className={classNames(`ant-dropdown-menu-item item`, {
                  checked: getGroupCode() === x.code,
                })}
                onClick={() => openWorkbench(x.code)}
              >
                <span className={`label`}>
                  <HolderOutlined className="handle" onClick={(ev) => ev.stopPropagation()} />
                  {x.title}
                </span>
                <i className={`icon`}>
                  <CheckOutlined />
                </i>
              </li>
            );
          })}
        </ReactSortable>
      </div>
    );
  };

  return (
    <Dropdown
      open={visible}
      dropdownRender={() => popupRender()}
      overlayClassName="workbench-cut__popper"
      placement="bottomRight"
      trigger={['click']}
      disabled={disabled}
      onOpenChange={(visible) => setVisible(visible)}
    >
      <span className={`workbench-cut icon`}>
        <MenuCutIcon />
      </span>
    </Dropdown>
  );
};

export default WorkbenchSetting;
