/*
 * @Author: 焦质晔
 * @Date: 2021-07-14 16:10:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-01-15 14:09:17
 */
.app-message-center {
  height: 100%;
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 4px;
    transition: all 0.3s ease;
    cursor: pointer;
    &:hover {
      background-color: rgba(0, 0, 0, 0.045);
    }
  }
  .icon {
    color: @textColorSecondary;
    font-size: 18px;
    cursor: pointer;
  }
}
