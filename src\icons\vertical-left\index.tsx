/*
 * @Author: 焦质晔
 * @Date: 2022-11-16 10:45:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-11-17 14:17:37
 */
import React from 'react';

const VerticalLeftIcon: React.FC = () => {
  return (
    <svg
      viewBox="0 0 24 24"
      focusable="false"
      data-icon="filter"
      width="1em"
      height="1em"
      fill="currentColor"
      aria-hidden="true"
    >
      <g
        fill="none"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="2"
      >
        <path strokeDasharray="20" strokeDashoffset="20" d="M3 3V21">
          <animate
            fill="freeze"
            attributeName="stroke-dashoffset"
            dur="0.3s"
            values="20;0"
          ></animate>
        </path>
        <path strokeDasharray="15" strokeDashoffset="15" d="M21 12H7.5">
          <animate
            fill="freeze"
            attributeName="stroke-dashoffset"
            begin="0.4s"
            dur="0.2s"
            values="15;0"
          ></animate>
        </path>
        <path strokeDasharray="12" strokeDashoffset="12" d="M7 12L14 19M7 12L14 5">
          <animate
            fill="freeze"
            attributeName="stroke-dashoffset"
            begin="0.6s"
            dur="0.2s"
            values="12;0"
          ></animate>
        </path>
      </g>
    </svg>
  );
};

export default VerticalLeftIcon;
