import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Radio,
  Slider,
  Divider,
  Alert,
  Progress,
  Upload,
  Table,
  Space,
  Button,
  InputNumber,
  message
} from 'antd';
import {
  UploadOutlined,
  SearchOutlined,
  InboxOutlined
} from '@ant-design/icons';
import {
  knowledgeCreate,
  knowledgeUpdate,
  categoryCreate,
  categoryUpdate,
  documentUpload,
  bindDocuments,
  initializeKnowledge,
  getInitializeStatus,
  getMilvusCollections
} from '../../../api/knowledge';

const { Option } = Select;
const { TextArea } = Input;
const { Dragger } = Upload;

/**
 * 类目管理对话框
 * <AUTHOR>
 */
export const CategoryModal = ({
  visible,
  onCancel,
  onSuccess,
  editingCategory,
  form
}) => {
  const [loading, setLoading] = useState(false);

  /**
   * 提交表单
   * <AUTHOR>
   */
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingCategory) {
        await categoryUpdate({ ...values, id: editingCategory.key });
        message.success('更新类目成功');
      } else {
        await categoryCreate(values);
        message.success('创建类目成功');
      }

      onSuccess();
      onCancel();
    } catch (error) {
      message.error(error.message || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={editingCategory ? '编辑类目' : '新建类目'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          name: '',
          description: ''
        }}
      >
        <Form.Item
          label="类目名称"
          name="name"
          rules={[
            { required: true, message: '请输入类目名称' },
            { max: 50, message: '类目名称不能超过50个字符' }
          ]}
        >
          <Input placeholder="请输入类目名称" />
        </Form.Item>
        
        <Form.Item
          label="类目描述"
          name="description"
          rules={[
            { max: 200, message: '描述不能超过200个字符' }
          ]}
        >
          <TextArea
            rows={3}
            placeholder="请输入类目描述"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

/**
 * 知识库管理对话框
 * <AUTHOR>
 */
export const KnowledgeModal = ({
  visible,
  onCancel,
  onSuccess,
  editingKnowledge,
  form
}) => {
  const [loading, setLoading] = useState(false);
  const [collections, setCollections] = useState([]);

  /**
   * 加载Collections列表
   * <AUTHOR>
   */
  const loadCollections = async () => {
    try {
      const response = await getMilvusCollections();
      if (response.code === 200) {
        setCollections(response.data || []);
      }
    } catch (error) {
      console.error('加载Collections失败:', error);
    }
  };

  useEffect(() => {
    if (visible) {
      loadCollections();
    }
  }, [visible]);

  /**
   * 提交表单
   * <AUTHOR>
   */
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const params = {
        ...values,
        dataType: '00', // 非结构化数据
        topK: 5,
        requeryOpen: '0',
        rerankOpen: '0'
      };

      if (editingKnowledge) {
        await knowledgeUpdate({ ...params, id: editingKnowledge.id });
        message.success('更新知识库成功');
      } else {
        await knowledgeCreate(params);
        message.success('创建知识库成功');
      }

      onSuccess();
      onCancel();
    } catch (error) {
      message.error(error.message || '操作失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={editingKnowledge ? '编辑知识库' : '创建知识库'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={800}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          name: '',
          description: '',
          dataSourceType: 'local',
          chunkStrategy: 'smart',
          maxChunkSize: 1000,
          chunkOverlap: 100,
          embeddingModel: 'text-embedding-v2',
          collectionName: '',
          similarityThreshold: 0.7
        }}
      >
        <Form.Item
          label="知识库名称"
          name="name"
          rules={[
            { required: true, message: '请输入知识库名称' },
            { max: 50, message: '名称不能超过50个字符' }
          ]}
        >
          <Input placeholder="请输入知识库名称" />
        </Form.Item>
        
        <Form.Item
          label="知识库描述"
          name="description"
          rules={[
            { max: 200, message: '描述不能超过200个字符' }
          ]}
        >
          <TextArea
            rows={3}
            placeholder="请输入知识库描述，可以描述知识库的用途、内容等"
          />
        </Form.Item>

        <Divider orientation="left">数据源配置</Divider>
        <Form.Item
          label="数据源类型"
          name="dataSourceType"
        >
          <Radio.Group>
            <Radio value="local">本地数据源</Radio>
            <Radio value="external">外部数据源</Radio>
          </Radio.Group>
        </Form.Item>

        <Divider orientation="left">切分策略配置</Divider>
        <Form.Item
          label="切分方式"
          name="chunkStrategy"
        >
          <Radio.Group>
            <Radio value="smart">智能切分</Radio>
            <Radio value="fixed">固定长度</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) =>
            prevValues.chunkStrategy !== currentValues.chunkStrategy
          }
        >
          {({ getFieldValue }) =>
            getFieldValue('chunkStrategy') === 'fixed' ? (
              <Form.Item
                label="最大切片长度"
                name="maxChunkSize"
              >
                <Slider
                  min={100}
                  max={4000}
                  step={100}
                  marks={{
                    100: '100',
                    2000: '2000',
                    4000: '4000'
                  }}
                />
              </Form.Item>
            ) : null
          }
        </Form.Item>

        <Form.Item
          label="切片重叠长度"
          name="chunkOverlap"
        >
          <Slider
            min={0}
            max={400}
            step={10}
            marks={{
              0: '0',
              200: '200',
              400: '400'
            }}
          />
        </Form.Item>

        <Divider orientation="left">向量化配置</Divider>
        <Form.Item
          label="向量化模型"
          name="embeddingModel"
          rules={[{ required: true, message: '请选择向量化模型' }]}
        >
          <Select placeholder="请选择向量化模型">
            <Option value="text-embedding-v2">text-embedding-v2</Option>
            <Option value="text-embedding-v3">text-embedding-v3</Option>
            <Option value="text-embedding-v4">text-embedding-v4</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="Collection"
          name="collectionName"
          rules={[{ required: true, message: '请选择Collection' }]}
        >
          <Select
            placeholder="请选择Collection"
            onFocus={loadCollections}
          >
            {collections.map(collection => (
              <Option key={collection} value={collection}>
                {collection}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="相似度阈值"
          name="similarityThreshold"
        >
          <Slider
            min={0.1}
            max={1.0}
            step={0.01}
            marks={{
              0.1: '0.1',
              0.5: '0.5',
              1.0: '1.0'
            }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

/**
 * 文档上传对话框
 * <AUTHOR>
 */
export const UploadModal = ({
  visible,
  onCancel,
  onSuccess,
  categories,
  knowledgeBases,
  form
}) => {
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState([]);

  /**
   * 文件上传前的检查
   * @param {File} file - 文件对象
   * @returns {boolean} 是否允许上传
   * <AUTHOR>
   */
  const beforeUpload = (file) => {
    // 文件大小检查 (50MB)
    if (file.size > 50 * 1024 * 1024) {
      message.error('文件大小不能超过 50MB');
      return false;
    }

    // 文件类型检查
    const allowedTypes = ['pdf', 'doc', 'docx', 'txt'];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
      message.error('只支持 PDF、Word、TXT 格式的文件');
      return false;
    }

    return false; // 阻止自动上传
  };

  /**
   * 文件列表变化处理
   * @param {Object} info - 文件信息
   * <AUTHOR>
   */
  const handleChange = (info) => {
    setFileList(info.fileList);
  };

  /**
   * 提交上传
   * <AUTHOR>
   */
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (fileList.length === 0) {
        message.warning('请选择要上传的文件');
        return;
      }

      setLoading(true);
      const formData = new FormData();
      formData.append('categoryId', values.categoryId);
      formData.append('ragKnowledgeId', values.ragKnowledgeId);
      formData.append('file', fileList[0].originFileObj);
      formData.append('chunkStrategy', '00'); // 智能切分
      formData.append('chunkLength', '1000'); // 默认分段长度

      await documentUpload(formData);
      message.success('上传文档成功');

      setFileList([]);
      onSuccess();
      onCancel();
    } catch (error) {
      message.error(error.message || '上传失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="上传文档"
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          label="选择类目"
          name="categoryId"
          rules={[{ required: true, message: '请选择类目' }]}
        >
          <Select placeholder="请选择类目">
            {categories.map(category => (
              <Option key={category.key} value={category.key}>
                {category.title}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="选择知识库"
          name="ragKnowledgeId"
          rules={[{ required: true, message: '请选择知识库' }]}
        >
          <Select placeholder="请选择知识库">
            {knowledgeBases.map(kb => (
              <Option key={kb.id} value={kb.id}>
                {kb.name}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="上传文件"
          required
        >
          <Dragger
            fileList={fileList}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            multiple={false}
            accept=".pdf,.doc,.docx,.txt"
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 PDF、Word、TXT 格式，单个文件不超过 50MB
            </p>
          </Dragger>
        </Form.Item>
      </Form>
    </Modal>
  );
};
