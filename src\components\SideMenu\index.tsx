/*
 * @Author: 焦质晔
 * @Date: 2022-04-21 08:43:23
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-10-25 09:35:04
 */
import React from 'react';
import classNames from 'classnames';
import { isEqual } from 'lodash-es';
import { Link, useLocation } from 'react-router-dom';
import { setStarMenu } from '@/store/actions';
import { useSelector, useDispatch } from '@/store';
import { createFlattenMenus, deepFindNav } from '@/store/reducers/app';
import { useLocale, useTool } from '@/hooks';
import { getSystem, addUrlToken, isHttpLink } from '@/utils';
import { isRedirect } from '@/router';
import { useCommonCase } from './useCommonCase';
import { getVirtualPath } from '../NavList/NavNode';
import config from '@/config';

import type { AppState, ISideMenu } from '@/store/reducers/app';

import SystemSwitch from '../SystemSwitch';
import StarMenu from '../StarMenu';
import SvgIcon from '../../layout/modules/SvgIcon';
import { QmScrollbar, Menu, Popover } from '@jiaozhiye/qm-design-react';
import { LeftOutlined, RightOutlined, MoreOutlined } from '@/icons';

import './index.less';

const getPath = (fullpath: string) => {
  return fullpath.split('?')[0];
};

const getIcon = (icon?: string) => {
  if (!icon || !isHttpLink(icon)) {
    return null;
  }
  return <SvgIcon svgUrl={icon} />;
};

const conversionPath = (path: string) => {
  if (path.startsWith('http')) {
    return path;
  }
  return `/${path}`.replace(/\/+/g, '/');
};

export const deepGetPath = (arr: ISideMenu[], attr: string, valule: string) => {
  for (let i = 0; i < arr.length; i++) {
    if (getPath(arr[i].key) === getPath(valule) && valule.startsWith(arr[i].key)) {
      return [arr[i][attr]];
    }
    if (Array.isArray(arr[i].children)) {
      const temp = deepGetPath(arr[i].children!, attr, valule);
      if (temp) {
        return [arr[i][attr], temp].flat();
      }
    }
  }
};

// 写死的功能，待优化
const QFC_BASE_SIGN = 'qfc-base-frame';

type IProps = {
  width: number;
  collapsed: boolean;
  hidden: boolean;
  onChange: (value: boolean) => void;
};

const SideMenu: React.FC<IProps> = (props) => {
  const { collapsed, hidden, width, onChange } = props;
  const { navList, menuList, sideMenus, starMenus } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { pathname, search } = useLocation();
  const { openView } = useTool();
  const { t } = useLocale();
  const { createCrumb, createCommonCase } = useCommonCase();
  const fullpath: string = pathname + search;

  const sideMenuKeys = React.useMemo<string[]>(
    () => createFlattenMenus(sideMenus).map((x) => x.key),
    [sideMenus]
  );

  const activeKey = React.useMemo<string>(() => {
    const v = sideMenuKeys.findIndex((x) => getPath(x) === pathname && fullpath.startsWith(x));
    if (v !== -1) {
      return sideMenuKeys[v];
    }
    return pathname;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fullpath, sideMenuKeys]);

  const [systemName, setSystemName] = React.useState<string>(''); // for qfc base
  const [openKeys, setOpenKeys] = React.useState<string[]>([]);
  const system = getSystem();

  const _setOpenKeys = (keys: string[]) => {
    if (isEqual(openKeys, keys)) return;
    setOpenKeys(keys);
  };

  const _getOpenKeys = (): string[] => {
    const allOpenKeys = deepGetPath(sideMenus, 'id', fullpath) || [];
    return allOpenKeys.slice(0, -1);
  };

  React.useEffect(() => {
    if (isRedirect(pathname)) return;
    _setOpenKeys(_getOpenKeys());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, sideMenus]);

  const createPop = (path: string, item: ISideMenu) => {
    const casePath = path.replace(`/${system}`, '');
    const isStar = starMenus.filter((x) => x.system === system).some((x) => x.key === casePath);
    return (
      <div className={`popup`}>
        <span
          onClick={(ev) => {
            ev.stopPropagation();
            dispatch<any>(
              setStarMenu(
                !isStar
                  ? ([
                      ...starMenus,
                      {
                        id: item.id,
                        key: casePath,
                        title: item.title,
                        system,
                        crumb: createCrumb(path),
                      },
                    ] as ISideMenu[])
                  : starMenus.filter((x) => !(x.key === casePath && x.system === system))
              )
            );
          }}
        >
          {!isStar ? t('app.sidebar.star') : t('app.sidebar.unstar')}
        </span>
      </div>
    );
  };

  const createMenuTree = (list: ISideMenu[]) => {
    return list
      .filter((x) => !x.hideInMenu)
      .map((item) => {
        const { title, icon } = item;
        const path: string = conversionPath(item.key || '');
        if (Array.isArray(item.children)) {
          return {
            key: item.id,
            popupClassName: 'ant-submenu-popup-dark',
            icon: getIcon(icon),
            label: title,
            children: createMenuTree(item.children),
          };
        }
        return {
          key: path,
          label: (
            <>
              <Link to={path} onClick={(ev) => ev.preventDefault()}>
                {title}
              </Link>
              {config.showStarNav && (
                <Popover
                  trigger="hover"
                  placement="bottom"
                  overlayClassName={`side-menu__popper`}
                  content={createPop(path, item)}
                  overlayStyle={{ paddingTop: 5 }}
                  destroyTooltipOnHide
                >
                  <MoreOutlined
                    className={classNames(`icon`)}
                    onClick={(ev) => ev.stopPropagation()}
                  />
                </Popover>
              )}
            </>
          ),
          onClick: () => {
            if (isHttpLink(item.caseHref!) && item.target === '_blank') {
              window.open(addUrlToken(item.caseHref!), '_blank');
            } else {
              const p = path.split('?');
              openView(p.length > 1 && p[0] === pathname ? `/redirect${path}` : path);
              createCommonCase(item);
            }
          },
        };
      });
  };

  // ================== Render ==================
  const showSystemCut = system.startsWith(QFC_BASE_SIGN);
  const currentNav = deepFindNav(navList, (x) => x.system === system);
  const title = showSystemCut ? systemName || currentNav?.title : currentNav?.title;
  let dashboardPath = config.isMainApp ? `/${system}/dashboard` : '/dashboard';
  if (currentNav?.virtual) {
    dashboardPath = getVirtualPath(menuList, currentNav);
  }
  const cls = {
    [`app-sider`]: true,
    opened: !collapsed,
    closed: collapsed,
  };
  return !hidden ? (
    <aside className={classNames(cls)} style={{ width, marginLeft: !collapsed ? 0 : -1 * width }}>
      <span className={`fold`} onClick={() => onChange(!collapsed)}>
        {React.createElement(collapsed ? RightOutlined : LeftOutlined, {
          className: classNames('trigger'),
        })}
      </span>
      <div className={`menu-top`}>
        <Link to={dashboardPath}>
          <h2>
            <span className={`text_overflow_cut`} title={title}>
              {title}
            </span>
            {showSystemCut && <SystemSwitch onChange={(val) => setSystemName(val)} />}
          </h2>
          <h5>{currentNav?.enDesc}</h5>
        </Link>
      </div>
      <QmScrollbar className={`menu-wrap`}>
        {config.showStarNav && <StarMenu sideMenus={sideMenus} width={width} />}
        <Menu
          key={sideMenus.length}
          mode="inline"
          inlineIndent={20}
          items={createMenuTree(sideMenus)}
          selectedKeys={[activeKey]}
          openKeys={openKeys}
          onOpenChange={(keys) => {
            setOpenKeys(keys);
          }}
        />
      </QmScrollbar>
    </aside>
  ) : null;
};

export default SideMenu;
