import React, { useState, useEffect, useCallback } from 'react';
import {
  Drawer, Progress, Select, Button, Space, Table, Checkbox,
  Pagination, Modal, message, Tag, Dropdown, Menu, Spin
} from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import {
  getAnnotationTaskDetailList,
  getKnowledgeDetail,
  markAnnotation,
  unlockAnnotation,
  completeAnnotationTask,
  robotlist // 假设需要机器人列表来显示名称
} from '@/modules/dataoperations/api/taskAnnotation';
import FAQDetailModal from '../../components/FAQDetailModal';
import styles from './index.module.less';

// --- 映射关系 (保持不变) ---
const annotationTypeMap = {
  '正确': 'correct',
  '错误': 'error',
  '未覆盖': 'uncovered',
  '无效': 'invalid',
  '待定': 'pending',
};

const annotationSubtypeMap = {
  '错误-暂不处理': 'error_no_handle',
  '错误-修改知识': 'error_modify_knowledge',
  '错误-已处理': 'error_handled',
  '未覆盖-暂不处理': 'uncovered_no_handle',
  '未覆盖-新增知识': 'uncovered_add_knowledge',
  '未覆盖-已处理': 'uncovered_handled',
};

const getEnumKeyByValue = (obj, value) => Object.keys(obj).find(key => obj[key] === value);

const TaskDetailDrawer = ({ visible, onClose, taskData, onTaskComplete }) => {
  const [loading, setLoading] = useState(false);
  const [detailItems, setDetailItems] = useState([]);
  // 优化点 1: 将 total 从 pagination 中分离
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20 });
  const [total, setTotal] = useState(0);
  const [filters, setFilters] = useState({ annotationType: 'all', isLocked: 'all' });
  const [robotName, setRobotName] = useState('');

  const [isFaqModalVisible, setIsFaqModalVisible] = useState(false);
  const [currentFaqData, setCurrentFaqData] = useState(null);

  // 获取机器人名称的逻辑保持不变
  useEffect(() => {
    if (visible && taskData) {
      robotlist({ pageSize: 99999, pageNum: 1 }).then(res => {
        const robot = res.data.records.find(r => r.id === taskData.robotId);
        if (robot) setRobotName(robot.robotName);
      })
    }
  }, [visible, taskData]);

  // 优化点 2: 修正 useCallback 的依赖，打破循环
  const fetchDetails = useCallback(async (pageInfo, filterInfo) => {
    if (!taskData) return;
    setLoading(true);
    try {
      const params = {
        annotationTaskId: taskData.id,
        pageNum: pageInfo.current,
        pageSize: pageInfo.pageSize,
        annotationType: filterInfo.annotationType === 'all' ? null : filterInfo.annotationType,
        isLocked: filterInfo.isLocked === 'all' ? null : (filterInfo.isLocked === 'true'),
      };
      const res = await getAnnotationTaskDetailList(params);
      if (res.data && res.data.list) {
        setDetailItems(res.data.list);
        setTotal(res.data.total); // 单独更新 total
      } else {
        // 当 res.data 不存在或 list 不存在时，也应该清空列表和总数
        setDetailItems([]);
        setTotal(0);
      }
    } catch (error) {
      message.error('获取任务详情列表失败');
    } finally {
      setLoading(false);
    }
  }, [taskData]); // 依赖项只保留 taskData，因为它是从外部传入且决定了请求的主体

  // 优化点 3: 使用 useEffect 进行首次加载和重置
  useEffect(() => {
    if (visible) {
      // 当抽屉打开时，重置筛选和分页，并加载第一页数据
      const initialFilters = { annotationType: 'all', isLocked: 'all' };
      const initialPagination = { current: 1, pageSize: 20 };
      setFilters(initialFilters);
      setPagination(initialPagination);
      fetchDetails(initialPagination, initialFilters);
    }
  }, [visible, taskData, fetchDetails]); // 依赖 visible, taskData 和 fetchDetails

  // 优化点 4: 显式地在事件处理器中调用 fetch
  const handleTableChange = (current, pageSize) => {
    const newPagination = { ...pagination, current, pageSize };
    setPagination(newPagination);
    fetchDetails(newPagination, filters);
  }

  const handleFilterSearch = () => {
    // 查询时，页码重置为 1
    const newPagination = { ...pagination, current: 1 };
    setPagination(newPagination);
    fetchDetails(newPagination, filters);
  }

  const handleFilterReset = () => {
    const newFilters = { annotationType: 'all', isLocked: 'all' };
    const newPagination = { ...pagination, current: 1 };
    setFilters(newFilters);
    setPagination(newPagination);
    fetchDetails(newPagination, newFilters);
  }

  const handleShowFaqModal = async (knowledgeId, env) => {
    // ... 此处逻辑基本不变，可以保持原样
    if (!knowledgeId || !env) {
      message.warn('知识ID或环境信息不存在');
      return;
    }
    try {
      const res = await getKnowledgeDetail(knowledgeId, env);
      if (res.data) {
        const faqData = {
          category: res.data.categoryName,
          title: res.data.knowledge.question,
          effectiveDate: res.data.knowledge.effectiveEndTime == null ? '永久生效' : `${res.data.knowledge.effectiveStartTime || 'N/A'} ~ ${res.data.knowledge.effectiveEndTime || 'N/A'}`,
          similarQuestions: res.data.similarQuestions,
          answer: {
            perspective: '默认',
            text: res.data.knowledge.answer,
          },
          answerType: res.data.knowledge.answerType,
        };
        setCurrentFaqData(faqData);
        setIsFaqModalVisible(true);
      } else {
        message.error('获取FAQ详情失败');
      }
    } catch (error) {
      message.error('获取FAQ详情失败');
    }
  };

  // 标注、解锁、完成任务的逻辑也可以优化，确保刷新时使用当前状态
  const refreshCurrentPage = () => {
    fetchDetails(pagination, filters);
  };

  const handleAnnotation = async (detailId, type, subType) => {
    try {
      const params = [{
        detailId: detailId,
        annotationType: annotationTypeMap[type],
        annotationSubtype: subType ? (annotationSubtypeMap[subType] || subType) : type
      }];
      await markAnnotation(params);
      message.success('标注成功');
      refreshCurrentPage(); // 刷新当前页
    } catch (error) {
      message.error('标注失败');
    }
  };

  const handleReannotate = async (detailId) => {
    try {
      await unlockAnnotation(detailId);
      message.success('已解锁，可以重新标注');
      refreshCurrentPage(); // 刷新当前页
    } catch (error) {
      message.error('解锁失败');
    }
  };

  const handleCompleteTask = () => {
    Modal.confirm({
      title: '确认完成标注任务吗？',
      icon: <ExclamationCircleOutlined />,
      content: '完成后任务状态将变为“已完成”，并不可再进行标注。',
      okText: '确认', cancelText: '取消',
      onOk: async () => {
        try {
          const res = await completeAnnotationTask(taskData.id);
          if (res.code == 200) {
            onTaskComplete(taskData.id); // 通知父组件
            onClose(); // 关闭抽屉
            message.success('任务已完成');
          }
        } catch (error) {
          message.error('操作失败');
        }
      },
    });
  };

  // --- Render 相关的函数 (基本保持不变) ---
  const renderAction = (_, record) => {
    if (record.isLocked) {
      const typeDisplay = getEnumKeyByValue(annotationTypeMap, record.annotationType);
      const subTypeDisplay = getEnumKeyByValue(annotationSubtypeMap, record.annotationSubtype) || record.annotationSubtype;

      let color = 'default';
      if (typeDisplay === '正确') color = 'success';
      if (typeDisplay === '错误') color = 'error';
      if (typeDisplay === '未覆盖') color = 'warning';
      if (typeDisplay === '待定') color = 'processing';

      return (
        <Space>
          <Tag color={color} className={styles.annotationTag}>{subTypeDisplay}</Tag>
          <Button type="link" onClick={() => handleReannotate(record.detailId)} className={styles.reannotateLink}>重新标注</Button>
        </Space>
      );
    }

    const errorMenu = {
      items: [
        {
          label: '暂不处理',
          key: '错误-暂不处理',
        },
        {
          label: '修改知识',
          key: '错误-修改知识',
        },
        {
          label: '已处理',
          key: '错误-已处理'
        }
      ],
      onClick: ({ key }) => handleAnnotation(record.detailId, '错误', key)
    }
    const uncovMenu = {
      items: [
        {
          label: '暂不处理',
          key: '未覆盖-暂不处理',
        },
        {
          label: '新增知识',
          key: '未覆盖-新增知识',
        },
        {
          label: '已处理',
          key: '未覆盖-已处理'
        }
      ],
      onClick: ({ key }) => handleAnnotation(record.detailId, '未覆盖', key)
    }


    return (
      <Space className={styles.actionColumn}>
        <Button type="link" onClick={() => handleAnnotation(record.detailId, '正确', '正确')}>正确</Button>
        <Dropdown menu={errorMenu} trigger={['click']}><Button type="link">错误</Button></Dropdown>
        <Dropdown menu={uncovMenu} trigger={['click']}><Button type="link">未覆盖</Button></Dropdown>
        <Button type="link" onClick={() => handleAnnotation(record.detailId, '无效', '无效')}>无效</Button>
        <Button type="link" onClick={() => handleAnnotation(record.detailId, '待定', '待定')}>待定</Button>
      </Space>
    );
  };

  const renderMatchType = (text) => {
    if (text === 'clarify') return <span className={`${styles.matchTypeTag} ${styles.clarify}`}>澄清</span>;
    if (text === 'no_answer') return <span className={`${styles.matchTypeTag} ${styles.no_answer}`}>无答案</span>;
    if (text === 'has_answer') return <span className={`${styles.matchTypeTag} ${styles.clarify}`}>有答案</span>;
    return text;
  };

  const detailColumns = [
    { title: '用户问法', dataIndex: 'userQuestion', key: 'userQuestion', width: 250 },
    { title: '匹配类型', dataIndex: 'matchType', key: 'matchType', width: 100, render: renderMatchType },
    {
      title: '匹配明细',
      dataIndex: 'faqTitle',
      key: 'faqTitle',
      render: (text, record) => record.faqTitle ? (
        <div className={styles.matchDetail}>
          <div>{record.faqTitle}</div>
          {record.knowledgeId && <a onClick={() => handleShowFaqModal(record.knowledgeId, record.environment || 'prod')} className={styles.moreLink}>更多</a>}
        </div>
      ) : '--',
    },
    { title: '操作', key: 'action', width: 350, fixed: 'right', render: renderAction },
  ];

  if (!taskData) return null;

  return (
    <>
      <Drawer
        title="任务详情"
        placement="right"
        onClose={onClose}
        open={visible}
        width="90%"
        destroyOnClose
        footer={
          <div className={styles.drawerFooter}>
            <Button type="primary" onClick={handleCompleteTask} disabled={taskData.status === 'completed'}>完成并结束标注任务</Button>
          </div>
        }
      >
        <Spin spinning={loading}>
          <div style={{ padding: '0 0 24px 0' }}>
            <div className={styles.infoCardGrid}>
              <div className={styles.infoCard}><div className={styles.cardLabel}>任务名称</div><div className={styles.cardValue}>{taskData.taskName}</div></div>
              <div className={styles.infoCard}><div className={styles.cardLabel}>机器人</div><div className={styles.cardValue}>{robotName || taskData.robotId}</div></div>
              <div className={styles.infoCard}><div className={styles.cardLabel}>标注情况</div><div className={styles.cardValue}>已标注 {taskData.annotatedCount}</div></div>
              <div className={styles.infoCard}><div className={styles.cardLabel}>标注总量</div><div className={styles.cardValue}>{taskData.totalCount}</div></div>
            </div>
            <div className={styles.filterBar}>
              <Space>
                <Select
                  value={filters.isLocked}
                  onChange={(value) => setFilters(f => ({ ...f, isLocked: value }))}
                  style={{ width: 120 }}
                >
                  <Select.Option value="all">全部状态</Select.Option>
                  <Select.Option value="false">未锁定</Select.Option>
                  <Select.Option value="true">已锁定</Select.Option>
                </Select>
                <Select
                  value={filters.annotationType}
                  onChange={(value) => setFilters(f => ({ ...f, annotationType: value }))}
                  style={{ width: 120 }}
                >
                  <Select.Option value="all">全部标注</Select.Option>
                  <Select.Option value="correct">正确</Select.Option>
                  <Select.Option value="error">错误</Select.Option>
                  <Select.Option value="uncovered">未覆盖</Select.Option>
                  <Select.Option value="invalid">无效</Select.Option>
                  <Select.Option value="pending">待定</Select.Option>
                </Select>
                <Button onClick={handleFilterReset}>重置</Button>
                <Button type="primary" onClick={handleFilterSearch}>查询</Button>
              </Space>
            </div>
            <Table
              columns={detailColumns}
              dataSource={detailItems}
              pagination={false} // 使用自定义的分页组件
              rowKey="detailId"
              rowSelection={{ type: 'checkbox' }}
              scroll={{ x: 1000 }}
            />
            <div className={styles.pagination}>
              <Pagination
                current={pagination.current}
                pageSize={pagination.pageSize}
                total={total} // 使用分离出的 total 状态
                showSizeChanger
                showQuickJumper
                showTotal={(t) => `共 ${t} 条数据`}
                onChange={handleTableChange} // 直接使用 onChange
              />
            </div>
          </div>
        </Spin>
      </Drawer>

      <FAQDetailModal
        visible={isFaqModalVisible}
        onClose={() => setIsFaqModalVisible(false)}
        faqData={currentFaqData}
      />
    </>
  );
};

export default TaskDetailDrawer;