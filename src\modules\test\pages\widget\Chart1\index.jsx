/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 14:08:41
 */
import React from 'react';
import { Widget } from '@/components';

import V1 from '@framework/pages/dashboard/charts/Chart1';
import FullView from './FullView';

import { getTableData } from '@test/api/demo';

/*
 * Widget 基类组件参数
 * cols: 磁贴组件所占的栅格数（总共6列栅格）
 * rows: 磁贴组件所占的行数（每行高度75px）
 * fullScreen: 配置全屏页面（不是必要参数）
 * Widget 实例方法
 * attachEvent: 事件监听，用于接收事件的动作，参数 (data) => void
 * dispatch: 事件派发，用于发送事件，参数 { code: 'widget组件编号', payload: any }
 * 说明：关于磁贴组件的开发，宽度需要满足响应式特性，高度是固定的（根据rows参数自动计算）
 */
const Chart1 = () => {
  const widgetRef = React.useRef(null);

  const customEventHandler = (data) => {
    if (data.code === 'DE-0505_APP_BUC__002') {
      console.log('Chart1 接收到了 Chart2 发送的事件: ', data);
    }
  };

  React.useEffect(() => {
    widgetRef.current.attachEvent(customEventHandler);
  }, []);

  return (
    <Widget
      ref={widgetRef}
      cols={2}
      rows={4}
      fullScreen={{
        title: '标题名称',
        content: <FullView />,
      }}
    >
      <V1 fetch={{ api: getTableData }} style={{ height: '100%' }} />
    </Widget>
  );
};

export default Chart1;
