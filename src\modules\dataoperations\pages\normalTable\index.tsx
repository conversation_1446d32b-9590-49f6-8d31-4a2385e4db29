import React, {forwardRef, ReactNode, useEffect, useImperative<PERSON>andle, useState} from "react";
import css from "./index.module.less";
import {QmTable} from "@jiaozhiye/qm-design-react";
import {message, Pagination} from "antd";
import {IColumn} from "@jiaozhiye/qm-design-react/lib/table/src/table/types";
import {useUpdateEffect} from "@/hooks";

interface AxiosResponse {
  code: number,
  data: any,
  message: string
}

interface TableProps {
  columns:  IColumn[],
  listApi: (param: object) => Promise<AxiosResponse>,
  param: object,
  ref: React.MutableRefObject<null>,
  children?: ReactNode,
  data?: string,
  totalName?: string,
  tableProps?: any
}

export interface TableRef {
  onSearch: () => void,
  tableData: [],
  clear: () => void,
}

export const NormalTable = forwardRef((props: TableProps, ref) => {
  NormalTable.displayName = 'NormalTable'

  const [tableData, setTableData] = useState([])
  const [columns, setColumns] = useState(props.columns)

  const [pageInfo, setPageInfo] = useState({
    pageNum: 1,
    pageSize: 20
  })
  const [total, setTotal] = useState(0)

  const pageChange = async (pageNum: number, pageSize: number ) => {
    setPageInfo({
      pageNum: pageNum,
      pageSize: pageSize
    })
  }

  const search = async () => {
    console.log(props.param)
    setTableData([])
    const res = await props.listApi({
      ...props.param,
      ...pageInfo
    })
    if (res.code === 200) {
      setTotal(res.data[props.totalName || 'totalCount'])
      setTableData(res.data[props.data ||'list'] || [])
    } else {
      message.error(res.message)
    }
  }

  const clear = () => {
    setTableData([])
    setPageInfo({
      pageNum: 1,
      pageSize: 20
    })
    setTotal(0)
  }

  const onSearch = () => {
    setPageInfo({
      pageNum: 1,
      pageSize: 20
    })
  }
  
  useEffect(() => {
    search().then()
  }, []);

  useUpdateEffect(() => {
    search().then(r => {
    })
  }, [pageInfo])
  
  useUpdateEffect(() => {
    setColumns(props.columns)
  }, [props.columns])

  useImperativeHandle(ref, () => ({
    onSearch,
    tableData,
    clear
  }))

  return (
    <>
      <QmTable columns={columns} rowKey={(row, index) => row.id}
               dataSource={tableData} maxHeight={400} columnsChange={(col: any) => {setColumns(col)}}
               paginationConfig={{total: total}} {...props.tableProps} infoBarConfig={{hideAlert: true}}
      >
        {props.children}
      </QmTable>
      <div className={css.Pagination}>
        <Pagination showQuickJumper current={pageInfo.pageNum} pageSize={pageInfo.pageSize} total={total}
                    onChange={pageChange} showSizeChanger={true} showTotal={(total) => `共 ${total} 条`}/>
      </div>
    </>
  );
}) ;
export default NormalTable
