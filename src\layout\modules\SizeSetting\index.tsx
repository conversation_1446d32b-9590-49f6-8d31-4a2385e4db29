/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-30 14:40:58
 */
import React from 'react';
import classNames from 'classnames';
import { useSelector, useDispatch } from '@/store';
import { createComponentSize } from '@/store/actions';
import { useApplication, useLocale } from '@/hooks';
import { emitter as microEvent } from '@/utils/mitt';
import { COMP_SIZE } from '@/store/types';
import config from '@/config';

import type { AppState } from '@/store/reducers/app';
import type { ComponentSize } from '@/utils/types';

import { Menu, Dropdown } from '@jiaozhiye/qm-design-react';
import { FontSizeOutlined } from '@/icons';

import './index.less';

const SizeSetting: React.FC = () => {
  const { iframeMenus, size } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { t } = useLocale();
  const { getFrameByName } = useApplication();

  const sizeChangeHandle = (size: ComponentSize) => {
    dispatch(createComponentSize(size));
    localStorage.setItem('size', size);
    iframeMenus.forEach((x) => {
      const $iframe = getFrameByName(x.key) as HTMLIFrameElement;
      if (!$iframe) return;
      $iframe.contentWindow?.postMessage({ type: COMP_SIZE, data: size }, config.postOrigin);
    });
    // 延迟 - 重要
    setTimeout(() => microEvent.$emit(COMP_SIZE, size));
  };

  const popupRender = () => {
    const items = [
      {
        key: 'large',
        label: t('app.sizeSelect.large'),
        disabled: size === 'large',
        onClick: () => sizeChangeHandle('large'),
      },
      {
        key: 'middle',
        label: t('app.sizeSelect.middle'),
        disabled: size === 'middle',
        onClick: () => sizeChangeHandle('middle'),
      },
      {
        key: 'small',
        label: t('app.sizeSelect.small'),
        disabled: size === 'small',
        onClick: () => sizeChangeHandle('small'),
      },
    ];
    return <Menu items={items} />;
  };

  return (
    <div className={classNames('app-size-setting')}>
      <Dropdown trigger={['click']} placement="bottomRight" dropdownRender={() => popupRender()}>
        <span>
          <FontSizeOutlined className={`icon`} />
        </span>
      </Dropdown>
    </div>
  );
};

export default SizeSetting;
