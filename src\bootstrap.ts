/*
 * @Author: 焦质晔
 * @Date: 2021-02-05 09:13:33
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-08 23:35:52
 */
import React from 'react';
import ReactDOM from 'react-dom/client';
import { pick } from 'lodash-es';
import '@/locale/setting';
import {
  getUrlToken,
  getWorkbenchId,
  setGlobalContext,
  queryFormat,
  destroyAlert,
  renameFields,
} from '@/utils';
import { getToken, setToken, getUserInfo, setUserInfo } from '@/utils/cookies';
import { setMicroEvent } from '@/utils/mitt';
import { changeLocale } from '@/locale';
import { createLocaleLang, createSiteInfo } from '@/store/actions';
import store from '@/store';
import config from '@/config';
import App from './App';

import type { Language } from '@/utils/types';

let root: ReactDOM.Root | null = null;

function render(props) {
  const { container } = props || {};
  const rootElement = container ? container.querySelector('#app') : document.querySelector('#app');
  if (!root && rootElement) {
    root = ReactDOM.createRoot(rootElement);
  }
  root?.render(React.createElement(App));
}

function initial() {
  const params = queryFormat(window.location.search);
  if (!config.isMainApp) {
    const token = getUrlToken();
    if (token && token !== getToken()) {
      setToken(token);
      const data = pick(params, ['userId', 'userName', 'loginName', 'systemId', 'tenantId']);
      setUserInfo(
        Object.assign(
          {},
          getUserInfo(),
          Object.fromEntries(
            Object.entries(renameFields(data, { userId: 'id', userName: 'name' })).map(
              ([key, val]) => [key, decodeURIComponent(val as string)]
            )
          )
        )
      );
    }
    if (params.lang) {
      changeLocale(params.lang as Language);
      store.dispatch(createLocaleLang(params.lang));
    }
    setGlobalContext({
      systemId: params.systemId || getWorkbenchId(),
      appCode: params.appCode || config.code,
    });
    // 业务自定义上下文参数 ...
  }
  if (params.microEnv) {
    window.__MAIM_APP_ENV__ = true;
  }
}

function initialMicro(props) {
  const { microEvent, isMainEnv, isWidget, pathRoute, extraProps, siteInfo } = props || {};
  // Widget 挂件
  if (isWidget) {
    // eslint-disable-next-line @typescript-eslint/no-var-requires
    const routes = require('@/router/config').default;
    const route = routes.find((x) => x.path === pathRoute);
    if (route) {
      route.path = '/';
      route.exact = false;
      extraProps && (route.props = Object.assign({}, route.props, extraProps));
    }
  }
  setMicroEvent(microEvent);
  // 全局上下文变量
  if (extraProps) {
    const { systemId, appCode } = queryFormat(window.location.search);
    if (!systemId && extraProps.systemId) {
      setGlobalContext({ systemId: extraProps.systemId });
    }
    if (!appCode && extraProps.appCode) {
      setGlobalContext({ appCode: extraProps.appCode });
    }
    // 业务自定义上下文参数 ...
  }
  if (siteInfo) {
    store.dispatch(createSiteInfo(siteInfo));
  }
  // for BPM (工作流)
  if (window.__MICRO_APP_NAME__.startsWith('BPMEngine') && extraProps) {
    const { taskId = '', processInstanceId = '', businessKey = '' } = extraProps;
    setGlobalContext({ taskId, processInstanceId, businessKey });
  }
  window.__MAIM_APP_ENV__ = isMainEnv ?? true;
}

function doMount() {
  if (config.powerByMicro) {
    // micro-app
    if (window.__MICRO_APP_ENVIRONMENT__) {
      __webpack_public_path__ = window.__MICRO_APP_PUBLIC_PATH__;
      Object.assign(window, { mount, unmount });
    }
  } else {
    render({});
  }
}

initial();

doMount();

export async function bootstrap() {}

export async function mount(props) {
  window.__MICRO_APP_ENVIRONMENT__ && initialMicro(window.microApp.getData());
  render(props);
}

export async function unmount(props) {
  destroyAlert();
  if (root) {
    root.unmount();
    root = null;
  }
}
