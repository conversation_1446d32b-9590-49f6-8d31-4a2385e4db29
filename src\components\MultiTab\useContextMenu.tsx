/*
 * @Author: 焦质晔
 * @Date: 2024-12-11 14:51:05
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-12-21 09:26:12
 */
import React from 'react';
import { useEvent, useClickAway } from '@/hooks';
import { nextTick } from '@/utils';

import Portal from '@rc-component/portal';

import type { Nullable } from '@/utils/types';

type UseContextMenuProps = {
  ref: React.RefObject<HTMLElement>;
  popup: (ev: MouseEvent) => React.ReactNode;
  getTriggerContainer?: (el: HTMLElement | null) => HTMLElement | null;
  enabled?: boolean;
};

const useContextMenu = ({
  ref,
  popup,
  getTriggerContainer,
  enabled = true,
}: UseContextMenuProps) => {
  const ctxMenuRef = React.useRef<HTMLDivElement>(null);
  const menuEventRef = React.useRef<Nullable<MouseEvent>>(null);

  const setMenuEvent = (value) => {
    menuEventRef.current = value;
  };

  const [open, setOpen] = React.useState<boolean>(false);
  const [pos, setPos] = React.useState<{ x: number; y: number } | null>(null);

  const defaultSize = { width: 150, height: 200, zIndex: 9998 };

  useClickAway({
    ref: ctxMenuRef,
    handler: () => closeMenu(),
  });

  const closeMenu = () => {
    setOpen(false);
    setPos(null);
    setMenuEvent(null);
  };

  const contextMenuHandler = useEvent((ev: MouseEvent) => {
    ev.preventDefault();

    setMenuEvent(ev);
    setOpen(true);

    nextTick(() => {
      const { width, height } = ctxMenuRef.current?.getBoundingClientRect() || defaultSize;
      const menuWidth = width;
      const menuHeight = height;
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;

      let x = ev.clientX;
      let y = ev.clientY;

      if (x + menuWidth > windowWidth) {
        x = Math.max(x - menuWidth, 0);
      }
      if (y + menuHeight > windowHeight) {
        y = Math.max(y - menuHeight, 0);
      }

      setPos({ x, y });
    });
  });

  React.useEffect(() => {
    const element = getTriggerContainer?.(ref.current) || ref.current;
    if (enabled && element) {
      element.addEventListener('contextmenu', contextMenuHandler);
    }
    return () => {
      if (element) {
        element.removeEventListener('contextmenu', contextMenuHandler);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ref]);

  const stys: React.CSSProperties = {
    position: 'fixed',
    top: `${pos?.y || 0}px`,
    left: `${pos?.x || 0}px`,
    zIndex: defaultSize.zIndex,
  };

  const contextMenu = (
    <Portal open={open}>
      <div ref={ctxMenuRef} className={`multi-tab__context-menu`} style={stys}>
        {menuEventRef.current ? popup(menuEventRef.current) : null}
      </div>
    </Portal>
  );

  return { contextMenu, closeMenu };
};

export default useContextMenu;
