import axios from '@/api/fetch';

// ----------机器人相关接口------------
// 机器人列表
export const robotlist = (params) => {
  return axios.post('/aio/faq-set/robot-list', params);
};

// 新建或编辑机器人
export const robotsaveOrUpdate = (params) => {
  return axios.post('/aio/faq-set/robot-saveOrUpdate', params);
};

// 发布机器人
export const robotpublish = (params) => {
  return axios.get('/aio/faq-set/robot-publish', { params });
};

// 删除机器人
export const robotdelete = (id) => {
  return axios.get(`/aio/faq-set/robot-delete/${id}`);
};

// 获取机器人详情
export const robotdetail = (params) => {
  return axios.get(`/aio/faq-set/robot-detail/${params.id}/${params.env}`);
};

// ----------知识相关接口------------
// 知识列表
export const knowledgepage = (params) => {
  return axios.post('/aio/faq-set/knowledge-page', params);
};

// 创建知识
export const knowledgecreate = (params) => {
  return axios.post('/aio/faq-set/knowledge-create', params);
};

// 更新知识
export const knowledgeupdate = (params) => {
  return axios.post('/aio/faq-set/knowledge-update', params);
};

// 删除知识
export const knowledgedelete = (id) => {
  return axios.get(`/aio/faq-set/knowledge-delete/${id}`);
};

// 获取知识详情
export const knowledgedetail = (params) => {
  return axios.get(`/aio/faq-set/knowledge-detail/${params.id}/${params.env}`);
};

// 生效配置
export const knowledgeEffectiveSet = (params) => {
  return axios.post(`/aio/faq-set/knowledge-effective-set`, params);
};

// 生效配置
export const knowledgetransfer = (params) => {
  return axios.post(`/aio/faq-set/knowledge-transfer`, params);
};

// 发布知识
export const knowledgepublish = (params) => {
  return axios.get(`/aio/faq-set/knowledge-publish`, { params });
};

// ----------类目相关接口------------
// 类目树形结构
export const categorytree = (params) => {
  return axios.get(`/aio/faq-set/category-tree/${params.env}`);
};

// 创建类目
export const categorycreate = (params) => {
  return axios.post('/aio/faq-set/category-create', params);
};

// 编辑类目
export const categoryupdate = (params) => {
  return axios.post('/aio/faq-set/category-update', params);
};

// 删除类目
export const categorydelete = (id) => {
  return axios.get(`/aio/faq-set/category-delete/${id}`);
};

/**
 * 机器人聊天
 */
export const faqChat = (params) => {
  return axios.post('/aio/faq-core/chat', params);
}
/**
 * 查询运营报表
 * 
 */
export const reportQuery = (params) => {
  return axios.post('/aio/faq-set/report-query', params);
}

/**
 * 查询知识列表
 */
export const getKknowledgeList = (params) => {
  return axios.post('/aio/faq-set/knowledge-list', params);
}