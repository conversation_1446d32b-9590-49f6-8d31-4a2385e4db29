import React, { useState, useEffect } from 'react';
import { Button, Space, Input, Form, Tree, Popconfirm, message } from 'antd';
import { PlusOutlined, DeleteOutlined, EditOutlined, CloseOutlined, CheckOutlined } from '@/icons';
import { categorycreate, categoryupdate, categorydelete } from '@/modules/dataoperations/api/robot';
import css from './index.module.less';
import { useForm } from 'antd/lib/form/Form';

const { Search } = Input;

const Category = ({ data, setUpdateTreeData }) => {
  const [form] = useForm();
  const [treeData, setTreeData] = useState([]);
  const [treeNodeTitle, setTreeNodeTitle] = useState('');
  const [firstLevel, setFirstLevel] = useState(false);
  const [inputName, setInputName] = useState('');
  const [parentId, setParentId] = useState('');
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [expandedKeys, setExpandedKeys] = useState([]);
  // 获取初始树数据
  useEffect(() => {
    setTreeData(data);
  }, [data]);

  // 搜索
  const onSearch = (value) => {
    console.log('value :', value);
    setSearchValue(value);
    // 获取匹配的节点路径
    const expandedKeysSet = new Set();
    const getExpandedKeys = (data, value) => {
      data.forEach((item) => {
        if (item.title.toLowerCase().includes(value.toLowerCase())) {
          expandedKeysSet.add(item.key);
        }
        if (item.children) {
          getExpandedKeys(item.children, value);
        }
      });
    };

    getExpandedKeys(treeData, value);
    setExpandedKeys([...expandedKeysSet]);
    setAutoExpandParent(true);
  };

  // 展开/收起节点时的回调
  const onExpand = (keys) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };

  // 树里面属性处理逻辑
  const loopFuc = (type, arr, val, fieldname, fieldValue, customName, customValue) => {
    for (let i = 0; i < arr.length; i++) {
      if (type == 'delete') {
        let index = arr.findIndex((item) => item.key == val);
        console.log('index :', index);
        if (index !== -1) {
          arr.splice(index, 1);
        }
      } else {
        arr[i][fieldname] = false;
        if (arr[i].key == val) {
          arr[i][fieldname] = fieldValue;
          if (customName) {
            arr[i][customName] = customValue;
          }
        }
      }
      if (arr[i]?.children) {
        loopFuc(type, arr[i].children, val, fieldname, fieldValue, customName, customValue);
      }
    }
    updateTreeData();
  };

  // 选中当前树节点
  const onSelect = (selectedKeysValue, info) => {
    loopFuc('edit', treeData, info.node.key, 'operation', true);
  };

  // 渲染树节点
  const handleReanderTreeNode = (val) => {
    return (
      <div className={css.treeNode}>
        <div>
          {val.isEdit ? (
            <div className={css.editBox}>
              <Input
                style={{ height: '20px' }}
                placeholder="请输入（不超过120字）"
                value={treeNodeTitle}
                onChange={(e) => setTreeNodeTitle(e.target.value)}
              />
              <CloseOutlined
                style={{ fontSize: '12px' }}
                onClick={(event) => {
                  event.stopPropagation();
                  loopFuc('eidt', treeData, val.key, 'isEdit', false);
                }}
              />
              <CheckOutlined
                style={{ fontSize: '12px' }}
                onClick={async (event) => {
                  event.stopPropagation();
                  loopFuc('eidt', treeData, val.key, 'isEdit', false, 'title', treeNodeTitle);
                  let obj = {
                    id: val.id,
                    name: treeNodeTitle,
                  };
                  let res = await categoryupdate(obj);
                  if (res.code != 200) return;
                  message.success('操作成功');
                  setUpdateTreeData();
                }}
              />
            </div>
          ) : (
            <div>{val.title}</div>
          )}
        </div>
        {val.operation && (
          <div>
            <Button
              type="link"
              icon={<PlusOutlined />}
              size="small"
              onClick={() => addCategory('01', val.parentId)}
            >
              平级
            </Button>
            <Button
              type="link"
              icon={<PlusOutlined />}
              size="small"
              onClick={() => addCategory('02', val.id)}
            >
              下级
            </Button>
            <Button
              type="link"
              icon={<EditOutlined />}
              size="small"
              onClick={() => {
                setTreeNodeTitle('');
                loopFuc('edit', treeData, val.key, 'isEdit', true);
              }}
            />
            <Popconfirm
              title="是否确认删除?"
              onConfirm={async () => {
                loopFuc('delete', treeData, val.key);
                let res = await categorydelete(val.id);
                if (res.code != 200) return;
                message.success('删除成功');
                setUpdateTreeData();
              }}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<DeleteOutlined />} size="small" />
            </Popconfirm>
          </div>
        )}
      </div>
    );
  };

  // 新增一级，平级，下级方法
  const addCategory = async (type, pid) => {
    form.setFieldsValue('categoryName', '');
    setFirstLevel(true);
    setInputName(type);
    setParentId(pid);
  };

  // 更新树结构参数
  const updateTreeData = () => {
    setTreeData(treeData.map((item) => item));
  };

  return (
    <>
      <Space style={{ marginBottom: '20px' }}>
        <Button type="primary" onClick={() => addCategory('00')}>
          新建一级类目
        </Button>
        <Search placeholder="请搜索" allowClear onSearch={onSearch} style={{ width: 320 }} />
      </Space>
      {firstLevel && (
        <div className={css.formBox}>
          <Form form={form} layout="vertical" style={{ width: '80%' }}>
            <Form.Item
              name="categoryName"
              label={`${inputName == '00' ? '一级' : inputName == '01' ? '平级' : '下级'}类目名称`}
              rules={[{ required: true }]}
            >
              <Input placeholder="请输入（不超过120字）" length={120} />
            </Form.Item>
          </Form>
          <Space style={{ marginTop: '6px' }}>
            <Button
              onClick={() => {
                setFirstLevel(false);
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                form
                  .validateFields()
                  .then(async (values) => {
                    let obj = {
                      name: values.categoryName,
                      parentId: inputName == '00' ? null : parentId,
                      type: inputName,
                    };
                    let res = await categorycreate(obj);
                    if (res.code != 200) return;
                    message.success('操作成功');
                    setUpdateTreeData();
                  })
                  .catch((errorInfo) => {});
              }}
            >
              确定
            </Button>
          </Space>
        </div>
      )}
      <div className={css.treeBox}>
        <Tree
          style={{ padding: '10px' }}
          height={300}
          onSelect={onSelect}
          treeData={treeData}
          blockNode
          titleRender={handleReanderTreeNode}
          expandedKeys={expandedKeys}
          autoExpandParent={autoExpandParent}
          onExpand={onExpand}
          filterTreeNode={(node) => node.title.toLowerCase().includes(searchValue.toLowerCase())}
        />
      </div>
    </>
  );
};

export default Category;
