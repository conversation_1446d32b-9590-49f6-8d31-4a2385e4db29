/**
 * TinyMCE version 6.1.2 (2022-07-29)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=l=e,(n=String).prototype.isPrototypeOf(o)||(null===(r=l.constructor)||void 0===r?void 0:r.name)===n.name)?"string":t;var o,l,n,r})(t)===e,o=e=>t=>typeof t===e,l=t("string"),n=t("array"),r=o("boolean"),a=(void 0,e=>undefined===e);const s=e=>!(e=>null==e)(e),c=o("function"),i=o("number"),m=()=>{},d=e=>()=>e,u=e=>e,p=(e,t)=>e===t;function b(e,...t){return(...o)=>{const l=t.concat(o);return e.apply(null,l)}}const g=d(!1),h=d(!0);class f{constructor(e,t){this.tag=e,this.value=t}static some(e){return new f(!0,e)}static none(){return f.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?f.some(e(this.value)):f.none()}bind(e){return this.tag?e(this.value):f.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:f.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return s(e)?f.some(e):f.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}f.singletonNone=new f(!1);const y=Object.keys,w=Object.hasOwnProperty,S=(e,t)=>{const o=y(e);for(let l=0,n=o.length;l<n;l++){const n=o[l];t(e[n],n)}},C=(e,t)=>{const o={};var l;return((e,t,o,l)=>{S(e,((e,n)=>{(t(e,n)?o:l)(e,n)}))})(e,t,(l=o,(e,t)=>{l[t]=e}),m),o},v=e=>y(e).length,T=(e,t)=>x(e,t)?f.from(e[t]):f.none(),x=(e,t)=>w.call(e,t),A=(e,t)=>x(e,t)&&void 0!==e[t]&&null!==e[t],R=Array.prototype.indexOf,_=Array.prototype.push,O=(e,t)=>((e,t)=>R.call(e,t))(e,t)>-1,D=(e,t)=>{for(let o=0,l=e.length;o<l;o++)if(t(e[o],o))return!0;return!1},I=(e,t)=>{const o=[];for(let l=0;l<e;l++)o.push(t(l));return o},M=(e,t)=>{const o=e.length,l=new Array(o);for(let n=0;n<o;n++){const o=e[n];l[n]=t(o,n)}return l},N=(e,t)=>{for(let o=0,l=e.length;o<l;o++)t(e[o],o)},P=(e,t)=>{const o=[];for(let l=0,n=e.length;l<n;l++){const n=e[l];t(n,l)&&o.push(n)}return o},k=(e,t,o)=>(N(e,((e,l)=>{o=t(o,e,l)})),o),B=(e,t)=>((e,t,o)=>{for(let l=0,n=e.length;l<n;l++){const n=e[l];if(t(n,l))return f.some(n);if(o(n,l))break}return f.none()})(e,t,g),E=(e,t)=>(e=>{const t=[];for(let o=0,l=e.length;o<l;++o){if(!n(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);_.apply(t,e[o])}return t})(M(e,t)),F=(e,t)=>{for(let o=0,l=e.length;o<l;++o)if(!0!==t(e[o],o))return!1;return!0},q=(e,t)=>t>=0&&t<e.length?f.some(e[t]):f.none(),L=(e,t)=>{for(let o=0;o<e.length;o++){const l=t(e[o],o);if(l.isSome())return l}return f.none()},H=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},j={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return H(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return H(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return H(o)},fromDom:H,fromPoint:(e,t,o)=>f.from(e.dom.elementFromPoint(t,o)).map(H)};"undefined"!=typeof window?window:Function("return this;")();const V=e=>e.dom.nodeName.toLowerCase(),z=e=>e.dom.nodeType,W=e=>t=>z(t)===e,$=W(1),U=W(3),G=W(9),K=W(11),J=e=>t=>$(t)&&V(t)===e,Q=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},X=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Y=(e,t)=>e.dom===t.dom,Z=Q,ee=e=>G(e)?e:j.fromDom(e.dom.ownerDocument),te=e=>f.from(e.dom.parentNode).map(j.fromDom),oe=e=>f.from(e.dom.nextSibling).map(j.fromDom),le=e=>M(e.dom.childNodes,j.fromDom),ne=c(Element.prototype.attachShadow)&&c(Node.prototype.getRootNode)?e=>j.fromDom(e.dom.getRootNode()):ee,re=e=>j.fromDom(e.dom.host),ae=e=>{const t=U(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=ne(e);return K(o=t)&&s(o.dom.host)?f.some(t):f.none();var o})(j.fromDom(t)).fold((()=>o.body.contains(t)),(l=ae,n=re,e=>l(n(e))));var l,n},se=(e,t)=>{let o=[];return N(le(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(se(e,t))})),o},ce=(e,t)=>((e,o)=>P(le(e),(e=>Q(e,t))))(e),ie=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return X(o)?[]:M(o.querySelectorAll(e),j.fromDom)})(t,e),me=(e,t,o)=>{let l=e.dom;const n=c(o)?o:g;for(;l.parentNode;){l=l.parentNode;const e=j.fromDom(l);if(t(e))return f.some(e);if(n(e))break}return f.none()},de=(e,t,o)=>me(e,(e=>Q(e,t)),o),ue=(e,t)=>((e,o)=>B(e.dom.childNodes,(e=>{return o=j.fromDom(e),Q(o,t);var o})).map(j.fromDom))(e),pe=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return X(o)?f.none():f.from(o.querySelector(e)).map(j.fromDom)})(t,e),be=(e,t,o)=>((e,t,o,l,n)=>((e,t)=>Q(e,t))(o,l)?f.some(o):c(n)&&n(o)?f.none():t(o,l,n))(0,de,e,t,o),ge=(e,t,o)=>{if(!(l(o)||r(o)||i(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},he=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},fe=(e,t)=>f.from(he(e,t)),ye=(e,t)=>{e.dom.removeAttribute(t)},we=(e,t,o=p)=>e.exists((e=>o(e,t))),Se=(e,t,o)=>e.isSome()&&t.isSome()?f.some(o(e.getOrDie(),t.getOrDie())):f.none(),Ce=(e,t)=>((e,t,o)=>""===t||e.length>=t.length&&e.substr(0,0+t.length)===t)(e,t),ve=(Te=/^\s+|\s+$/g,e=>e.replace(Te,""));var Te;const xe=e=>e.length>0,Ae=e=>void 0!==e.style&&c(e.style.getPropertyValue),Re=(e,t)=>{const o=e.dom,l=window.getComputedStyle(o).getPropertyValue(t);return""!==l||ae(e)?l:_e(o,t)},_e=(e,t)=>Ae(e)?e.style.getPropertyValue(t):"",Oe=(e,t)=>{const o=e.dom,l=_e(o,t);return f.from(l).filter((e=>e.length>0))},De=(e,t,o=0)=>fe(e,t).map((e=>parseInt(e,10))).getOr(o),Ie=(e,t)=>Me(e,t,h),Me=(e,t,o)=>E(le(e),(e=>Q(e,t)?o(e)?[e]:[]:Me(e,t,o))),Ne=["tfoot","thead","tbody","colgroup"],Pe=(e,t,o)=>({element:e,rowspan:t,colspan:o}),ke=(e,t,o)=>({element:e,cells:t,section:o}),Be=(e,t)=>be(e,"table",t),Ee=e=>Ie(e,"tr"),Fe=e=>Be(e).fold(d([]),(e=>ce(e,"colgroup"))),qe=e=>te(e).map((e=>{const t=V(e);return(e=>O(Ne,e))(t)?t:"tbody"})).getOr("tbody"),Le=e=>fe(e,"data-snooker-locked-cols").bind((e=>f.from(e.match(/\d+/g)))).map((e=>((e,t)=>{const o={};for(let l=0,n=e.length;l<n;l++){const n=e[l];o[String(n)]=t(n,l)}return o})(e,h))),He=(e,t)=>e+","+t,je=e=>{const t={},o=[];var l;const n=(l=e,q(l,0)).map((e=>e.element)).bind(Be).bind(Le).getOr({});let r=0,a=0,s=0;const{pass:c,fail:i}=((e,t)=>{const o=[],l=[];for(let t=0,r=e.length;t<r;t++){const r=e[t];(n=r,"colgroup"===n.section?o:l).push(r)}var n;return{pass:o,fail:l}})(e);N(i,(e=>{const l=[];N(e.cells,(e=>{let o=0;for(;void 0!==t[He(s,o)];)o++;const r=A(n,o.toString()),c=((e,t,o,l,n,r)=>({element:e,rowspan:t,colspan:o,row:l,column:n,isLocked:r}))(e.element,e.rowspan,e.colspan,s,o,r);for(let l=0;l<e.colspan;l++)for(let n=0;n<e.rowspan;n++){const e=o+l,r=He(s+n,e);t[r]=c,a=Math.max(a,e+1)}l.push(c)})),r++,o.push(ke(e.element,l,e.section)),s++}));const{columns:m,colgroups:d}=(e=>q(e,e.length-1))(c).map((e=>{const t=(e=>{const t={};let o=0;return N(e.cells,(e=>{const l=e.colspan;I(l,(n=>{const r=o+n;t[r]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,l,r)})),o+=l})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,((e,t)=>{const o=[];return S(e,((e,l)=>{o.push(t(e,l))})),o})(t,u));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),p=((e,t)=>({rows:e,columns:t}))(r,a);return{grid:p,access:t,all:o,columns:m,colgroups:d}},Ve=e=>{const t=(e=>{const t=Ee(e);return((e,t)=>M(e,(e=>{if("colgroup"===V(e)){const t=M((e=>Q(e,"colgroup")?ce(e,"col"):E(Fe(e),(e=>ce(e,"col"))))(e),(e=>{const t=De(e,"span",1);return Pe(e,1,t)}));return ke(e,t,"colgroup")}{const o=M((e=>Ie(e,"th,td"))(e),(e=>{const t=De(e,"rowspan",1),o=De(e,"colspan",1);return Pe(e,t,o)}));return ke(e,o,t(e))}})))([...Fe(e),...t],qe)})(e);return je(t)},ze=(e,t,o)=>f.from(e.access[He(t,o)]),We=(e,t,o)=>{const l=((e,t)=>{const o=E(e.all,(e=>e.cells));return P(o,t)})(e,(e=>o(t,e.element)));return l.length>0?f.some(l[0]):f.none()},$e=(e,t)=>f.from(e.columns[t]);var Ue=tinymce.util.Tools.resolve("tinymce.util.Tools");const Ge=(e,t,o)=>((t,l)=>{for(let n=0;n<l.length;n++){const r=e.getStyle(l[n],o);if(void 0===t&&(t=r),t!==r)return""}return t})(void 0,e.select("td,th",t)),Ke=(e,t,o)=>{Ue.each("left center right".split(" "),(l=>{l!==o&&e.formatter.remove("align"+l,{},t)})),o&&e.formatter.apply("align"+o,{},t)},Je=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},Qe=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?f.none():f.some(t)})(e).getOr(t))(Re(e,t),o),Xe=e=>((e,t)=>{const o=e.dom,l=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?l:((e,t,o,l)=>t-Qe(e,"padding-left",0)-Qe(e,"padding-right",0)-Qe(e,"border-left-width",0)-Qe(e,"border-right-width",0))(e,l)})(e,"content-box");var Ye=tinymce.util.Tools.resolve("tinymce.Env");const Ze=I(5,(e=>{const t=`${e+1}px`;return{title:t,value:t}})),et=M(["Solid","Dotted","Dashed","Double","Groove","Ridge","Inset","Outset","None","Hidden"],(e=>({title:e,value:e.toLowerCase()}))),tt="100%",ot=e=>{var t;const o=e.dom,l=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return Xe(j.fromDom(l))+"px"},lt=e=>t=>t.options.get(e),nt=lt("table_sizing_mode"),rt=lt("table_border_widths"),at=lt("table_border_styles"),st=lt("table_cell_advtab"),ct=lt("table_row_advtab"),it=lt("table_advtab"),mt=lt("table_appearance_options"),dt=lt("table_grid"),ut=lt("table_style_by_css"),pt=lt("table_cell_class_list"),bt=lt("table_row_class_list"),gt=lt("table_class_list"),ht=lt("table_toolbar"),ft=lt("table_background_color_map"),yt=lt("table_border_color_map"),wt=e=>"fixed"===nt(e),St=e=>"responsive"===nt(e),Ct=e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>St(e)||!ut(e)?t:wt(e)?{...t,width:ot(e)}:{...t,width:tt})(e,o)},vt=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>St(e)||ut(e)?t:wt(e)?{...t,width:ot(e)}:{...t,width:tt})(e,o)},Tt=e=>t=>Y(t,(e=>j.fromDom(e.getBody()))(e)),xt=e=>/^\d+(\.\d+)?$/.test(e)?e+"px":e,At=e=>j.fromDom(e.selection.getStart()),Rt=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,_t=(e,t,o)=>((e,t,o)=>{const l=We(e,t,Y),n=We(e,o,Y);return l.bind((e=>n.map((t=>{return o=e,l=t,{startRow:Math.min(o.row,l.row),startCol:Math.min(o.column,l.column),finishRow:Math.max(o.row+o.rowspan-1,l.row+l.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,l.column+l.colspan-1)};var o,l}))))})(e,t,o).bind((t=>((e,t)=>{let o=!0;const l=b(Rt,t);for(let n=t.startRow;n<=t.finishRow;n++)for(let r=t.startCol;r<=t.finishCol;r++)o=o&&ze(e,n,r).exists(l);return o?f.some(t):f.none()})(e,t))),Ot=Ve,Dt=(e,t)=>{te(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},It=(e,t)=>{oe(e).fold((()=>{te(e).each((e=>{Mt(e,t)}))}),(e=>{Dt(e,t)}))},Mt=(e,t)=>{e.dom.appendChild(t.dom)},Nt=(e,t)=>{N(t,((o,l)=>{const n=0===l?e:t[l-1];It(n,o)}))},Pt=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},kt=((e,t)=>{const o=t=>e(t)?f.from(t.dom.nodeValue):f.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(U);var Bt=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];const Et=(e,t,o,l)=>{const n=t(e,o);return r=(o,l)=>{const n=t(e,l);return Ft(e,o,n)},a=n,((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(l,((e,t)=>{a=r(a,e)})),a;var r,a},Ft=(e,t,o)=>t.bind((t=>o.filter(b(e.eq,t)))),qt={up:d({selector:de,closest:be,predicate:me,all:(e,t)=>{const o=c(t)?t:g;let l=e.dom;const n=[];for(;null!==l.parentNode&&void 0!==l.parentNode;){const e=l.parentNode,t=j.fromDom(e);if(n.push(t),!0===o(t))break;l=e}return n}}),down:d({selector:ie,predicate:se}),styles:d({get:Re,getRaw:Oe,set:(e,t,o)=>{((e,t,o)=>{if(!l(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Ae(e)&&e.style.setProperty(t,o)})(e.dom,t,o)},remove:(e,t)=>{((e,t)=>{Ae(e)&&e.style.removeProperty(t)})(e.dom,t),we(fe(e,"style").map(ve),"")&&ye(e,"style")}}),attrs:d({get:he,set:(e,t,o)=>{ge(e.dom,t,o)},remove:ye,copyTo:(e,t)=>{((e,t)=>{const o=e.dom;S(t,((e,t)=>{ge(o,t,e)}))})(t,k(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))}}),insert:d({before:Dt,after:It,afterAll:Nt,append:Mt,appendAll:(e,t)=>{N(t,(t=>{Mt(e,t)}))},prepend:(e,t)=>{(e=>((e,t)=>{const o=e.dom.childNodes;return f.from(o[0]).map(j.fromDom)})(e))(e).fold((()=>{Mt(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},wrap:(e,t)=>{Dt(e,t),Mt(t,e)}}),remove:d({unwrap:e=>{const t=le(e);t.length>0&&Nt(e,t),Pt(e)},remove:Pt}),create:d({nu:j.fromTag,clone:e=>j.fromDom(e.dom.cloneNode(!1)),text:j.fromText}),query:d({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:e=>f.from(e.dom.previousSibling).map(j.fromDom),nextSibling:oe}),property:d({children:le,name:V,parent:te,document:e=>ee(e).dom,isText:U,isComment:e=>8===z(e)||"#comment"===V(e),isElement:$,isSpecial:e=>{const t=V(e);return O(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>$(e)?fe(e,"lang"):f.none(),getText:e=>kt.get(e),setText:(e,t)=>kt.set(e,t),isBoundary:e=>!!$(e)&&("body"===V(e)||O(Bt,V(e))),isEmptyTag:e=>!!$(e)&&O(["br","img","hr","input"],V(e)),isNonEditable:e=>$(e)&&"false"===he(e,"contenteditable")}),eq:Y,is:Z},Lt=e=>de(e,"table"),Ht=(e,t,o)=>pe(e,t).bind((t=>pe(e,o).bind((e=>{return(o=Lt,l=[t,e],((e,t,o)=>o.length>0?((e,t,o,l)=>l(e,t,o[0],o.slice(1)))(e,t,o,Et):f.none())(qt,((e,t)=>o(t)),l)).map((o=>({first:t,last:e,table:o})));var o,l})))),jt=e=>M(e,j.fromDom),Vt={selected:"data-mce-selected",selectedSelector:"td[data-mce-selected],th[data-mce-selected]",firstSelected:"data-mce-first-selected",firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:"data-mce-last-selected",lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"},zt=e=>(t,o)=>{const l=V(t),n="col"===l||"colgroup"===l?Be(r=t).bind((e=>((e,t)=>((e,t)=>{const o=ie(e,t);return o.length>0?f.some(o):f.none()})(e,t))(e,Vt.firstSelectedSelector))).fold(d(r),(e=>e[0])):t;var r;return be(n,e,o)},Wt=zt("th,td,caption"),$t=zt("th,td"),Ut=e=>jt(e.model.table.getSelectedCells()),Gt=(e,t)=>{const o=$t(e),l=o.bind((e=>Be(e))).map((e=>Ee(e)));return Se(o,l,((e,o)=>P(o,(o=>D(jt(o.dom.cells),(o=>"1"===he(o,t)||Y(o,e))))))).getOr([])},Kt=[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}],Jt=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Qt=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Xt=e=>{return(t=e,"#",Ce(t,"#")?((e,t)=>e.substring(t))(t,"#".length):t).toUpperCase();var t},Yt=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Zt=e=>({value:Yt(e.red)+Yt(e.green)+Yt(e.blue)}),eo=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,to=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,oo=(e,t,o,l)=>({red:e,green:t,blue:o,alpha:l}),lo=(e,t,o,l)=>{const n=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),s=parseFloat(l);return oo(n,r,a,s)},no=e=>{if("transparent"===e)return f.some(oo(0,0,0,0));const t=eo.exec(e);if(null!==t)return f.some(lo(t[1],t[2],t[3],"1"));const o=to.exec(e);return null!==o?f.some(lo(o[1],o[2],o[3],o[4])):f.none()},ro=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},ao=(e,t,o)=>l=>{const n=(e=>{const t=ro(f.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(f.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(f.some(e))}}})((e=>e.unbind())),r=!xe(o),a=()=>{const a=Ut(e),s=l=>e.formatter.match(t,{value:o},l.dom,r);r?(l.setActive(!D(a,s)),n.set(e.formatter.formatChanged(t,(e=>l.setActive(!e)),!0))):(l.setActive(F(a,s)),n.set(e.formatter.formatChanged(t,l.setActive,!1,{value:o})))};return e.initialized?a():e.on("init",a),n.clear},so=e=>A(e,"menu"),co=e=>M(e,(e=>{const t=e.text||e.title;return so(e)?{text:t,items:co(e.menu)}:{text:t,value:e.value}})),io=(e,t,o,l)=>M(t,(t=>{const n=t.text||t.title;return so(t)?{type:"nestedmenuitem",text:n,getSubmenuItems:()=>io(e,t.menu,o,l)}:{text:n,type:"togglemenuitem",onAction:()=>l(t.value),onSetup:ao(e,o,t.value)}})),mo=(e,t)=>o=>{e.execCommand("mceTableApplyCellStyle",!1,{[t]:o})},uo=e=>E(e,(e=>so(e)?[{...e,menu:uo(e.menu)}]:xe(e.value)?[e]:[])),po=(e,t,o,l)=>n=>n(io(e,t,o,l)),bo=(e,t,o)=>{const l=M(t,(e=>{return{text:e.title,value:"#"+(o=e.value,(t=o,(e=>Jt.test(e)||Qt.test(e))(t)?f.some({value:Xt(t)}):f.none()).orThunk((()=>no(o).map(Zt))).getOrThunk((()=>{const e=document.createElement("canvas");e.height=1,e.width=1;const t=e.getContext("2d");t.clearRect(0,0,e.width,e.height),t.fillStyle="#FFFFFF",t.fillStyle=o,t.fillRect(0,0,1,1);const l=t.getImageData(0,0,1,1).data,n=l[0],r=l[1],a=l[2],s=l[3];return Zt(oo(n,r,a,s))}))).value,type:"choiceitem"};var t,o}));return[{type:"fancymenuitem",fancytype:"colorswatch",initData:{colors:l.length>0?l:void 0,allowCustomColors:!1},onAction:t=>{const l="remove"===t.value?"":t.value;e.execCommand("mceTableApplyCellStyle",!1,{[o]:l})}}]},go=e=>()=>{const t="header"===e.queryCommandValue("mceTableRowType")?"body":"header";e.execCommand("mceTableRowType",!1,{type:t})},ho=e=>()=>{const t="th"===e.queryCommandValue("mceTableColType")?"td":"th";e.execCommand("mceTableColType",!1,{type:t})},fo=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"listbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"listbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"listbox",label:"Horizontal align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"listbox",label:"Vertical align",items:Kt}],yo=e=>fo.concat((e=>{const t=co(pt(e));return t.length>0?f.some({name:"class",type:"listbox",label:"Class",items:t}):f.none()})(e).toArray()),wo=(e,t)=>{const o=[{name:"borderstyle",type:"listbox",label:"Border style",items:[{text:"Select...",value:""}].concat(co(at(e)))},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===t?[{name:"borderwidth",type:"input",label:"Border width"}].concat(o):o}},So=(e,t)=>{const o=e.dom;return{setAttrib:(e,l)=>{o.setAttrib(t,e,l)},setStyle:(e,l)=>{o.setStyle(t,e,l)},setFormat:(o,l)=>{""===l?e.formatter.remove(o,{value:null},t,!0):e.formatter.apply(o,{value:l},t)}}},Co=J("th"),vo=(e,t)=>e&&t?"sectionCells":e?"section":"cells",To=e=>{const t=M(e,(e=>(e=>{const t="thead"===e.section,o=we((e=>{const t=P(e,(e=>Co(e.element)));return 0===t.length?f.some("td"):t.length===e.length?f.some("th"):f.none()})(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:vo(t,o)}:{type:"body"}})(e).type)),o=O(t,"header"),l=O(t,"footer");if(o||l){const e=O(t,"body");return!o||e||l?o||e||!l?f.none():f.some("footer"):f.some("header")}return f.some("body")},xo=(e,t)=>L(e.all,(e=>B(e.cells,(e=>Y(t,e.element))))),Ao=(e,t,o)=>{const l=(e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t})(M(t.selection,(t=>{return(l=t,((e,t,o=g)=>o(t)?f.none():O(e,V(t))?f.some(t):de(t,e.join(","),(e=>Q(e,"table")||o(e))))(["td","th"],l,n)).bind((t=>xo(e,t))).filter(o);var l,n})));return n=l,l.length>0?f.some(n):f.none();var n},Ro=(e,t)=>Ao(e,t,h),_o=(e,t)=>F(t,(t=>((e,t)=>xo(e,t).exists((e=>!e.isLocked)))(e,t))),Oo=(e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>_o(e,t.cells))),Do=(e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>_o(e,t))),Io=((e=>{if(!n(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};N(e,((l,r)=>{const a=y(l);if(1!==a.length)throw new Error("one and only one name per case");const s=a[0],c=l[s];if(void 0!==o[s])throw new Error("duplicate key detected:"+s);if("cata"===s)throw new Error("cannot have a case named cata (sorry)");if(!n(c))throw new Error("case arguments must be an array");t.push(s),o[s]=(...o)=>{const l=o.length;if(l!==c.length)throw new Error("Wrong number of arguments to case "+s+". Expected "+c.length+" ("+c+"), got "+l);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const l=y(e);if(t.length!==l.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+l.join(","));if(!F(t,(e=>O(l,e))))throw new Error("Not all branches were specified when using match. Specified: "+l.join(", ")+"\nRequired: "+t.join(", "));return e[s].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:s,params:o})}}}}))})([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),(e,t)=>{const o=Ve(e);return Ro(o,t).bind((e=>{const t=e[e.length-1],l=e[0].row,n=t.row+t.rowspan,r=o.all.slice(l,n);return To(r)})).getOr("")}),Mo=e=>{return Ce(e,"rgb")?no(t=e).map(Zt).map((e=>"#"+e.value)).getOr(t):e;var t},No=e=>{const t=j.fromDom(e);return{borderwidth:Oe(t,"border-width").getOr(""),borderstyle:Oe(t,"border-style").getOr(""),bordercolor:Oe(t,"border-color").map(Mo).getOr(""),backgroundcolor:Oe(t,"background-color").map(Mo).getOr("")}},Po=e=>{const t=e[0],o=e.slice(1);return N(o,(e=>{N(y(t),(o=>{S(e,((e,l)=>{const n=t[o];""!==n&&o===l&&n!==e&&(t[o]="")}))}))})),t},ko=(e,t,o,l)=>B(e,(e=>!a(o.formatter.matchNode(l,t+e)))).getOr(""),Bo=b(ko,["left","center","right"],"align"),Eo=b(ko,["top","middle","bottom"],"valign"),Fo=e=>Be(j.fromDom(e)).map((t=>{const o={selection:jt(e.cells)};return Io(t,o)})).getOr(""),qo=(e,t)=>{const o=Ve(e),l=(e=>E(e.all,(e=>e.cells)))(o),n=P(l,(e=>D(t,(t=>Y(e.element,t)))));return M(n,(e=>({element:e.element.dom,column:$e(o,e.column).map((e=>e.element.dom))})))},Lo=(e,t,o,l)=>{const n=l.getData();l.close(),e.undoManager.transact((()=>{((e,t,o,l)=>{const n=C(l,((e,t)=>o[t]!==e));v(n)>0&&t.length>=1&&Be(t[0]).each((o=>{const r=qo(o,t),a=v(C(n,((e,t)=>"scope"!==t&&"celltype"!==t)))>0,s=x(n,"celltype");(a||x(n,"scope"))&&((e,t,o,l)=>{const n=1===t.length;N(t,(t=>{const r=t.element,a=n?h:l,s=So(e,r);((e,t,o,l)=>{l("scope")&&e.setAttrib("scope",o.scope),l("class")&&e.setAttrib("class",o.class),l("height")&&e.setStyle("height",xt(o.height)),l("width")&&t.setStyle("width",xt(o.width))})(s,t.column.map((t=>So(e,t))).getOr(s),o,a),st(e)&&((e,t,o)=>{o("backgroundcolor")&&e.setFormat("tablecellbackgroundcolor",t.backgroundcolor),o("bordercolor")&&e.setFormat("tablecellbordercolor",t.bordercolor),o("borderstyle")&&e.setFormat("tablecellborderstyle",t.borderstyle),o("borderwidth")&&e.setFormat("tablecellborderwidth",xt(t.borderwidth))})(s,o,a),l("halign")&&Ke(e,r,o.halign),l("valign")&&((e,t,o)=>{Ue.each("top middle bottom".split(" "),(l=>{l!==o&&e.formatter.remove("valign"+l,{},t)})),o&&e.formatter.apply("valign"+o,{},t)})(e,r,o.valign)}))})(e,r,l,b(x,n)),s&&((e,t)=>{e.execCommand("mceTableCellType",!1,{type:t.celltype,no_events:!0})})(e,l),Je(e,o.dom,{structure:s,style:a})}))})(e,t,o,n),e.focus()}))},Ho=e=>{const t=Ut(e);if(0===t.length)return;const o=((e,t)=>{const o=Be(t[0]).map((o=>M(qo(o,t),(t=>((e,t,o,l)=>{const n=e.dom,r=(e,t)=>n.getStyle(e,t)||n.getAttrib(e,t);return{width:r(l.getOr(t),"width"),height:r(t,"height"),scope:n.getAttrib(t,"scope"),celltype:(a=t,a.nodeName.toLowerCase()),class:n.getAttrib(t,"class",""),halign:Bo(e,t),valign:Eo(e,t),...o?No(t):{}};var a})(e,t.element,st(e),t.column)))));return Po(o.getOrDie())})(e,t),l={type:"tabpanel",tabs:[{title:"General",name:"general",items:yo(e)},wo(e,"cell")]},n={type:"panel",items:[{type:"grid",columns:2,items:yo(e)}]};e.windowManager.open({title:"Cell Properties",size:"normal",body:st(e)?l:n,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onSubmit:b(Lo,e,t,o)})},jo=[{type:"listbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],Vo=e=>jo.concat((e=>{const t=co(bt(e));return t.length>0?f.some({name:"class",type:"listbox",label:"Class",items:t}):f.none()})(e).toArray()),zo=(e,t,o,l)=>{const n=l.getData();l.close(),e.undoManager.transact((()=>{((e,t,o,l)=>{const n=C(l,((e,t)=>o[t]!==e));if(v(n)>0){const o=x(n,"type"),r=!o||v(n)>1;r&&((e,t,o,l)=>{const n=1===t.length?h:l;N(t,(t=>{const r=So(e,t);((e,t,o)=>{o("class")&&e.setAttrib("class",t.class),o("height")&&e.setStyle("height",xt(t.height))})(r,o,n),ct(e)&&((e,t,o)=>{o("backgroundcolor")&&e.setStyle("background-color",t.backgroundcolor),o("bordercolor")&&e.setStyle("border-color",t.bordercolor),o("borderstyle")&&e.setStyle("border-style",t.borderstyle)})(r,o,n),l("align")&&Ke(e,t,o.align)}))})(e,t,l,b(x,n)),o&&((e,t)=>{e.execCommand("mceTableRowType",!1,{type:t.type,no_events:!0})})(e,l),Be(j.fromDom(t[0])).each((t=>Je(e,t.dom,{structure:o,style:r})))}})(e,t,o,n),e.focus()}))},Wo=e=>{const t=Gt(At(e),Vt.selected);if(0===t.length)return;const o=M(t,(t=>((e,t,o)=>{const l=e.dom;return{height:l.getStyle(t,"height")||l.getAttrib(t,"height"),class:l.getAttrib(t,"class",""),type:Fo(t),align:Bo(e,t),...o?No(t):{}}})(e,t.dom,ct(e)))),l=Po(o),n={type:"tabpanel",tabs:[{title:"General",name:"general",items:Vo(e)},wo(e,"row")]},r={type:"panel",items:[{type:"grid",columns:2,items:Vo(e)}]};e.windowManager.open({title:"Row Properties",size:"normal",body:ct(e)?n:r,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:l,onSubmit:b(zo,e,M(t,(e=>e.dom)),l)})},$o=(e,t,o)=>{const l=o?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],n=mt(e)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],r=t.length>0?[{type:"listbox",name:"class",label:"Class",items:t}]:[];return l.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(n).concat([{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(r)},Uo=(e,t,o,n)=>{if("TD"===t.tagName||"TH"===t.tagName)l(o)?e.setStyle(t,o,n):e.setStyles(t,o);else if(t.children)for(let l=0;l<t.children.length;l++)Uo(e,t.children[l],o,n)},Go=(e,t,o,l)=>{const n=e.dom,r=l.getData(),a=C(r,((e,t)=>o[t]!==e));l.close(),""===r.class&&delete r.class,e.undoManager.transact((()=>{if(!t){const o=parseInt(r.cols,10)||1,l=parseInt(r.rows,10)||1;e.execCommand("mceInsertTable",!1,{rows:l,columns:o}),t=$t(At(e),Tt(e)).bind((t=>Be(t,Tt(e)))).map((e=>e.dom)).getOrUndefined()}if(v(a)>0){((e,t,o)=>{const l=e.dom,n={},r={};if(n.class=o.class,r.height=xt(o.height),ut(e)?r.width=xt(o.width):l.getAttrib(t,"width")&&(n.width=(e=>e?e.replace(/px$/,""):"")(o.width)),ut(e)?(r["border-width"]=xt(o.border),r["border-spacing"]=xt(o.cellspacing)):(n.border=o.border,n.cellpadding=o.cellpadding,n.cellspacing=o.cellspacing),ut(e)&&t.children)for(let n=0;n<t.children.length;n++)Uo(l,t.children[n],{"border-width":xt(o.border),padding:xt(o.cellpadding)}),it(e)&&Uo(l,t.children[n],{"border-color":o.bordercolor});it(e)&&(r["background-color"]=o.backgroundcolor,r["border-color"]=o.bordercolor,r["border-style"]=o.borderstyle),n.style=l.serializeStyle({...Ct(e),...r}),l.setAttribs(t,{...vt(e),...n})})(e,t,r);const o=n.select("caption",t)[0];(o&&!r.caption||!o&&r.caption)&&e.execCommand("mceTableToggleCaption"),Ke(e,t,r.align)}if(e.focus(),e.addVisual(),v(a)>0){const o=x(a,"caption"),l=!o||v(a)>1;Je(e,t,{structure:o,style:l})}}))},Ko=(e,t)=>{const o=e.dom;let l,n=((e,t)=>{const o=Ct(e),l=vt(e),n=t?{borderstyle:T(o,"border-style").getOr(""),bordercolor:Mo(T(o,"border-color").getOr("")),backgroundcolor:Mo(T(o,"background-color").getOr(""))}:{};return{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,class:"",align:"",border:"",...o,...l,...n,...(()=>{const t=o["border-width"];return ut(e)&&t?{border:t}:T(l,"border").fold((()=>({})),(e=>({border:e})))})(),...{...T(o,"border-spacing").or(T(l,"cellspacing")).fold((()=>({})),(e=>({cellspacing:e}))),...T(o,"border-padding").or(T(l,"cellpadding")).fold((()=>({})),(e=>({cellpadding:e})))}}})(e,it(e));!1===t?(l=o.getParent(e.selection.getStart(),"table",e.getBody()),l?n=((e,t,o)=>{const l=e.dom,n=ut(e)?l.getStyle(t,"border-spacing")||l.getAttrib(t,"cellspacing"):l.getAttrib(t,"cellspacing")||l.getStyle(t,"border-spacing"),r=ut(e)?Ge(l,t,"padding")||l.getAttrib(t,"cellpadding"):l.getAttrib(t,"cellpadding")||Ge(l,t,"padding");return{width:l.getStyle(t,"width")||l.getAttrib(t,"width"),height:l.getStyle(t,"height")||l.getAttrib(t,"height"),cellspacing:n,cellpadding:r,border:((t,o)=>{const l=Oe(j.fromDom(o),"border-width");return ut(e)&&l.isSome()?l.getOr(""):t.getAttrib(o,"border")||Ge(e.dom,o,"border-width")||Ge(e.dom,o,"border")})(l,t),caption:!!l.select("caption",t)[0],class:l.getAttrib(t,"class",""),align:Bo(e,t),...o?No(t):{}}})(e,l,it(e)):it(e)&&(n.borderstyle="",n.bordercolor="",n.backgroundcolor="")):(n.cols="1",n.rows="1",it(e)&&(n.borderstyle="",n.bordercolor="",n.backgroundcolor=""));const r=co(gt(e));r.length>0&&n.class&&(n.class=n.class.replace(/\s*mce\-item\-table\s*/g,""));const a={type:"grid",columns:2,items:$o(e,r,t)},s=it(e)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[a]},wo(e,"table")]}:{type:"panel",items:[a]};e.windowManager.open({title:"Table Properties",size:"normal",body:s,onSubmit:b(Go,e,l,n),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:n})},Jo=u,Qo=e=>{const t=(e,t)=>fe(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&F(e,(e=>t(e,"rowspan")||t(e,"colspan")))?f.some(e):f.none()},Xo=(e,t,o)=>{return t.length<=1?f.none():(l=e,n=o.firstSelectedSelector,r=o.lastSelectedSelector,Ht(l,n,r).bind((e=>{const t=e=>Y(l,e),o="thead,tfoot,tbody,table",n=de(e.first,o,t),r=de(e.last,o,t);return n.bind((t=>r.bind((o=>Y(t,o)?((e,t,o)=>{const l=Ot(e);return _t(l,t,o)})(e.table,e.first,e.last):f.none()))))}))).map((e=>({bounds:e,cells:t})));var l,n,r},Yo=e=>{const t=ro(f.none()),o=ro([]);let l=f.none();const n=J("caption"),r=e=>l.forall((t=>!t[e])),a=()=>Wt(At(e),Tt(e)).bind((t=>{return o=Se(Be(t),Wt((e=>j.fromDom(e.selection.getEnd()))(e),Tt(e)).bind(Be),((o,l)=>Y(o,l)?n(t)?f.some((e=>({element:e,mergable:f.none(),unmergable:f.none(),selection:[e]}))(t)):f.some(((e,t,o)=>({element:o,mergable:Xo(t,e,Vt),unmergable:Qo(e),selection:Jo(e)}))(Ut(e),o,t)):f.none())),o.bind(u);var o})),s=e=>Be(e.element).map((t=>{const o=Ve(t),l=Ro(o,e).getOr([]),n=k(l,((e,t)=>(t.isLocked&&(e.onAny=!0,0===t.column?e.onFirst=!0:t.column+t.colspan>=o.grid.columns&&(e.onLast=!0)),e)),{onAny:!1,onFirst:!1,onLast:!1});return{mergeable:Oo(o,e).isSome(),unmergeable:Do(o,e).isSome(),locked:n}})),c=()=>{t.set((e=>{let t,o=!1;return(...l)=>(o||(o=!0,t=e.apply(null,l)),t)})(a)()),l=t.get().bind(s),N(o.get(),(e=>e()))},i=e=>(e(),o.set(o.get().concat([e])),()=>{o.set(P(o.get(),(t=>t!==e)))}),m=(e,o)=>i((()=>t.get().fold((()=>{e.setEnabled(!1)}),(t=>{e.setEnabled(!o(t))})))),d=(e,o,l)=>i((()=>t.get().fold((()=>{e.setEnabled(!1),e.setActive(!1)}),(t=>{e.setEnabled(!o(t)),e.setActive(l(t))})))),p=e=>l.exists((t=>t.locked[e])),b=(t,o)=>l=>d(l,(e=>n(e.element)),(()=>e.queryCommandValue(t)===o)),h=b("mceTableRowType","header"),y=b("mceTableColType","th");return e.on("NodeChange ExecCommand TableSelectorChange",c),{onSetupTable:e=>m(e,(e=>!1)),onSetupCellOrRow:e=>m(e,(e=>n(e.element))),onSetupColumn:e=>t=>m(t,(t=>n(t.element)||p(e))),onSetupPasteable:e=>t=>m(t,(t=>n(t.element)||e().isNone())),onSetupPasteableColumn:(e,t)=>o=>m(o,(o=>n(o.element)||e().isNone()||p(t))),onSetupMergeable:e=>m(e,(e=>r("mergeable"))),onSetupUnmergeable:e=>m(e,(e=>r("unmergeable"))),resetTargets:c,onSetupTableWithCaption:t=>d(t,g,(t=>Be(t.element,Tt(e)).exists((e=>ue(e,"caption").isSome())))),onSetupTableRowHeaders:h,onSetupTableColumnHeaders:y,targets:t.get}};var Zo=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const el=e=>{var t;const o=null!==(t=Zo.read())&&void 0!==t?t:[];return L(o,(t=>f.from(t.getType(e))))},tl=()=>el("x-tinymce/dom-table-rows"),ol=()=>el("x-tinymce/dom-table-columns");e.add("table",(e=>{const t=Yo(e);(e=>{const t=e.options.register;t("table_border_widths",{processor:"object[]",default:Ze}),t("table_border_styles",{processor:"object[]",default:et}),t("table_cell_advtab",{processor:"boolean",default:!0}),t("table_row_advtab",{processor:"boolean",default:!0}),t("table_advtab",{processor:"boolean",default:!0}),t("table_appearance_options",{processor:"boolean",default:!0}),t("table_grid",{processor:"boolean",default:!Ye.deviceType.isTouch()}),t("table_cell_class_list",{processor:"object[]",default:[]}),t("table_row_class_list",{processor:"object[]",default:[]}),t("table_class_list",{processor:"object[]",default:[]}),t("table_toolbar",{processor:"string",default:"tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"}),t("table_background_color_map",{processor:"object[]",default:[]}),t("table_border_color_map",{processor:"object[]",default:[]})})(e),(e=>{S({mceTableProps:b(Ko,e,!1),mceTableRowProps:b(Wo,e),mceTableCellProps:b(Ho,e)},((t,o)=>e.addCommand(o,(()=>t())))),e.addCommand("mceInsertTableDialog",(t=>{Ko(e,!0)}))})(e),((e,t)=>{const o=t=>()=>e.execCommand(t),l=(t,l)=>!!e.queryCommandSupported(l.command)&&(e.ui.registry.addMenuItem(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)}),!0),n=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addToggleMenuItem(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})},r=t=>{e.execCommand("mceInsertTable",!1,{rows:t.numRows,columns:t.numColumns})},a=[l("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",command:"mceTableInsertRowBefore",onSetup:t.onSetupCellOrRow}),l("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",command:"mceTableInsertRowAfter",onSetup:t.onSetupCellOrRow}),l("tabledeleterow",{text:"Delete row",icon:"table-delete-row",command:"mceTableDeleteRow",onSetup:t.onSetupCellOrRow}),l("tablerowprops",{text:"Row properties",icon:"table-row-properties",command:"mceTableRowProps",onSetup:t.onSetupCellOrRow}),l("tablecutrow",{text:"Cut row",icon:"cut-row",command:"mceTableCutRow",onSetup:t.onSetupCellOrRow}),l("tablecopyrow",{text:"Copy row",icon:"duplicate-row",command:"mceTableCopyRow",onSetup:t.onSetupCellOrRow}),l("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",command:"mceTablePasteRowBefore",onSetup:t.onSetupPasteable(tl)}),l("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",command:"mceTablePasteRowAfter",onSetup:t.onSetupPasteable(tl)})],s=[l("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",command:"mceTableInsertColBefore",onSetup:t.onSetupColumn("onFirst")}),l("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",command:"mceTableInsertColAfter",onSetup:t.onSetupColumn("onLast")}),l("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",command:"mceTableDeleteCol",onSetup:t.onSetupColumn("onAny")}),l("tablecutcolumn",{text:"Cut column",icon:"cut-column",command:"mceTableCutCol",onSetup:t.onSetupColumn("onAny")}),l("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",command:"mceTableCopyCol",onSetup:t.onSetupColumn("onAny")}),l("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",command:"mceTablePasteColBefore",onSetup:t.onSetupPasteableColumn(ol,"onFirst")}),l("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",command:"mceTablePasteColAfter",onSetup:t.onSetupPasteableColumn(ol,"onLast")})],i=[l("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",command:"mceTableCellProps",onSetup:t.onSetupCellOrRow}),l("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",command:"mceTableMergeCells",onSetup:t.onSetupMergeable}),l("tablesplitcells",{text:"Split cell",icon:"table-split-cells",command:"mceTableSplitCells",onSetup:t.onSetupUnmergeable})];dt(e)?e.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"inserttable",onAction:r}]}):e.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:o("mceInsertTableDialog")}),e.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:o("mceInsertTableDialog")}),l("tableprops",{text:"Table properties",onSetup:t.onSetupTable,command:"mceTableProps"}),l("deletetable",{text:"Delete table",icon:"table-delete-table",onSetup:t.onSetupTable,command:"mceTableDelete"}),O(a,!0)&&e.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:d("tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter")}),O(s,!0)&&e.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",getSubmenuItems:d("tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter")}),O(i,!0)&&e.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:d("tablecellprops tablemergecells tablesplitcells")}),e.ui.registry.addContextMenu("table",{update:()=>(t.resetTargets(),t.targets().fold(d(""),(e=>"caption"===V(e.element)?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable")))});const m=uo(gt(e));0!==m.length&&e.queryCommandSupported("mceTableToggleClass")&&e.ui.registry.addNestedMenuItem("tableclass",{icon:"table-classes",text:"Table styles",getSubmenuItems:()=>io(e,m,"tableclass",(t=>e.execCommand("mceTableToggleClass",!1,t))),onSetup:t.onSetupTable});const u=uo(pt(e));0!==u.length&&e.queryCommandSupported("mceTableCellToggleClass")&&e.ui.registry.addNestedMenuItem("tablecellclass",{icon:"table-cell-classes",text:"Cell styles",getSubmenuItems:()=>io(e,u,"tablecellclass",(t=>e.execCommand("mceTableCellToggleClass",!1,t))),onSetup:t.onSetupCellOrRow}),e.queryCommandSupported("mceTableApplyCellStyle")&&(e.ui.registry.addNestedMenuItem("tablecellvalign",{icon:"vertical-align",text:"Vertical align",getSubmenuItems:()=>io(e,Kt,"tablecellverticalalign",mo(e,"vertical-align")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellborderwidth",{icon:"border-width",text:"Border width",getSubmenuItems:()=>io(e,rt(e),"tablecellborderwidth",mo(e,"border-width")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellborderstyle",{icon:"border-style",text:"Border style",getSubmenuItems:()=>io(e,at(e),"tablecellborderstyle",mo(e,"border-style")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellbackgroundcolor",{icon:"cell-background-color",text:"Background color",getSubmenuItems:()=>bo(e,ft(e),"background-color"),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellbordercolor",{icon:"cell-border-color",text:"Border color",getSubmenuItems:()=>bo(e,yt(e),"border-color"),onSetup:t.onSetupCellOrRow})),n("tablecaption",{icon:"table-caption",text:"Table caption",command:"mceTableToggleCaption",onSetup:t.onSetupTableWithCaption}),n("tablerowheader",{text:"Row header",icon:"table-top-header",command:"mceTableRowType",onAction:go(e),onSetup:t.onSetupTableRowHeaders}),n("tablecolheader",{text:"Column header",icon:"table-left-header",command:"mceTableColType",onAction:ho(e),onSetup:t.onSetupTableRowHeaders})})(e,t),((e,t)=>{e.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:e=>e("inserttable | cell row column | advtablesort | tableprops deletetable")});const o=t=>()=>e.execCommand(t),l=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addButton(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})},n=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addToggleButton(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})};l("tableprops",{tooltip:"Table properties",command:"mceTableProps",icon:"table",onSetup:t.onSetupTable}),l("tabledelete",{tooltip:"Delete table",command:"mceTableDelete",icon:"table-delete-table",onSetup:t.onSetupTable}),l("tablecellprops",{tooltip:"Cell properties",command:"mceTableCellProps",icon:"table-cell-properties",onSetup:t.onSetupCellOrRow}),l("tablemergecells",{tooltip:"Merge cells",command:"mceTableMergeCells",icon:"table-merge-cells",onSetup:t.onSetupMergeable}),l("tablesplitcells",{tooltip:"Split cell",command:"mceTableSplitCells",icon:"table-split-cells",onSetup:t.onSetupUnmergeable}),l("tableinsertrowbefore",{tooltip:"Insert row before",command:"mceTableInsertRowBefore",icon:"table-insert-row-above",onSetup:t.onSetupCellOrRow}),l("tableinsertrowafter",{tooltip:"Insert row after",command:"mceTableInsertRowAfter",icon:"table-insert-row-after",onSetup:t.onSetupCellOrRow}),l("tabledeleterow",{tooltip:"Delete row",command:"mceTableDeleteRow",icon:"table-delete-row",onSetup:t.onSetupCellOrRow}),l("tablerowprops",{tooltip:"Row properties",command:"mceTableRowProps",icon:"table-row-properties",onSetup:t.onSetupCellOrRow}),l("tableinsertcolbefore",{tooltip:"Insert column before",command:"mceTableInsertColBefore",icon:"table-insert-column-before",onSetup:t.onSetupColumn("onFirst")}),l("tableinsertcolafter",{tooltip:"Insert column after",command:"mceTableInsertColAfter",icon:"table-insert-column-after",onSetup:t.onSetupColumn("onLast")}),l("tabledeletecol",{tooltip:"Delete column",command:"mceTableDeleteCol",icon:"table-delete-column",onSetup:t.onSetupColumn("onAny")}),l("tablecutrow",{tooltip:"Cut row",command:"mceTableCutRow",icon:"cut-row",onSetup:t.onSetupCellOrRow}),l("tablecopyrow",{tooltip:"Copy row",command:"mceTableCopyRow",icon:"duplicate-row",onSetup:t.onSetupCellOrRow}),l("tablepasterowbefore",{tooltip:"Paste row before",command:"mceTablePasteRowBefore",icon:"paste-row-before",onSetup:t.onSetupPasteable(tl)}),l("tablepasterowafter",{tooltip:"Paste row after",command:"mceTablePasteRowAfter",icon:"paste-row-after",onSetup:t.onSetupPasteable(tl)}),l("tablecutcol",{tooltip:"Cut column",command:"mceTableCutCol",icon:"cut-column",onSetup:t.onSetupColumn("onAny")}),l("tablecopycol",{tooltip:"Copy column",command:"mceTableCopyCol",icon:"duplicate-column",onSetup:t.onSetupColumn("onAny")}),l("tablepastecolbefore",{tooltip:"Paste column before",command:"mceTablePasteColBefore",icon:"paste-column-before",onSetup:t.onSetupPasteableColumn(ol,"onFirst")}),l("tablepastecolafter",{tooltip:"Paste column after",command:"mceTablePasteColAfter",icon:"paste-column-after",onSetup:t.onSetupPasteableColumn(ol,"onLast")}),l("tableinsertdialog",{tooltip:"Insert table",command:"mceInsertTableDialog",icon:"table"});const r=uo(gt(e));0!==r.length&&e.queryCommandSupported("mceTableToggleClass")&&e.ui.registry.addMenuButton("tableclass",{icon:"table-classes",tooltip:"Table styles",fetch:po(e,r,"tableclass",(t=>e.execCommand("mceTableToggleClass",!1,t))),onSetup:t.onSetupTable});const a=uo(pt(e));0!==a.length&&e.queryCommandSupported("mceTableCellToggleClass")&&e.ui.registry.addMenuButton("tablecellclass",{icon:"table-cell-classes",tooltip:"Cell styles",fetch:po(e,a,"tablecellclass",(t=>e.execCommand("mceTableCellToggleClass",!1,t))),onSetup:t.onSetupCellOrRow}),e.queryCommandSupported("mceTableApplyCellStyle")&&(e.ui.registry.addMenuButton("tablecellvalign",{icon:"vertical-align",tooltip:"Vertical align",fetch:po(e,Kt,"tablecellverticalalign",mo(e,"vertical-align")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellborderwidth",{icon:"border-width",tooltip:"Border width",fetch:po(e,rt(e),"tablecellborderwidth",mo(e,"border-width")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellborderstyle",{icon:"border-style",tooltip:"Border style",fetch:po(e,at(e),"tablecellborderstyle",mo(e,"border-style")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellbackgroundcolor",{icon:"cell-background-color",tooltip:"Background color",fetch:t=>t(bo(e,ft(e),"background-color")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellbordercolor",{icon:"cell-border-color",tooltip:"Border color",fetch:t=>t(bo(e,yt(e),"border-color")),onSetup:t.onSetupCellOrRow})),n("tablecaption",{tooltip:"Table caption",icon:"table-caption",command:"mceTableToggleCaption",onSetup:t.onSetupTableWithCaption}),n("tablerowheader",{tooltip:"Row header",icon:"table-top-header",command:"mceTableRowType",onAction:go(e),onSetup:t.onSetupTableRowHeaders}),n("tablecolheader",{tooltip:"Column header",icon:"table-left-header",command:"mceTableColType",onAction:ho(e),onSetup:t.onSetupTableColumnHeaders})})(e,t),(e=>{const t=ht(e);t.length>0&&e.ui.registry.addContextToolbar("table",{predicate:t=>e.dom.is(t,"table")&&e.getBody().contains(t),items:t,scope:"node",position:"node"})})(e)}))}();