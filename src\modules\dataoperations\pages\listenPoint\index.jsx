// src/pages/DataQueryPage/index.jsx
import React, { useState, useEffect, useCallback } from 'react';
import SearchForm from './searchForm';
import DataTable from './dataTable';
import { message } from 'antd';
import { getLxxMessageRecord, lxxMessageRecordExcel } from '@/modules/dataoperations/api/content';
import dayjs from 'dayjs';

const ListenPoint = () => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [searchParams, setSearchParams] = useState({});
    const [downloadBtnLoading, setDownloadBtnLoading] = useState(false);
    const fetchData = useCallback(async (params) => {
        setLoading(true);
        try {
            const res = await getLxxMessageRecord({
                pageNum: pagination.current,
                pageSize: pagination.pageSize,
                ...params,
            });
            if (res.code == 10001 || res.code == 200) {
                setData(res.data.data);
                setPagination(prev => ({
                    ...prev,
                    total: res.data.totalCount,
                }));
            } else {
                message.error(res.message || '获取数据失败');
            }
        } catch (error) {
            message.error('网络错误，请稍后再试');
        } finally {
            setLoading(false);
        }
    }, [pagination.current, pagination.pageSize]);

    useEffect(() => {
        fetchData(searchParams);
    }, [fetchData, searchParams]);

    const handleSearch = useCallback((values) => {
        setPagination(prev => ({ ...prev, current: 1 }));
        setSearchParams(values);
    }, []);

    const handleReset = useCallback(() => {
        setSearchParams({});
        setPagination(prev => ({ ...prev, current: 1 }));
    }, []);

    const handlePageChange = useCallback((page, pageSize) => {
        setPagination({ ...pagination, current: page, pageSize });
    }, [pagination]);

    const handleDownload = useCallback(async () => {
        message.success('正在准备下载数据...');
        setDownloadBtnLoading(true);
        try {
            const res = await lxxMessageRecordExcel(searchParams);
            const blob = new Blob([res.data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `灵小犀日志数据-${dayjs().format('YYYYMMDDHHmmss')}.xlsx`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            message.success('数据下载成功！');
        } catch (error) {
            message.error('下载失败，请稍后重试');
        } finally {
            setDownloadBtnLoading(false);
        }
    }, [searchParams]);

    return (
        <div style={{ padding: 8 }}>
            <SearchForm onSearch={handleSearch} onReset={handleReset} />
            <DataTable
                dataSource={data}
                pagination={pagination}
                loading={loading}
                onPageChange={handlePageChange}
                onDownload={handleDownload}
                downloadBtnLoading={downloadBtnLoading}
            />
        </div>
    );
};

export default ListenPoint;