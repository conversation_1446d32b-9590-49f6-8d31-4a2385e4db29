/*
 * @Author: 焦质晔
 * @Date: 2023-03-08 16:52:33
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-04-14 16:57:31
 */
import React from 'react';
import classNames from 'classnames';
import { Link } from 'react-router-dom';
import { useLocale, useTool } from '@/hooks';
import { addUrlToken, getSystem, isHttpLink, sleep } from '@/utils';
import { useSelector, useDispatch } from '@/store';
import { setStarMenu } from '@/store/actions';
import { createFlattenMenus } from '@/store/reducers/app';
import { useUpdateEffect } from '@/hooks';
import { useCommonCase } from '../SideMenu/useCommonCase';
import config from '@/config';

import type { AppState, ISideMenu } from '@/store/reducers/app';

import { Menu, Select, Dropdown, Popover, pinyin } from '@jiaozhiye/qm-design-react';
import { StarFilled, SearchOutlined, MoreOutlined } from '@/icons';

import './index.less';

const { Option } = Select;

type IProps = {
  width: number;
  sideMenus: ISideMenu[];
};

const StarMenu: React.FC<IProps> = (props) => {
  const { sideMenus, width } = props;
  const { starMenus } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { t } = useLocale();
  const { openView } = useTool();
  const { createCommonCase } = useCommonCase();

  const [visible, setVisible] = React.useState<boolean>();
  const [openKeys, setOpenKeys] = React.useState<string[]>([]);
  const [searchValue, setSearchValue] = React.useState<string>();
  const options = React.useMemo<ISideMenu[]>(() => createFlattenMenus(sideMenus), [sideMenus]);

  useUpdateEffect(() => {
    setOpenKeys([]);
  }, [sideMenus]);

  const onSelectChange = async (path: string) => {
    setSearchValue(path);
    const item = options.find((x) => x.key === path)!;
    if (isHttpLink(item.caseHref!) && item.target === '_blank') {
      window.open(addUrlToken(item.caseHref!), '_blank');
    } else {
      openView(path);
      createCommonCase(item);
    }
    setVisible(false);
    await sleep(300);
    setSearchValue(undefined);
  };

  const linkClick = (item: ISideMenu, ev) => {
    ev.preventDefault();
    openView(config.isMainApp ? `/${item.system}${item.key}` : item.key);
    createCommonCase(item, true);
  };

  const createPop = (item: ISideMenu) => {
    return (
      <div className={`popup`}>
        <span
          onClick={(ev) => {
            ev.stopPropagation();
            dispatch<any>(
              setStarMenu(
                starMenus.filter((x) => !(x.key === item.key && x.system === item.system))
              )
            );
          }}
        >
          {t('app.sidebar.unstar')}
        </span>
      </div>
    );
  };

  const createMenuItems = () => {
    return starMenus
      .filter((x) => x.system === getSystem())
      .map((x) => ({
        key: x.key,
        label: (
          <>
            <Link
              to={config.isMainApp ? `/${x.system}${x.key}` : x.key}
              onClick={(ev) => linkClick(x, ev)}
            >
              <span>{x.title}</span>
            </Link>
            {config.showStarNav && (
              <Popover
                trigger="hover"
                placement="bottom"
                overlayClassName={`side-menu__popper`}
                content={createPop(x)}
                overlayStyle={{ paddingTop: 5 }}
                destroyTooltipOnHide
              >
                <MoreOutlined
                  className={classNames(`icon`)}
                  onClick={(ev) => ev.stopPropagation()}
                />
              </Popover>
            )}
          </>
        ),
      }));
  };

  const createMenu = () => {
    return [
      {
        key: 'star-nav',
        popupClassName: 'ant-submenu-popup-dark',
        // icon: <StarFilled />,
        label: t('app.sidebar.starNav'),
        children: createMenuItems(),
      },
    ];
  };

  const renderMenus = () => {
    return (
      <Select
        value={searchValue}
        showArrow={false}
        showSearch
        style={{ width: width - 20 }}
        placeholder={t('app.sidebar.allNavPlaceholder')}
        filterOption={(input, option) => {
          const pyt: string = pinyin
            .parse(option!.children)
            .map((v) => {
              if (v.type === 2) {
                return v.target.toLowerCase().slice(0, 1);
              }
              return v.target;
            })
            .join('');
          return `${option!.children}|${pyt}`.toLowerCase().indexOf(input.toLowerCase()) >= 0;
        }}
        onKeyDown={(ev) => ev.stopPropagation()}
        onClick={(ev) => ev.stopPropagation()}
        onChange={onSelectChange}
      >
        {options.map((x) => (
          <Option key={x.key} value={x.key}>
            {x.title}
          </Option>
        ))}
      </Select>
    );
  };

  return (
    <Menu
      className={`star-menu`}
      mode="inline"
      inlineIndent={20}
      selectedKeys={[]}
      openKeys={openKeys}
      expandIcon={
        <Dropdown
          open={visible}
          dropdownRender={() => renderMenus()}
          overlayClassName={`star-menu__popper ant-dropdown-menu`}
          placement="bottomRight"
          trigger={['click']}
          onOpenChange={(visible) => setVisible(visible)}
        >
          <SearchOutlined onClick={(ev) => ev.stopPropagation()} />
        </Dropdown>
      }
      items={createMenu()}
      onOpenChange={(keys) => {
        setOpenKeys(keys);
      }}
    />
  );
};

export default StarMenu;
