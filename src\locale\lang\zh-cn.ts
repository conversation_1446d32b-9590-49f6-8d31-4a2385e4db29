/*
 * @Author: 焦质晔
 * @Date: 2021-02-12 13:18:02
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-02-21 08:24:03
 */
export default {
  name: 'zh-cn',
  app: {
    global: {
      title: '一汽云工作台',
      home: '首页',
      dashboard: '概览',
      emptyText: '暂无数据',
      noMatch: '抱歉，您访问的页面不存在',
      noAuth: '抱歉，您无权限访问此页面',
      errorLoad: '加载失败，请刷新页面后重试',
      leaveText: '数据尚未保存，确认离开吗？',
      selectText: '请选择数据！',
      dictText: '数据字典为空，请刷新当前用例！',
      env: '环境',
    },
    sidebar: {
      allNavTitle: '全部导航',
      allNavPlaceholder: '请输入菜单名称/拼音头',
      usedNav: '常用导航',
      starNav: '我的收藏',
      star: '添加收藏',
      unstar: '取消收藏',
    },
    header: {
      fold: '收起',
      unfold: '展开',
    },
    multiTab: {
      refresh: '刷新页签',
      closeCurrent: '关闭页签',
      closeRight: '关闭右侧',
      closeLeft: '关闭左侧',
      closeOthers: '关闭其它',
    },
    settings: {
      usercenter: '个人中心',
      usersetting: '个人设置',
      customIndex: '自定义首页',
      clearcache: '清理缓存',
      logout: '退出登录',
      admin: '管理员',
    },
    insideLetter: {
      text: '消息中心',
      notice: '通知',
      message: '消息',
      waiting: '待办',
      clearMsg: '清空消息',
      showMore: '查看更多',
    },
    dreadcrumb: {
      location: '位置导航',
    },
    appCenter: {
      text: '应用中心',
    },
    fullSearch: {
      text: '全文检索',
      historySearch: '历史搜索',
    },
    sizeSelect: {
      text: '尺寸设置',
      large: '大尺寸',
      middle: '中等',
      small: '小尺寸',
    },
    langSelect: {
      text: '国际化',
    },
    helperDoc: {
      helpDoc: '帮助文档',
      useManual: '使用手册',
    },
    login: {
      title: '系统登录',
      weChat: '微信登录',
    },
    theme: {
      text: '主题设置',
      color: '主题颜色',
      type: '主题模式',
    },
    customPage: {
      dashboardEdit: '概览页编辑',
      homeEdit: '首页编辑',
    },
    fetch: {
      errorCode: {
        200: '任务已完成，一切顺利',
        201: '数据已创建，一切顺利',
        202: '服务器已经收到你的请求，但还在忙着处理呢，稍安勿躁哦',
        204: '数据已入虚空，删除成功啦',
        400: '你的请求好像有点问题呢，再检查一下吧',
        401: '抱歉，你需要先登录才能继续，记得带上你的通行证',
        403: '抱歉，你没有权限访问这里，赶紧溜走吧',
        404: '这里什么都没有，你可能迷路了',
        406: '这个格式的大饼，服务器消化不了',
        410: '这个资源已经消失不见了，好像去了外太空',
        412: '请求的前置条件弄错了，你得重新来过',
        422: '新对象，抱歉你不是我的菜',
        500: '服务器发生故障，程序猿正在抢修',
        502: '微服务网关故障，程序猿正在抢修',
        503: '服务不可用，先去喝杯茶等等吧',
        504: '网关超时，稍后再试试吧',
      },
      errorText: '请求超时',
      lockText: '锁定请求',
      cancelText: '取消请求',
      fetchError: ['【{title}】出现异常', '如有问题请联系：{leader}'],
      repairText: '报修',
    },
    information: {
      title: '提示信息',
      confirm: '确认进行此操作？',
      success: '设置成功！',
      failed: '设置失败！',
      maxCache: '最多支持 {total} 个菜单项！',
      maxStar: '最多只能收藏 {total} 个菜单！',
    },
    workbench: {
      customNavTitle: '自定义工作台导航',
      customNavDesc: '鼠标拖动可更换位置',
      addNavTitle: '添加工作台导航',
      addNavDesc: '新增子应用到工作台',
      addToNavTitle: '点击添加到导航栏',
      systemCutText: '已成功切换到',
      setDefaultWb: '设置默认角色组',
      caseRanking: '用例排行',
    },
    button: {
      confirm: '确定',
      cancel: '取消',
      close: '关闭',
      submit: '提交',
      remove: '删除',
      add: '新增',
      edit: '编辑',
      modify: '修改',
      details: '详情',
      view: '查看',
      print: '打印',
      export: '导出',
      import: '导入',
      upload: '上传',
      download: '下载',
      search: '搜索',
      reset: '重置',
      save: '保存',
      clear: '清空',
      operation: '操作',
      preview: '预览',
    },
  },
};
