/**
 * 复制文本到剪切板
 * @param {string} text 要复制的文本
 * @param {function} [callback] 复制成功或失败后的回调函数
 * @returns {Promise} 返回一个Promise对象
 */
export const copyToClipboard = (text, callback) => {
  // 返回一个Promise以便可以使用then/catch或async/await
  return new Promise((resolve, reject) => {
    // 创建一个临时的textarea元素
    const textarea = document.createElement('textarea');
    textarea.value = text;
    
    // 设置样式使其不可见
    textarea.style.position = 'fixed';
    textarea.style.left = '-9999px';
    textarea.style.top = '0';
    textarea.style.opacity = '0';
    
    // 添加到DOM中
    document.body.appendChild(textarea);
    
    // 选中文本
    textarea.select();
    textarea.setSelectionRange(0, textarea.value.length); // 对于移动设备
    
    let success = false;
    
    try {
      // 执行复制命令
      success = document.execCommand('copy');
      
      if (success) {
        resolve(text);
        if (typeof callback === 'function') callback(null, text);
      } else {
        throw new Error('复制失败');
      }
    } catch (err) {
      reject(err);
      if (typeof callback === 'function') callback(err);
    } finally {
      // 清理DOM
      document.body.removeChild(textarea);
    }
    
    // 如果浏览器支持navigator.clipboard API，优先使用
    if (!success && navigator.clipboard) {
      navigator.clipboard.writeText(text)
        .then(() => {
          resolve(text);
          if (typeof callback === 'function') callback(null, text);
        })
        .catch(err => {
          reject(err);
          if (typeof callback === 'function') callback(err);
        });
    }
  });
}