/**
 * @Author: 焦质晔
 * @Date: 2019-06-20 10:00:00
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-02-23 09:37:27
 */
const workbenchList = [
  {
    id: '1',
    title: '设计师',
    code: 'w1001',
  },
  {
    id: '2',
    title: 'IT工程师师',
    code: 'w1002',
  },
];

const workbenchList2 = [
  {
    id: '1',
    title: 'shejishi',
    code: 'w1001',
  },
  {
    id: '2',
    title: 'IT gongchegnshi',
    code: 'w1002',
  },
];

const appList = [
  {
    id: '100',
    workbenchCode: 'w1001', // 工作台(角色组) code
    appCode: 'DE-0505_APP_DMS', // 子应用 code
    feEngineeringName: 'dms', // 工程名
    originHost: 'http://localhost:9021/', // 子应用地址
    title: '业务中心', // 子应用名称
    appShortName: '业务', // 子应用简称
    appEngDiscription: 'Business Center', // 英文描述
    appDiscription: '业务中文描述', // 中文描述
    icon: '', // 图标
    type: 1, // 类型 - app -> 1、文件夹 -> 0
    sideState: 'O', // 'O' -> 展开、'C' -> 折叠、'H' -> 隐藏
  },
  {
    id: '200',
    workbenchCode: 'w1002',
    appCode: 'DE-0505_APP_TDS',
    feEngineeringName: 'tds',
    originHost: 'http://localhost:9022/',
    title: '标准中心',
    appShortName: '标准',
    appEngDiscription: 'Standard Center',
    appDiscription: '标准中文描述',
    icon: '',
    type: 0,
    sideState: 'O',
    children: [
      {
        id: '300',
        workbenchCode: 'w1002',
        appCode: 'DE-0505_APP_CHP',
        feEngineeringName: 'chp',
        originHost: 'http://localhost:9025/',
        title: '自定义首页',
        appShortName: '自定义首页',
        appEngDiscription: 'Custom Home Page',
        appDiscription: '自定义首页中文描述',
        icon: '',
        type: 1,
        sideState: 'O',
      },
    ],
  },
  {
    id: '800',
    workbenchCode: 'w1001',
    appCode: 'DE-0505_APP_EMS',
    feEngineeringName: 'ems',
    originHost: 'http://localhost:9026/',
    title: '资源中心',
    appShortName: '资源',
    appEngDiscription: 'Business Center',
    appDiscription: '资源中文描述',
    icon: '',
    type: 1,
    sideState: 'O',
  },
  {
    id: '900',
    workbenchCode: 'w1002',
    appCode: 'DE-0505_APP_BMS',
    feEngineeringName: 'bms',
    originHost: 'http://localhost:9027/',
    title: '销售中心',
    appShortName: '销售',
    appEngDiscription: 'Business Center',
    appDiscription: '销售中文描述',
    icon: '',
    type: 1,
    sideState: 'O',
  },
];

const appList2 = [
  {
    id: '100',
    workbenchCode: 'w1001',
    appCode: 'DE-0505_APP_DMS',
    feEngineeringName: 'dms',
    originHost: 'http://localhost:9021/',
    title: 'ywzx',
    appShortName: 'yw',
    appEngDiscription: 'Business Center',
    appDiscription: '英文描述',
    icon: '',
    type: 1,
  },
  {
    id: '200',
    workbenchCode: 'w1002',
    appCode: 'DE-0505_APP_TDS',
    feEngineeringName: 'tds',
    originHost: 'http://localhost:9022/',
    title: 'bzzx',
    appShortName: 'bz',
    appEngDiscription: 'Standard Center',
    appDiscription: '英文描述',
    icon: '',
    type: 0,
    children: [
      {
        id: '300',
        appCode: 'DE-0505_APP_CHP',
        feEngineeringName: 'chp',
        originHost: 'http://localhost:9025/',
        title: 'custom index',
        appShortName: 'custom index',
        appEngDiscription: 'Custom Home Page',
        appDiscription: '英文描述',
        icon: '',
        type: 1,
      },
    ],
  },
];

const sideMenus = [
  {
    id: '100',
    appCode: 'DE-0505_APP_DMS',
    feEngineeringName: 'dms',
    children: [
      {
        id: '1',
        appCode: 'DE-0505_APP_DMS',
        caseCode: null,
        feEngineeringName: null,
        icon: 'http://127.0.0.1:3000/menu-icon.svg',
        title: '备件管理', // 菜单名称
        children: [
          {
            id: '2',
            appCode: 'DE-0505_APP_DMS',
            caseCode: null,
            feEngineeringName: null,
            icon: '',
            title: '采购管理',
            children: [
              {
                id: '3',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'spa1001', // 用例号
                feEngineeringName: 'dms',
                icon: '',
                title: '备件采购订单',
              },
              {
                id: '4',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'spa1002',
                feEngineeringName: 'dms',
                icon: '',
                title: '备件采购入库',
                keepAlive: 0,
              },
              {
                id: '5',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'spa1003',
                feEngineeringName: 'dms',
                icon: '',
                title: '备件采购退库',
                // target: '_blank',
                caseHref: 'http://localhost:9021/iframe/spa1003',
              },
            ],
          },
        ],
      },
      {
        id: '6',
        appCode: 'DE-0505_APP_DMS',
        caseCode: null,
        feEngineeringName: null,
        icon: 'http://127.0.0.1:3000/menu-icon.svg',
        title: '销售管理',
        children: [
          {
            id: '7',
            appCode: 'DE-0505_APP_DMS',
            caseCode: null,
            feEngineeringName: null,
            icon: '',
            title: '线索管理',
            children: [
              {
                id: '8',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'sal1001',
                feEngineeringName: 'dms',
                icon: '',
                title: '线索分配',
              },
              {
                id: '9',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'sal1002',
                feEngineeringName: 'dms',
                icon: '',
                title: '线索记录',
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: '200',
    appCode: 'DE-0505_APP_TDS',
    feEngineeringName: 'tds',
    children: [
      {
        id: '10',
        appCode: 'DE-0505_APP_TDS',
        caseCode: null,
        feEngineeringName: null,
        icon: 'http://127.0.0.1:3000/menu-icon.svg',
        title: '客服管理',
        children: [
          {
            id: '11',
            appCode: 'DE-0505_APP_TDS',
            caseCode: null,
            feEngineeringName: null,
            icon: '',
            title: '回访管理',
            children: [
              {
                id: '12',
                appCode: 'DE-0505_APP_TDS',
                caseCode: 'car1001',
                feEngineeringName: 'tds',
                icon: '',
                title: '销售回访',
              },
              {
                id: '13',
                appCode: 'DE-0505_APP_TDS',
                caseCode: 'car1002',
                feEngineeringName: 'tds',
                icon: '',
                title: '回访分配',
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: '300',
    appCode: 'DE-0505_APP_CHP',
    feEngineeringName: 'chp',
    children: [
      {
        id: '31',
        appCode: 'DE-0505_APP_CHP',
        caseCode: 'chp001',
        feEngineeringName: 'chp',
        icon: '',
        title: '首页布局',
      },
    ],
  },
];

const sideMenus2 = [
  {
    id: '100',
    appCode: 'DE-0505_APP_DMS',
    feEngineeringName: 'dms',
    children: [
      {
        id: '1',
        appCode: 'DE-0505_APP_DMS',
        caseCode: null,
        feEngineeringName: null,
        icon: 'http://127.0.0.1:3000/menu-icon.svg',
        title: 'bjgl', // 菜单名称
        children: [
          {
            id: '2',
            appCode: 'DE-0505_APP_DMS',
            caseCode: null,
            feEngineeringName: null,
            icon: '',
            title: 'cggl',
            children: [
              {
                id: '3',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'spa1001', // 用例号
                feEngineeringName: 'dms',
                icon: '',
                title: 'bjcgdd',
              },
              {
                id: '4',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'spa1002',
                feEngineeringName: 'dms',
                icon: '',
                title: 'bjcgrk',
                keepAlive: 0,
              },
              {
                id: '5',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'spa1003',
                feEngineeringName: 'dms',
                icon: '',
                title: 'bjcgtk',
                // target: '_blank',
                caseHref: 'http://localhost:9021/iframe/spa1003',
              },
            ],
          },
        ],
      },
      {
        id: '6',
        appCode: 'DE-0505_APP_DMS',
        caseCode: null,
        feEngineeringName: null,
        icon: 'http://127.0.0.1:3000/menu-icon.svg',
        title: 'xsgl',
        children: [
          {
            id: '7',
            appCode: 'DE-0505_APP_DMS',
            caseCode: null,
            feEngineeringName: null,
            icon: '',
            title: 'xsgl',
            children: [
              {
                id: '8',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'sal1001',
                feEngineeringName: 'dms',
                icon: '',
                title: 'xsfp',
              },
              {
                id: '9',
                appCode: 'DE-0505_APP_DMS',
                caseCode: 'sal1002',
                feEngineeringName: 'dms',
                icon: '',
                title: 'xsjl',
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: '200',
    appCode: 'DE-0505_APP_TDS',
    feEngineeringName: 'tds',
    children: [
      {
        id: '10',
        appCode: 'DE-0505_APP_TDS',
        caseCode: null,
        feEngineeringName: null,
        icon: 'http://127.0.0.1:3000/menu-icon.svg',
        title: 'kfgl',
        children: [
          {
            id: '11',
            appCode: 'DE-0505_APP_TDS',
            caseCode: null,
            feEngineeringName: null,
            icon: '',
            title: 'hfgl',
            children: [
              {
                id: '12',
                appCode: 'DE-0505_APP_TDS',
                caseCode: 'car1001',
                feEngineeringName: 'tds',
                icon: '',
                title: 'xshf',
              },
              {
                id: '13',
                appCode: 'DE-0505_APP_TDS',
                caseCode: 'car1002',
                feEngineeringName: 'tds',
                icon: '',
                title: 'hffp',
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: '300',
    appCode: 'DE-0505_APP_CHP',
    feEngineeringName: 'chp',
    children: [
      {
        id: '31',
        appCode: 'DE-0505_APP_CHP',
        caseCode: 'chp001',
        feEngineeringName: 'chp',
        icon: '',
        title: 'index layout',
      },
    ],
  },
];

export default {
  [`zh-cn`]: { workbenchList, appList: [appList[0]], sideMenus: [sideMenus[0]] },
  [`en`]: { workbenchList: workbenchList2, appList: [appList2[0]], sideMenus: [sideMenus2[0]] },
};
