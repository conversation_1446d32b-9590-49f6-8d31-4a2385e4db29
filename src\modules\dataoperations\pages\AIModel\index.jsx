import React, { useEffect, useRef, useState } from 'react';
import css from './index.module.less';
import { Button, Col, Form, Input, QmTable, Row, Select } from '@jiaozhiye/qm-design-react';
import { Api } from '@/modules/dataoperations/api/AiScene';
import Insert from './insert';
import NormalTable from '@/modules/dataoperations/pages/normalTable';

export const Index = (props) => {
  const [form] = Form.useForm();

  const [param, setParam] = useState({});

  const [columns, setColumns] = useState([]);

  const [data, setData] = useState({});

  const [showModal, setShowModal] = useState(false);

  const [curRow, setCurRow] = useState(null);

  const tableRef = useRef(null);

  const edit = (row) => {
    setCurRow(row)
    setShowModal(true)
  }

  const createColumn = () => {
    return [
      {
        title: 'AI审核模型ID',
        dataIndex: 'id'
      },
      {
        title: 'AI审核模型名称',
        dataIndex: 'auditModelName',
      },
      {
        title: '审核点',
        dataIndex: 'aiAuditModelPoint',
        render: (text, row) => {
          return row.aiAuditModelPointList?.map(it => it.auditPointName).join(',')
        }
      },
      {
        title: '技术模型',
        dataIndex: 'aiAuditModelTechnology',
        render: (text, row) => {
          return row.aiAuditModelTechnologyList?.map(it => it.technologyModelName).join(',')
        }
      },
      {
        title: '创建时间',
        dataIndex: 'createTime'
      },
      {
        title: '操作',
        dataIndex: '__action__',
        fixed: 'right',
        render: (text, row) => {
          return <Button type={'link'} onClick={() => edit(row)}>编辑</Button>
        }
      },
    ]
  }

  const reset = () => {
    setParam({})
    form.resetFields()
    tableRef.current.onSearch()
  }

  const search = () => {
    const value = form.getFieldsValue(true)
    setParam(value)
    tableRef.current.onSearch()
  }

  const insert = (row) => {
    if (row) {
      setCurRow(row);
    }
    setShowModal(true);
  };

  useEffect(() => {
    setColumns(createColumn());
  }, []);

  return (
    <div className={css.pageWrapper}>
      <div className={css.pageTitle}>AI审核模型</div>
      <Form form={form}>
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item name={'auditModelName'} label={'AI审核模型名称'}>
              <Input placeholder={'请输入'} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Button type={'primary'} onClick={() => search()}>
              搜索
            </Button>
            <Button style={{ marginLeft: '16px' }} onClick={() => reset()}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
      <NormalTable ref={tableRef} columns={columns} listApi={Api.getAiAuditModelList} param={param} data={'list'}
                   totalName={'totalCount'}>
        <Button type={'primary'} onClick={insert}>
          新建
        </Button>
      </NormalTable>
      <Insert
        visible={showModal}
        onClose={(val) => {
          setShowModal(false);
          setCurRow(null);
          if (val) {
            search();
          }
        }}
        row={curRow}
      />
    </div>
  );
};
export default Index;


