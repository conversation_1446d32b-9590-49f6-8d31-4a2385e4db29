import React, { useEffect, useRef ,useState } from 'react'
import { QmForm , QmSpin } from '@jiaozhiye/qm-design-react'
import { useChart } from '@/charts';
import { aiTrendChard } from '@/modules/dataoperations/api'
import  moment from 'moment'
 const Trend = ()=> {
  const refFrom = useRef(null)
  const chartRef = useRef(null)
  const [params,setParams] = useState({totalType:'1'})
  const { setOptions } = useChart(chartRef);
  const [loading,setLoading] = useState(false)
  const createOptios = (data = [],totalType) => {
    const color = ['#FAC858','#91CC75','#2151D1']
    let xAxis = []
    if(totalType == '1'){
       xAxis = data[0]?.rateAndDateVOList?.map(item=>item.billDateBegin.slice(0, 10)) ?? []
    }
     if(totalType == '2'){
      xAxis = data[0]?.rateAndDateVOList?.map(item=>`${moment(item.billDateBegin).week()}周`) ?? []
    }
    if(totalType == '3'){
      xAxis = data[0]?.rateAndDateVOList?.map(item=>item.billDateBegin.slice(0, 7)) ?? []
    }
    const series = data.map((item,index)=>({
      name: item.systemName,
      type: 'line',
      stack:item.systemName,
      showAllSymbol: true,
      symbol: 'emptyCircle',
      symbolSize: 8,
      itemStyle: {
         color:color[index] 
      },
      data:item.rateAndDateVOList?.map(i=>i.billRate)
  }))
    return {
        tooltip: {
            trigger: 'axis',
            backgroundColor: '#fff',
            axisPointer: {
                type: 'shadow',
                label: {
                    show: true,
                    backgroundColor: '#7B7DDC'
                }
            }
        },
        legend: {
            data: data.map(item=>item.systemName),
            right:'0',
            top:'0',
        },
        grid: {
            top: '12%',
            left:'2%',
            right:'4%',
            bottom: '12%',
        },
        xAxis: {
            data:xAxis,
            axisLine: {
                lineStyle: {
                    color: '#F0F0F0'
                }
            },
            axisLabel: {
                color:' #525252',
                rotate: -20, 
                margin: 15, 
            },
            axisTick: {
                show: false,
            },
        },
        yAxis: [
            {
                splitLine: { show: false }
            },
            {
                splitLine: { show: true }
            }
        ],
        series: series
    }
}

useEffect(()=>{
  refFrom.current?.SET_FIELDS_VALUE({ totalType:params.totalType })
 },[])

 useEffect(()=>{
  aiTrendChardFn(params)
 },[params])

 const aiTrendChardFn = async(params)=>{
  try {
    setLoading(true)
    const res = await aiTrendChard(params)
    setLoading(false)
    if(res.code == 200){
      setOptions(createOptios(res.data ?? [],params.totalType), true)
    }else{
      setOptions(createOptios([],params.totalType), true)
    }
  }catch{
    setLoading(false)
  }
}

  return (
    <>
      <QmForm
        ref={refFrom}
        formType='search'
        layout ='vertical'
        isFieldsDefine={false}
        items={[
          {
            type: 'RANGE_DATE',
            label: '人工审核日期',
            placeholder: '请选择',
            disabled: params.totalType && params.totalType != '1',
            fieldName: 'time',
          },
          {
            type: 'SELECT',
            label: '时间维度',
            placeholder: '请选择',
            fieldName: 'totalType',
            options: {
              itemList:[
                {
                text:'日',
                value:'1'
               },
               {
                text:'周',
                value:'2'
               },
               {
                text:'月',
                value:'3'
               }
            ]
            },
            onChange(value){
              refFrom.current?.SET_FIELDS_VALUE({ time:[] })
              refFrom.current?.SET_FORM_ITEM('time', {
                'disabled': value !='1'
              })
            }
          }
        ]}
        cols={4}
        onFinish = {(formValues)=>{
         const[ humanCheckTimeStart,humanCheckTimeEnd ]= formValues.time ?? []
         setParams({
          totalType:formValues.totalType,humanCheckTimeStart,humanCheckTimeEnd
         })
        }}
      />
      <div style={{width:'100%',height:'380px',border:'1px solid #E4E4E7',padding:'20px',marginTop:'16px',borderRadius:'4px'}}>
         <QmSpin size="small" spinning={loading} fullHeight={true}>
            <div style={{width:'100%',height:'100%'}} ref={ chartRef } ></div>
          </QmSpin>
      </div>
    </>
  )
}

export default Trend