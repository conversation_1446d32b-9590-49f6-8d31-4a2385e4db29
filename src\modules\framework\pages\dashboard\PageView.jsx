/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-01-20 21:48:53
 */
import React from 'react';
import classNames from 'classnames';
import { useLocale } from '@/hooks';
import config from '@/config';

import { ToCustomPage } from '@/components';

// import Chart1 from './charts/Chart1';
// import Chart2 from './charts/Chart2';

import css from './index.module.less';

const PageView = () => {
  const { t } = useLocale();
  return (
    <div className={classNames(css['dashboard-bg'])}>
      <ToCustomPage title={t('app.customPage.dashboardEdit')} search={`?appCode=${config.code}`} />
      {/* <Chart1 fetch={{ api: getTableData, params, dataKey: '' }} style={{ height: 300 }} /> */}
      {/* <Chart2 fetch={{ api: getTableData, params, dataKey: '' }} style={{ height: 300 }} /> */}
      {process.env.ENV_CONFIG !== 'prod' && (
        <h4>
          {t('app.global.title')} - <span>{process.env.ENV_CONFIG?.toUpperCase()} </span>
          {t('app.global.env')}
        </h4>
      )}
    </div>
  );
};

export default PageView;
