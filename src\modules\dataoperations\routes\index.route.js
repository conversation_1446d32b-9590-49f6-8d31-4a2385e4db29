import { lazy } from 'react';

export default {
  // webpackChunkName -> webpack 在打包编译时，生成的文件路径(名)，格式：模块名称/用例名称 service/spt1001
  routes: [
    {
      path: '/operations',
      meta: { keepAlive: true },
      component: lazy(() => import(/* webpackChunkName: "test/demo" */ '../pages/operations')),
    },
    {
      path: '/chat',
      meta: { keepAlive: true },
      component: lazy(() => import(/* webpackChunkName: "test/demo" */ '../pages/chat')),
    },
    {
      path: '/chatRobot',
      meta: { keepAlive: true },
      component: lazy(() => import(/* webpackChunkName: "test/demo" */ '../pages/robot')),
    },
    {
      path: '/AIScene',
      meta: { keepAlive: true },
      component: lazy(() => import(/* webpackChunkName: "test/demo" */ '../pages/AIScene')),
    },
    {
      path: '/AIModel',
      meta: { keepAlive: true },
      component: lazy(() => import(/* webpackChunkName: "test/demo" */ '../pages/AIModel')),
    },
    {
      path: '/listenPoint',
      meta: { keepAlive: true },
      component: lazy(() => import(/* webpackChunkName: "test/demo" */ '../pages/listenPoint')),
    }
  ],
  public: [],
};
