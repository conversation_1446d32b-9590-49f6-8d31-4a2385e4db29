/*
 * @Author: 焦质晔
 * @Date: 2021-07-14 15:49:32
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-01-04 09:24:07
 */
.app-theme-setting {
  height: 100%;
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 4px;
    transition: all 0.3s ease;
    cursor: pointer;
    &:hover {
      background-color: rgba(0, 0, 0, 0.045);
    }
  }
  .icon {
    color: @textColorSecondary;
    font-size: 18px;
    cursor: pointer;
  }
}

.theme-setting__popper {
  .ant-dropdown-menu {
    padding: 5px 10px;
    .themeColor {
      overflow: hidden;
      .title {
        color: @textColor;
        line-height: 22px;
        margin-bottom: @moduleMargin;
      }
      .color-block {
        width: 20px;
        height: 20px;
        border-radius: 2px;
        float: left;
        cursor: pointer;
        margin: 0 8px 6px 0;
        text-align: center;
        color: #fff;
        font-weight: bold;
      }
      .color-type-item {
        position: relative;
        width: 44px;
        height: 36px;
        margin-right: 15px;
        overflow: hidden;
        background-color: #f0f2f5;
        border-radius: 4px;
        box-shadow: 0 1px 2.5px 0 rgb(0 0 0 / 18%);
        cursor: pointer;
        &::before {
          position: absolute;
          top: 0;
          left: 0;
          width: 33%;
          height: 100%;
          background-color: #fff;
          content: '';
        }
        &::after {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 25%;
          background-color: #fff;
          content: '';
        }
      }
      .item-light {
        &::before {
          background-color: @siderBackgroundColor;
          z-index: 1;
        }
        &::after {
          background-color: #fff;
        }
      }
      .item-dark {
        background-color: rgba(0, 21, 41, 0.8);
        &::before {
          background-color: @siderBackgroundColor;
        }
        &::after {
          background-color: @siderBackgroundColor;
        }
      }
      .selectIcon {
        position: absolute;
        right: 8px;
        bottom: 6px;
        font-size: @textSize;
        font-weight: 700;
        color: @primaryColor;
        pointer-events: none;
      }
    }
  }
}
