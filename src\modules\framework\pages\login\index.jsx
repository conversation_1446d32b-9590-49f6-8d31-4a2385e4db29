/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-10-30 11:09:04
 */
import React from 'react';
import classNames from 'classnames';
import { JSEncrypt } from 'jsencrypt';
import { useDispatch } from '@/store';
import { useTool } from '@/hooks';
import { createSignIn, createWorkbench } from '@/store/actions';
import { setToken, setUserInfo, removeToken } from '@/utils/cookies';
import { doLogin, getPublicKey } from '@framework/api/login';
import { t } from './lang';
import SettingPassword from './pwd';

import { Form, Input, Divider, QmButton, QmModal } from '@jiaozhiye/qm-design-react';

import css from './index.module.less';
import logo from './assets/login_logo.png';

// 密码加密
const setEncrypt = (publicKey, str) => {
  const jsencrypt = new JSEncrypt();
  jsencrypt.setPublicKey(publicKey);
  return jsencrypt.encrypt(str);
};

const Login = (props) => {
  const formRef = React.useRef();

  const { openView } = useTool();
  const dispatch = useDispatch();
  const [visibleSettingPassword, setVisibleSettingPassword] = React.useState(false);
  const [pwdInfo, setPwdInfo] = React.useState({});

  const onLoginUcg = () => {
    // ...
  };

  const onLoginKeyClock = () => {
    // ...
  };

  const closeModal = () => {
    setVisibleSettingPassword(false);
  };

  const clearPassword = () => {
    formRef.current.setFieldsValue({
      passWord: '',
    });
    removeToken();
  };

  const loginHandle = async () => {
    try {
      const data = await formRef.current.validateFields();
      const publicKey = await getPublicKey();
      if (publicKey) {
        const res = await doLogin({
          userName: data.userName,
          passWord: setEncrypt(publicKey, data.passWord),
        });
        if (res.code === 200) {
          setToken(res.data.token);
          const { id, name, workbenchGroupCode } = res.data.userInfo;
          setUserInfo(res.data.userInfo);
          dispatch(
            createSignIn({
              id: id,
              name: name,
            })
          );
          dispatch(createWorkbench(workbenchGroupCode ?? ''));
          setTimeout(() => {
            const redirect = props.location.search.split('=')[1] || '/home';
            openView(redirect);
          });
        } else if (res.code === 995) {
          const { token, businessKey } = res.data;
          setPwdInfo({ token, businessKey });
          setVisibleSettingPassword(true);
        }
      }
    } catch (err) {
      // ...
    }
  };

  return (
    <div className={classNames('app-login', css.login_wrapper)}>
      <div className={classNames(css.left)}>
        <div className={classNames(css.logo)}>
          <img src={logo} height="50" alt="" />
        </div>
      </div>
      <div className={classNames(css.right)}>
        <div className={classNames(css.main)}>
          <h4 className={classNames(css.title)}></h4>
          <div className={classNames(css.container)}>
            <Form
              ref={formRef}
              labelCol={{ span: 0 }}
              wrapperCol={{ span: 24 }}
              className={classNames(css.login_form)}
            >
              <Form.Item
                name="userName"
                rules={[{ required: true, message: t('account.loginName') }]}
              >
                <Input placeholder={t('account.loginName')} />
              </Form.Item>
              <Form.Item
                name="passWord"
                rules={[{ required: true, message: t('account.loginPwd') }]}
                onKeyUp={(ev) => {
                  if (ev.keyCode === 13) {
                    loginHandle();
                  }
                }}
              >
                <Input.Password placeholder={t('account.loginPwd')} />
              </Form.Item>
              <div className={css.forget_pwd}>
                <a href="#">{t('account.forgotPwd')}</a>
              </div>
              <Form.Item>
                <QmButton
                  size="large"
                  type="primary"
                  className={classNames(css.btn)}
                  click={loginHandle}
                >
                  {t('account.signIn')}
                </QmButton>
                <div className={classNames(css.tooltip)}>{t('account.suggestion')}</div>
              </Form.Item>
            </Form>
            <Divider plain className={classNames(css.divider)}>
              {t('login.methods')}
            </Divider>
            <div style={{ textAlign: 'center' }}>
              <QmButton onClick={onLoginUcg}>{t('login.ucg')}</QmButton>
              <QmButton onClick={onLoginKeyClock} style={{ marginLeft: 10 }}>
                {t('login.keyClock')}
              </QmButton>
            </div>
          </div>
        </div>
      </div>
      <QmModal
        visible={visibleSettingPassword}
        title={t('account.changePwd')}
        showFullScreen={false}
        closable={false}
      >
        <SettingPassword
          onClose={closeModal}
          isForce={true}
          clearPassword={clearPassword}
          pwdInfo={pwdInfo}
        />
      </QmModal>
    </div>
  );
};

export default Login;
