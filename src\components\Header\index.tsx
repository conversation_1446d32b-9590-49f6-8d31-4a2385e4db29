/*
 * @Author: 焦质晔
 * @Date: 2022-04-20 18:00:57
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-06-09 15:00:21
 */
import React from 'react';
import classNames from 'classnames';
import { xor } from 'lodash-es';
import { useSelector } from '@/store';
import { useSize, useLocale, useUpdateEffect } from '@/hooks';
import { local } from '@/utils/storage';
import config from '@/config';

import type { AppState } from '@/store/reducers/app';

import './index.less';

import Logo from '../Logo';
import SizeSetting from '@/layout/modules/SizeSetting';
import LangSetting from '@/layout/modules/LangSetting';
import ThemeSetting from '@/layout/modules/ThemeSetting';
import NavSetting from '../NavSetting';
import NavList from '../NavList';
import WorkbenchSetting from '../WorkbenchSetting';
import MessageCenter from '../MessageCenter';
import UserCenter from '../UserCenter';
import ActionSetting from '../ActionSetting';

type IModuleItem = {
  key: string;
  name: string;
  visible: boolean;
  component: any;
};

const Header: React.FC = () => {
  const { lang } = useSelector((state: AppState) => state.app);
  const { t } = useLocale();
  const size = useSize();

  const MODULES: IModuleItem[] = [
    {
      key: 'MessageCenter',
      name: t('app.insideLetter.text'),
      visible: config.showNotification,
      component: MessageCenter,
    },
    {
      key: 'SizeSetting',
      name: t('app.sizeSelect.text'),
      visible: config.showSizeSelect,
      component: SizeSetting,
    },
    {
      key: 'LangSetting',
      name: t('app.langSelect.text'),
      visible: config.showLangSelect,
      component: LangSetting,
    },
    {
      key: 'ThemeSetting',
      name: t('app.theme.text'),
      visible: config.showCustomTheme,
      component: ThemeSetting,
    },
  ];

  useUpdateEffect(() => {
    setModuleList(MODULES);
  }, [lang]);

  const createModules = () => {
    const localData = local.getItem('toolbar_setting');
    if (Array.isArray(localData)) {
      if (
        xor(
          localData.map((x) => x.key),
          MODULES.map((x) => x.key)
        ).length
      ) {
        local.removeItem('toolbar_setting');
        return MODULES;
      }
      return localData.map((x) => Object.assign(MODULES.find((k) => k.key === x.key)!, x));
    }
    return MODULES;
  };

  const [moduleList, setModuleList] = React.useState<IModuleItem[]>(createModules());

  const cls = {
    [`app-header`]: !0,
    [`app-header__sm`]: size === 'small',
    [`app-header__lg`]: size === 'large',
  };

  return (
    <header className={classNames(cls)}>
      <div className={`logo`}>
        <Logo width={170} />
        {config.isMainApp && <WorkbenchSetting />}
      </div>
      <div className={`nav`}>
        <dl>{config.isMainApp && <NavSetting />}</dl>
        <NavList />
      </div>
      <div className={`setting`}>
        {moduleList
          .filter((x) => x.visible)
          .map((x) => React.createElement(x.component, { key: x.key }))}
        <UserCenter />
        <ActionSetting items={moduleList} onChange={(list) => setModuleList(list)} />
      </div>
    </header>
  );
};

export default Header;
