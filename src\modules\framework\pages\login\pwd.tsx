/*
 * @Author: 焦质晔
 * @Date: 2023-10-30 11:09:06
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-10-30 11:09:31
 */
import React from 'react';
import { Comment, QmForm, QmButton, QmSpace } from '@jiaozhiye/qm-design-react';
import { Message } from '@/utils';
import classNames from 'classnames';
import { getUserInfo } from '@/utils/cookies';
import { JSEncrypt } from 'jsencrypt';
import { t } from './lang';

import css from './index.module.less';
import { getUpdatePwd, getPublicKey, forceUupdatePwd, passwordRegex } from '@framework/api/login';

// 密码加密
const setEncrypt = (publicKey, str) => {
  const jsencrypt = new JSEncrypt();
  jsencrypt.setPublicKey(publicKey);
  return jsencrypt.encrypt(str);
};

type IState = {
  formItems: any[];
  backgroundColor: string;
  password: string;
};

class SettingPassword extends React.Component<any, IState> {
  public formRef;

  state: IState = {
    backgroundColor: '',
    formItems: this.createFormList(),
    password: '',
  };

  componentDidMount() {
    // 获取密码校验信息
    this.getPasswordRegex();
  }

  //表单信息
  createFormList() {
    return [
      {
        type: 'INPUT',
        label: `${t('userCenter.newPwd')}`,
        fieldName: 'password',
        rules: [
          {
            required: true,
          },
          {
            validator: (rule, value) => {
              const regExp = /(?!^[0-9]+$)(?!^[A-z]+$)(?!^[^A-z0-9]+$)^[^\s\u4e00-\u9fa5]{8,30}$/;
              if (!value) {
                return Promise.reject();
              } else if (value.length < 8) {
                return Promise.reject(t('userCenter.lengthTip'));
              } else if (value.length > 30) {
                return Promise.reject(t('userCenter.lengthTip'));
              } else if (regExp.test(value)) {
                return Promise.resolve();
              }
              return Promise.reject(t('userCenter.regTip'));
            },
          },
        ],
        options: {
          password: true,
        },
        onChange: (value) => {
          this.setState({ password: value });
        },
      },
      {
        type: 'INPUT',
        label: `${t('userCenter.confirmPwd')}`,
        fieldName: 'confirmPwd',
        rules: [
          {
            required: true,
          },
          {
            validator: (rule, value) => {
              const password0 = this.state.password;
              if (!value || password0 === value) {
                //正确
                return Promise.resolve();
              }
              // 异常返回
              return Promise.reject(t('userCenter.confirmTip'));
            },
          },
        ],
        options: {
          password: true,
        },
      },
      {
        type: 'INPUT',
        label: `${t('userCenter.originalPwd')}`,
        fieldName: 'oldPwd',
        rules: [
          {
            required: true,
          },
          {
            validator: (rule, value) => {
              const password0 = this.state.password;
              if (!value || password0 !== value) {
                // 正常返回
                return Promise.resolve();
              }

              // 异常返回
              return Promise.reject(t('userCenter.differentTip'));
            },
          },
        ],
        options: {
          password: true,
        },
      },
    ];
  }

  // 关闭drawer
  cancelHandle = () => {
    this.props.onClose();
  };

  // 保存
  saveHandle = async () => {
    const [err, data] = await this.formRef.GET_FORM_DATA();
    if (err) return;
    // 增加当前用户的ID
    const param = Object.assign({ id: getUserInfo().id }, data);
    // 密码，确认密码 旧密码 加密
    const publicKey = await getPublicKey({});
    param.password = setEncrypt(publicKey, param.password);
    param.confirmPwd = setEncrypt(publicKey, param.confirmPwd);
    param.oldPwd = setEncrypt(publicKey, param.oldPwd);
    // 提交保存
    const res = await getUpdatePwd(param);
    if (res.code === 200) {
      Message(res.data.message || t('app.information.success'), 'success');
      this.cancelHandle();
    }
  };

  // 强制修改保存
  forceSaveHandle = async () => {
    try {
      const [err, data] = await this.formRef.GET_FORM_DATA();
      if (err) return;

      const { pwdInfo } = this.props;
      // 增加当前用户的ID
      const param = Object.assign({}, data, {
        token: pwdInfo.token,
        id: pwdInfo.businessKey,
      });
      // 密码，确认密码 旧密码 加密
      const publicKey = await getPublicKey({});
      param.password = setEncrypt(publicKey, param.password);
      param.confirmPwd = setEncrypt(publicKey, param.confirmPwd);
      param.oldPwd = setEncrypt(publicKey, param.oldPwd);
      param.id = setEncrypt(publicKey, param.id);

      // 提交保存
      const res = await forceUupdatePwd(param);
      if (res.code === 200) {
        Message(res.data.message || t('app.information.success'), 'success');
        this.cancelHandle();
        this.props.clearPassword();
      }
    } catch (err) {
      //...
    }
  };

  getPasswordRegex = async () => {
    if (process.env.MOCK_DATA !== 'true') {
      try {
        const res = await passwordRegex();
        if (res.code === 200) {
          this.formRef.SET_FORM_ITEM('password', {
            'rules[1].validator': (rule, value) => {
              const regExp = new RegExp(res.data.regex);
              if (!value) {
                return Promise.reject();
              } else if (regExp.test(value)) {
                return Promise.resolve();
              }
              return Promise.reject(res.data.message);
            },
          });
        }
      } catch (err) {
        // ...
      }
    }
  };

  render() {
    const { formItems } = this.state;
    const { isForce } = this.props;
    return (
      <div>
        <Comment
          content={
            <p className={classNames(css.comment)}>
              <span className={classNames(css.title)}>{t('userCenter.warning')}：</span>
              <br />
              {t('userCenter.warningContent')}
              {isForce && <span>{t('userCenter.modifiedSuccess')}</span>}
            </p>
          }
        />
        <QmForm
          ref={(ref) => (this.formRef = ref)}
          labelWidth={150}
          formType={'default'}
          cols={1}
          items={formItems}
          fieldsChange={(items) => this.setState({ formItems: items })}
        />
        <QmSpace className={`fixed-footer`}>
          {!isForce ? (
            <>
              <QmButton onClick={() => this.cancelHandle()}> {t('app.button.cancel')}</QmButton>
              <QmButton type="primary" click={() => this.saveHandle()}>
                {t('app.button.confirm')}
              </QmButton>
            </>
          ) : (
            <QmButton type="primary" click={() => this.forceSaveHandle()}>
              {t('app.button.confirm')}
            </QmButton>
          )}
        </QmSpace>
      </div>
    );
  }
}

export default SettingPassword;
