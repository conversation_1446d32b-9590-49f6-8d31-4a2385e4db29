/*
 * @Author: 焦质晔
 * @Date: 2023-11-18 16:25:05
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-19 01:39:27
 */
import React from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from '@/store';
import { createWorkbench, createMicroMenu, createIframeMenu } from '@/store/actions';
import { isHttpLink } from '@/utils';
import { useLocale, useTool } from '@/hooks';
import { useCustomNavs } from '../NavSetting/useCustomNavs';
import { MOUSE_ENTER_DELAY, MOUSE_LEAVE_DELAY } from './PopperNode';
import config from '@/config';

import type { AppState, INavMenu } from '@/store/reducers/app';
import type { TimeoutHandle } from '@/utils/types';

export const deepGetKeyPath = (items: INavMenu[], value: string) => {
  for (let i = 0; i < items.length; i++) {
    if (items[i].key.startsWith(value)) {
      return [items[i].id];
    }
    if (Array.isArray(items[i].children)) {
      const temp = deepGetKeyPath(items[i].children!, value);
      if (temp) {
        return [items[i].id, temp].flat();
      }
    }
  }
};

export const useNavCore = (tabsWrapperRef: React.RefObject<HTMLDivElement>) => {
  const { workbenchList, workbench, microMenus, lang } = useSelector(
    (state: AppState) => state.app
  );
  const dispatch = useDispatch();
  const { openView } = useTool();
  const { t } = useLocale();
  const { customNavList } = useCustomNavs();
  const { pathname } = useLocation();

  const createNavList = () => {
    if (!config.isMainApp) {
      return customNavList.filter((x) => !x.hideInNav);
    }
    // const results: INavMenu[] = [];
    // workbenchList.forEach((x) => {
    //   if (x.code === workbench) {
    //     results.push(...customNavList.filter((k) => !k.hideInNav && k.wbCode === x.code));
    //   } else {
    //     results.push({ ...x, key: '', type: 2 }); // 2 -> 角色组
    //   }
    // });
    // return results; // 角色组展示在导航上
    return customNavList.filter((x) => !x.hideInNav);
  };

  const navMenus = React.useMemo<INavMenu[]>(() => {
    return [
      {
        id: '-',
        key: '/home',
        title: t('app.global.home'),
        system: 'home',
        type: 1,
      },
      ...createNavList(),
    ];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customNavList, workbenchList, workbench, lang]);

  const _pathname = config.isMainApp ? pathname.replace(/^\/([^/]+).*/, '/$1/') : '/dashboard';
  const activeKey =
    deepGetKeyPath(navMenus, pathname === '/home' ? pathname : _pathname)?.[0] || '';

  const openLinkHandle = (item: INavMenu, path: string, ev) => {
    ev.preventDefault();

    if (!item.type) return;
    // 工作台
    if (item.type === 2) {
      return dispatch(createWorkbench(item.code!));
    }
    if (item.system === 'home') {
      config.isMainApp &&
        microMenus.forEach((k) => {
          dispatch(createMicroMenu(k.key, 'remove'));
          dispatch(createIframeMenu(k.key, 'remove'));
        });
    }

    openView(path);
    closePopper();
  };

  // Popper
  const [visible, setVisible] = React.useState<boolean>(false);
  const [navItem, setNavItem] = React.useState<INavMenu>();
  const stopDrop = React.useRef<boolean>(false);
  const timer = React.useRef<TimeoutHandle>();
  const timer2 = React.useRef<TimeoutHandle>();

  React.useEffect(() => {
    if (!visible) {
      removeHoverClass();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const closePopper = () => {
    setTimeout(() => setVisible(false), MOUSE_LEAVE_DELAY);
  };

  const visibleChange = (visible: boolean) => {
    if (stopDrop.current) return;
    setVisible(visible);
  };

  const clearTimer2 = () => {
    clearTimeout(timer2.current);
  };

  const addHoverClass = ($el: Element) => {
    removeHoverClass();
    $el.classList.add('focused');
  };

  const removeHoverClass = () => {
    tabsWrapperRef.current!.querySelectorAll('.link').forEach((x) => x.classList.remove('focused'));
  };

  const mouseEnterHandle = (item: INavMenu, path: string, ev) => {
    if (!config.isMainApp) return;
    if (item.id === '-' || item.type === 2 || isHttpLink(path) || item.sideState === 'H') {
      stopDrop.current = true;
      timer2.current = setTimeout(() => setVisible(false), MOUSE_ENTER_DELAY);
    } else {
      timer.current = setTimeout(() => {
        if (!visible) {
          setVisible(true);
        }
        if (navItem !== item) {
          setNavItem(item);
        }
      }, MOUSE_ENTER_DELAY);
      addHoverClass(ev.target);
    }
  };

  const mouseLeaveHandle = (ev) => {
    clearTimeout(timer.current);
    clearTimer2();
    stopDrop.current = false;
    !visible && removeHoverClass();
  };

  return {
    navMenus,
    activeKey,
    navItem,
    visible,
    openLinkHandle,
    closePopper,
    clearTimer2,
    visibleChange,
    mouseEnterHandle,
    mouseLeaveHandle,
  };
};
