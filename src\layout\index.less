/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 14:17:46
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-08-21 10:27:02
 */
.app-layout {
  flex-direction: row !important;
  height: calc(100vh - 46px);
  .app-main {
    width: 0;
  }
  .app-content {
    padding: @modulePadding @modulePadding 0;
    background: #fff;
    overflow-x: hidden;
    overflow-y: auto;
    .app-iframe-container {
      height: calc(100% + @moduleMargin);
      margin: -@moduleMargin -@moduleMargin 0;
      overflow: hidden;
    }
    micro-app-body {
      overflow: visible;
    }
  }
  [name='wb-chp'] {
    padding: @modulePadding @modulePadding 0;
    micro-app-body {
      overflow-x: visible;
      overflow-y: visible;
    }
  }
  &.is-locked > .app-sider,
  &.is-locked > .app-main {
    transition: none;
    pointer-events: none;
  }
}
