/*
 * @Author: 焦质晔
 * @Date: 2021-07-18 19:57:39
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-01 10:37:37
 */
import React from 'react';
import { get } from 'lodash-es';
import { getGlobalProp } from '@/utils';
import config from '@/config';

import type { AnyObject, IAuthUi } from '@/utils/types';

const withAuth = <T extends AnyObject<any>>(WrappedComponent: React.ComponentType<T>): any => {
  const C = (props, ref) => {
    const getLocalAuth = (): Record<string, IAuthUi> => {
      return getGlobalProp('__auth_data__') || {};
    };

    const getLocalAuthBtn = (): Record<string, string[]> => {
      return getGlobalProp('__auth_btn_data__') || {};
    };

    /**
     * @description 对按钮进行权限控制
     * @param {string} caseCode 用例号
     * @param {string} code 权限按钮的 code 码
     * @returns {boolean}
     */
    const getButtonAuth = (caseCode: string, code: string): boolean => {
      const auth = getLocalAuthBtn();
      const list = auth[caseCode] || auth[config.code]?.[caseCode];
      if (Array.isArray(list)) {
        return list.findIndex((x) => x == code) > -1;
      }
      return false;
    };

    /**
     * @description 获取UI界面权限
     * @param {string} caseCode 用例号
     * @param {string} code 组件编号
     * @returns {object}
     */
    const createAuth = (caseCode: string, code: string) => {
      const auth = getLocalAuth();
      const data = auth[caseCode] || auth[config.code]?.[caseCode] || {};
      return get(data, code) ?? null;
    };

    return (
      <WrappedComponent
        ref={ref}
        {...props}
        createAuth={createAuth}
        getButtonAuth={getButtonAuth}
      />
    );
  };

  C.displayName = `Auth(${WrappedComponent.displayName || WrappedComponent.name})`;

  return React.forwardRef(C);
};

export default withAuth;
