/*
 * @Author: 焦质晔
 * @Date: 2025-03-28 13:22:08
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-30 13:12:46
 */
import React from 'react';
import localforage from 'localforage';
import { useSelector, useDispatch } from '@/store';
import { createDictData, createAuthBtn } from '@/store/actions';
import { isLogin } from '@/router';
import { isSameOrigin, setGlobalProp } from '@/utils';
import { useUpdateEffect } from '@/hooks';
import config from '@/config';

import type { AnyFunction } from '@/utils/types';
import type { AppState } from '@/store/reducers/app';

import './index.less';

type IProps = {
  children?: React.ReactNode;
};

type ILoadState = 'notStart' | 'loading' | 'loaded';

const DeferLoader: React.FC<IProps> = (props) => {
  const { lang } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch<AnyFunction<any>>();

  const sameOrigin = React.useMemo<boolean>(() => isSameOrigin(), []);
  const loadState = React.useRef<ILoadState>('notStart');
  const setLoadState = (value: ILoadState) => (loadState.current = value);
  // 微前端  同源  未登录 -> 无需获取数据字典、权限等
  const noFetchRequired = config.powerByMicro || sameOrigin || !isLogin();

  const [updateMark, setUpdateMark] = React.useState<number>(0);

  // 数据字典
  const fetchDict = async (reload?: boolean) => {
    const data = await localforage.getItem('dict');
    if (!data || reload) {
      await dispatch(createDictData(reload));
    } else {
      setGlobalProp('__dict_data__', data);
      dispatch(createDictData(true)); // 更新缓存数据
    }
  };

  // UI 权限

  // 按钮权限
  const fetchAuthBtn = async (reload?: boolean) => {
    const data = await localforage.getItem('auth_btn');
    if (!data || reload) {
      await dispatch(createAuthBtn(reload));
    } else {
      setGlobalProp('__auth_btn_data__', data);
      dispatch(createAuthBtn(true)); // 更新缓存数据
    }
  };

  const createGlobalData = (reload?: boolean, count = 0) => {
    setLoadState('loading');
    Promise.all([fetchDict(reload), fetchAuthBtn(reload)])
      .then(() => {
        setUpdateMark((prev) => prev + 1);
      })
      .catch(() => {
        // 重试一次
        if (count < 1) {
          createGlobalData(reload, count + 1);
        } else {
          console.error(`[${config.code}] Error: 字典、权限加载失败，请检查接口！`);
        }
      })
      .finally(() => {
        setLoadState('loaded');
      });
  };

  React.useMemo(() => {
    if (!noFetchRequired && loadState.current === 'notStart') {
      createGlobalData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [noFetchRequired]);

  useUpdateEffect(() => {
    if (!noFetchRequired) {
      createGlobalData(true);
    }
  }, [lang]);

  if (noFetchRequired) {
    return props.children;
  }

  const LoadingNode = (
    <div className={`spin-wrapper defer-loader-spin`}>
      <span className={`spin-dot spin-dot-spin`}>
        <i></i>
        <i></i>
        <i></i>
        <i></i>
      </span>
      <span className={`spin-text`}>Loading...</span>
    </div>
  );

  return !updateMark ? LoadingNode : props.children;
};

DeferLoader.displayName = 'DeferLoader';

export default DeferLoader;
