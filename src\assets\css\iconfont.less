/*
 * @Author: 焦质晔
 * @Date: 2021-02-14 15:02:07
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-01-15 14:14:40
 */
@font-face {
  font-family: 'iconfont';
  src: url('../fonts/iconfont.woff?t=1567214741873') format('woff'),
    /* chrome, firefox */ url('../fonts/iconfont.ttf?t=1567214741873') format('truetype'); /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
}

.iconfont {
  font-family: 'iconfont' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-appstore-fill:before {
  content: '\e853';
}

.icon-folder-add-fill:before {
  content: '\e85e';
}

.icon-flag-fill:before {
  content: '\e863';
}

.icon-star-fill:before {
  content: '\e86a';
}
