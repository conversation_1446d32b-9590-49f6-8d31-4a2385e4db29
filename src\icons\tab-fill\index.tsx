/*
 * @Author: 焦质晔
 * @Date: 2022-11-16 10:45:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-11-17 14:17:37
 */
import React from 'react';

const TabFillIcon: React.FC = () => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      focusable="false"
      data-icon="filter"
      width="1em"
      height="1em"
      fill="currentColor"
      aria-hidden="true"
    >
      <path d="M288 106.666667A181.333333 181.333333 0 0 1 469.333333 288V469.333333H288a181.333333 181.333333 0 1 1 0-362.666666z m0 448H469.333333v181.333333A181.333333 181.333333 0 1 1 288 554.666667z m448-448a181.333333 181.333333 0 1 1 0 362.666666H554.666667V288a181.333333 181.333333 0 0 1 181.333333-181.333333zM554.666667 554.666667h181.333333A181.333333 181.333333 0 1 1 554.666667 736V554.666667z"></path>
    </svg>
  );
};

export default TabFillIcon;
