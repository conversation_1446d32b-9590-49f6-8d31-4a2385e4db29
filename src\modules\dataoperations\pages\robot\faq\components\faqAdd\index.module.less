.content {
  display: flex;
  gap: 10px;
  .con {
    flex: 1;
    padding: 20px;
    .title {
      display: flex;
      align-items: center;
      margin-bottom: 28px;
      .title_num {
        width: 20px;
        height: 20px;
        background: #3a2ae4;
        color: #fff;
        border-radius: 20px;
        text-align: center;
        line-height: 20px;
      }
      .title_txt {
        font-size: 16px;
        font-weight: 500;
        color: #262629;
        margin-left: 8px;
      }
    }


    .part {
      border-radius: 4px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 8px 16px;
      background: linear-gradient(270deg, rgba(238, 238, 255, 0.3) 0%, #eeeeff 100%);
      margin-bottom: 16px;
      .part_txt {
        font-size: 14px;
        line-height: 24px;
        font-weight: 500;
        color: #262629;
      }
    }
    .inputPart {
      height: 200px;
      overflow: auto;
      .inputBox {
        margin-bottom: 16px;
        display: flex;
        gap: 10px;
      }
    }
  }
  .leftCon {
    border-right: 1px solid #d8d8d8;
  }
  .formLabel::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
  }
}
.leftBtnArea {
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 12px;
}