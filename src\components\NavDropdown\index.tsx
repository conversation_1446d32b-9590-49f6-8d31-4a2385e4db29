/*
 * @Author: 焦质晔
 * @Date: 2023-04-11 17:18:46
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-04-13 21:40:55
 */
import React from 'react';
import classNames from 'classnames';
import { Link, NavLink, useLocation } from 'react-router-dom';
import { useSelector } from '@/store';
import { useUpdateEffect, useTool, useLocale } from '@/hooks';
import { addUrlToken, getSystem, isHttpLink } from '@/utils';
import { useCommonCase } from '../SideMenu/useCommonCase';
import { getVirtualPath } from '../NavList/NavNode';
import config from '@/config';

import type { AppState, INavMenu, ISideMenu } from '@/store/reducers/app';

import { QmEmpty, QmSplit, QmScrollbar } from '@jiaozhiye/qm-design-react';
import { RightOutlined, CaretDownOutlined } from '@/icons';

import './index.less';

type IProps = {
  navItem?: INavMenu;
  visible?: boolean;
  onClose?: () => void;
  onMouseEnter?: (ev: React.MouseEvent) => void;
  onMouseLeave?: (ev: React.MouseEvent) => void;
};

const NavDropdown: React.FC<IProps> = (props) => {
  const { navItem, visible, onMouseEnter, onClose } = props;
  const { type, system, children: subNavItems = [] } = navItem || {};

  const { menuList, commonMenus } = useSelector((state: AppState) => state.app);
  const { openView } = useTool();
  const { t } = useLocale();
  const { pathname } = useLocation();
  const { createCommonCase } = useCommonCase();

  const getCurrentSystem = () => {
    if (type) {
      return system!;
    }
    return subNavItems.map((x) => x.system).includes(getSystem())
      ? getSystem()
      : subNavItems[0]?.system ?? '';
  };

  const [current, setCurrent] = React.useState<string>(getCurrentSystem());

  useUpdateEffect(() => {
    setCurrent(getCurrentSystem());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navItem]);

  useUpdateEffect(() => {
    if (!visible) {
      setTimeout(() => setCurrent(getCurrentSystem()), 200);
    }
  }, [visible]);

  const commonCases = React.useMemo<ISideMenu[]>(() => {
    return commonMenus.filter((x) => x.system === current).slice(0, 5);
  }, [current, commonMenus]);

  const menus = React.useMemo<ISideMenu[]>(() => {
    if (subNavItems.find((x) => x.system === current)?.sideState === 'H') {
      return [];
    }
    return menuList.find((x) => x.system === current)?.children || [];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [current]);

  const createClassName = (name?: string) => {
    return name === current ? 'actived' : '';
  };

  const hasSubMenu = (item: ISideMenu) => {
    return !!item.children?.length;
  };

  const filterMenu = (list?: ISideMenu[]) => {
    return list?.filter((x) => !x.hideInMenu) || [];
  };

  const renderLink = (item: ISideMenu, showArrow?: boolean) => {
    return (
      <NavLink
        to={item.key}
        activeClassName={'actived'}
        onClick={(ev) => {
          ev.preventDefault();
          if (isHttpLink(item.caseHref!) && item.target === '_blank') {
            window.open(addUrlToken(item.caseHref!), '_blank');
          } else {
            const p = item.key.split('?');
            openView(p.length > 1 && p[0] === pathname ? `/redirect${item.key}` : item.key);
            createCommonCase(item);
          }
          onClose?.();
        }}
      >
        {item.title}
        {showArrow && <RightOutlined className={`icon`} />}
      </NavLink>
    );
  };

  return (
    <div className={`nav-dropdown`} onMouseEnter={(ev) => onMouseEnter?.(ev)}>
      <QmSplit defaultValue={240} style={{ flex: '1 0' }}>
        <QmSplit.Pane min={240} className={`box-left`}>
          <QmScrollbar className={`main`}>
            <div className={`topper`}>
              <h4 className={`title`}>{navItem?.title}</h4>
              <div className={`description`}>{navItem?.cnDesc}</div>
              {subNavItems.length ? (
                <div className={`subapp-list`}>
                  <ul>
                    {subNavItems.map((x) => {
                      const _path = !x.virtual ? x.key : getVirtualPath(menuList, x);
                      return (
                        <li key={x.id}>
                          <Link
                            to={_path}
                            className={createClassName(x.system)}
                            onMouseEnter={() => {
                              setCurrent(x.sideState !== 'H' ? x.system! : '');
                            }}
                            onClick={(ev) => {
                              ev.preventDefault();
                              !isHttpLink(_path) ? openView(_path) : window.open(_path, '_blank');
                              onClose?.();
                            }}
                          >
                            <span>{x.shortName || x.title}</span>
                            <RightOutlined className={`icon`} />
                          </Link>
                          <p className={`description`}>{x.cnDesc}</p>
                        </li>
                      );
                    })}
                  </ul>
                </div>
              ) : null}
            </div>
          </QmScrollbar>
        </QmSplit.Pane>
        <QmSplit.Pane>
          <div className={`box-right`}>
            <QmScrollbar className={`main`}>
              {!filterMenu(menus).length ? (
                <QmEmpty style={{ marginTop: '20vh' }} />
              ) : (
                <div className={`outer`}>
                  {filterMenu(menus).map((x) => {
                    return (
                      <div key={x.id} className={`wrap`}>
                        <h4 className={classNames('first', { [`is-case`]: !hasSubMenu(x) })}>
                          {hasSubMenu(x) ? x.title : renderLink(x, true)}
                        </h4>
                        {hasSubMenu(x) && (
                          <ul>
                            {filterMenu(x.children).map((k) => {
                              return (
                                <li key={k.id}>
                                  {hasSubMenu(k) ? k.title : renderLink(k)}
                                  {hasSubMenu(k) && (
                                    <dl>
                                      {filterMenu(k.children).map((l) => (
                                        <dd key={l.id}>
                                          {hasSubMenu(l) ? (
                                            <>
                                              <CaretDownOutlined className={`icon`} />
                                              {l.title}
                                            </>
                                          ) : (
                                            renderLink(l)
                                          )}
                                          {hasSubMenu(l) &&
                                            filterMenu(l.children).map((m) => (
                                              <p
                                                key={m.id}
                                                style={{ paddingLeft: 10, lineHeight: '26px' }}
                                              >
                                                {renderLink(m)}
                                              </p>
                                            ))}
                                        </dd>
                                      ))}
                                    </dl>
                                  )}
                                </li>
                              );
                            })}
                          </ul>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </QmScrollbar>
            <div className={`poster`}>
              <div className={`inner scroll-bar`}>
                <h4 className={`title`}>{t('app.workbench.caseRanking')}</h4>
                {!commonCases.length ? (
                  <QmEmpty style={{ margin: 30 }} />
                ) : (
                  <ul>
                    {commonCases.map((x, i) => {
                      const _path = config.isMainApp ? `/${x.system}${x.key}` : x.key;
                      return (
                        <li key={x.id} className={`item`}>
                          <Link
                            to={_path}
                            onClick={(ev) => {
                              ev.preventDefault();
                              !isHttpLink(_path) ? openView(_path) : window.open(_path, '_blank');
                              createCommonCase(x, true);
                              onClose?.();
                            }}
                          >
                            <i className={`rank`}>{i + 1}</i>
                            {x.title}
                          </Link>
                        </li>
                      );
                    })}
                  </ul>
                )}
              </div>
            </div>
          </div>
        </QmSplit.Pane>
      </QmSplit>
    </div>
  );
};

export default NavDropdown;
