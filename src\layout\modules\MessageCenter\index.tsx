/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:09:11
 */
import React from 'react';
import classNames from 'classnames';

import { Menu, Dropdown, Badge } from '@jiaozhiye/qm-design-react';
import { BellOutlined } from '@/icons';

import './index.less';

const MessageCenter: React.FC = () => {
  const renderMenus = () => {
    const items = [
      {
        key: 1,
        label: '测试消息1',
      },
      {
        key: 2,
        label: '测试消息2',
      },
    ];
    return <Menu items={items} />;
  };

  return (
    <div className={classNames('app-message-center')}>
      <Dropdown dropdownRender={() => renderMenus()} placement="bottomRight" trigger={['click']}>
        <span>
          <Badge dot>
            <BellOutlined className={`icon`} />
          </Badge>
        </span>
      </Dropdown>
    </div>
  );
};

export default MessageCenter;
