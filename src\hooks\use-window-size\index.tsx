/*
 * @Author: 焦质晔
 * @Date: 2024-08-20 10:45:32
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-08-20 11:00:08
 */
import * as React from 'react';

export default function useWindowSize() {
  const [state, setState] = React.useState({
    winWidth: window.innerWidth,
    winHeight: window.innerHeight,
  });

  React.useEffect(() => {
    const handler = () => {
      setState({
        winWidth: window.innerWidth,
        winHeight: window.innerHeight,
      });
    };
    window.addEventListener('resize', handler);

    return () => window.removeEventListener('resize', handler);
  }, []);

  return state;
}
