import axios from '@/api/fetch'; // 假设您的axios实例封装在@/api/fetch

/**
 * 新建标注任务
 * @param {object} params - 请求参数
 * @returns {Promise}
 */
export const createAnnotationTask = (params) => {
  return axios.post('/aio/faq-hit-log/annotation-task-create', params);
};

/**
 * 任务标注列表查询
 * @param {object} params - 请求参数
 * @returns {Promise}
 */
export const getAnnotationTaskList = (params) => {
  return axios.post('/aio/faq-hit-log/annotation-task-list', params);
};

/**
 * 任务详情列表查询
 * @param {object} params - 请求参数
 * @returns {Promise}
 */
export const getAnnotationTaskDetailList = (params) => {
  return axios.post('/aio/faq-hit-log/annotation-task-detail', params);
};

/**
 * 数据统计查询
 * @param {string} taskId - 任务ID
 * @returns {Promise}
 */
export const getAnnotationStats = (taskId) => {
  return axios.get(`/aio/faq-hit-log/annotation-statistics/${taskId}`);
};

/**
 * 任务标注 (单条或批量)
 * @param {Array<object>} params - 请求参数数组
 * @returns {Promise}
 */
export const markAnnotation = (params) => {
  return axios.post('/aio/faq-hit-log/annotation-mark', params);
};

/**
 * 重新标注（解锁）
 * @param {string} detailId - 详情ID
 * @returns {Promise}
 */
export const unlockAnnotation = (detailId) => {
  return axios.post(`/aio/faq-hit-log/annotation-unlock/${detailId}`);
};

/**
 * 完成标注任务
 * @param {string} taskId - 任务ID
 * @returns {Promise}
 */
export const completeAnnotationTask = (taskId) => {
  return axios.post(`/aio/faq-hit-log/annotation-task-complete/${taskId}`);
};

/**
 * 删除标注任务
 * @param {string} taskId - 任务ID
 * @returns {Promise}
 */
export const deleteAnnotationTask = (taskId) => {
  return axios.post(`/aio/faq-hit-log/annotation-task-delete/${taskId}`);
};

/**
 * 导出标注任务数据
 * @param {string} taskId - 任务ID
 * @returns {Promise} - 返回一个blob对象用于下载
 */
export const exportAnnotationTask = (taskId) => {
  return axios.get(`/aio/faq-hit-log/annotation-task-export/${taskId}`, {
    responseType: 'blob', // 重要：设置响应类型为blob以下载文件
  });
};

/**
 * 获取知识详情（用于FAQ详情弹窗）
 * @param {string} id - 知识ID
 * @param {string} env - 环境
 * @returns {Promise}
 */
export const getKnowledgeDetail = (id, env) => {
  return axios.get(`/aio/faq-hit-log/knowledge-detail-answer/${id}/${env}`);
};

// 您也可以将已有的robot.js中的robotlist接口导入到这里或需要用到的组件中
// 以便在“新建任务”弹窗中动态获取机器人列表
export * from './robot';