/*
 * @Author: 焦质晔
 * @Date: 2022-11-16 10:45:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-11-17 14:17:37
 */
import React from 'react';

const ThreeDownIcon: React.FC = () => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      width="1em"
      height="1em"
      focusable="false"
      data-icon="filter"
      fill="currentColor"
      aria-hidden="true"
    >
      <path
        fillOpacity="0.6"
        d="m513.999991,473a29.199091,32 0 0 0 20.731355,-9.28l364.988637,-400l-41.462709,-46.72l-344.257282,378.88l-344.257282,-378.88l-41.462709,46.72l364.988637,401.28a29.199091,32 0 0 0 20.731355,8z"
      />
      <path
        fillOpacity="0.8"
        d="m514.500001,674.88l-344.703558,-378.88l-41.516459,46.72l365.461788,401.28a29.236943,32 0 0 0 41.516459,0l365.461788,-400l-41.516459,-48l-344.703558,378.88z"
      />
      <path
        fillOpacity="1"
        d="m514.500001,1023a29.312642,32 0 0 0 20.811976,-9.28l366.408025,-400l-41.623952,-46.72l-345.596049,378.88l-345.596049,-378.88l-41.623952,46.72l366.408025,401.28a29.312642,32 0 0 0 20.811976,8z"
      />
    </svg>
  );
};

export default ThreeDownIcon;
