.ckEditor {
  max-width: 500px;
  :global {
    .ck-content {
      min-height: 200px;
    }

    // 目标：.ck.ck-toolbar 元素
    // 作用：允许工具栏本身的高度根据内容自适应
    .ck.ck-toolbar {
      height: auto;
      min-height: 0; // 如果有 min-height 限制，也一并重置
    }

    // 目标：.ck-toolbar__items 元素，这是工具栏按钮的直接 flex 容器
    // 作用：核心步骤，允许 flex 项换行
    .ck-toolbar__items {
      flex-wrap: wrap !important; // 强制 flex 容器中的项目换行
    }

    // 目标：.ck-toolbar__overflow-button 元素，即“更多”(...)按钮
    // 作用：隐藏掉这个按钮，因为它在换行模式下不再需要
    .ck-toolbar__overflow-button {
      display: none !important; // 强制隐藏
    }
  }
  .main-container {
    font-family: var(--ck-content-font-family);
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
  }

  .editor-container_classic-editor .editor-container__editor {
    min-width: 795px;
    max-width: 795px;
  }

  .ck-content h3.category {
    font-family: 'Oswald';
    font-size: 20px;
    font-weight: bold;
    color: #555;
    letter-spacing: 10px;
    margin: 0;
    padding: 0;
  }

  .ck-content h2.document-title {
    font-family: 'Oswald';
    font-size: 50px;
    font-weight: bold;
    margin: 0;
    padding: 0;
    border: 0;
  }

  .ck-content h3.document-subtitle {
    font-family: 'Oswald';
    font-size: 20px;
    color: #555;
    margin: 0 0 1em;
    font-weight: bold;
    padding: 0;
  }

  .ck-content p.info-box {
    --background-size: 30px;
    --background-color: #e91e63;
    padding: 1.2em 2em;
    border: 1px solid var(--background-color);
    background: linear-gradient(
        135deg,
        var(--background-color) 0%,
        var(--background-color) var(--background-size),
        transparent var(--background-size)
      ),
      linear-gradient(
        135deg,
        transparent calc(100% - var(--background-size)),
        var(--background-color) calc(100% - var(--background-size)),
        var(--background-color)
      );
    border-radius: 10px;
    margin: 1.5em 2em;
    box-shadow: 5px 5px 0 #ffe6ef;
  }

  .ck-content span.marker {
    background: yellow;
  }

  .ck-content span.spoiler {
    background: #000;
    color: #000;
  }

  .ck-content span.spoiler:hover {
    background: #000;
    color: #fff;
  }

  .ck-content .button {
    display: inline-block;
    width: 260px;
    border-radius: 8px;
    margin: 0 auto;
    padding: 12px;
    color: #ffffff;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    text-decoration: none;
  }

  .ck-content .button--green {
    background-color: #406b1e;
  }

  .ck-content .button--black {
    background-color: #141517;
  }
}
