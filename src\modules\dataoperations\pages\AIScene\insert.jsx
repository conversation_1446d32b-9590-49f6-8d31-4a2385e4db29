import React, {useEffect, useState} from "react";
import { Button, Cascader, Input, InputNumber, message, QmModal, Radio, Select } from '@jiaozhiye/qm-design-react';
import {Form} from 'antd'
import { Api } from '@/modules/dataoperations/api/AiScene';
import { getUserInfo } from '@/utils/cookies';

export const Insert = (props) => {

  const [form] = Form.useForm();

  const [flowList, setFlowList] = useState([]);

  const [unitList, setUnitList] = useState([]);

  const getFlowList = async () => {
    const res = await Api.getFlowList()
    if(res.code === 200) {
      setFlowList([
        {
          value: 3,
          label: 'L3',
          children: res.data.l3flowInfoList.map(it => {
            return {
              label: it.l3flowName,
              value: it.l3flowCode
            }
          })
        },
        {
          value: 4,
          label: 'L4',
          children: res.data.l4flowInfoList.map(it => {
            return {
              label: it.l3flowName,
              value: it.l3flowCode
            }
          })
        },
        {
          value: 5,
          label: 'L5',
          children: res.data.l5flowInfoList.map(it => {
            return {
              label: it.l3flowName,
              value: it.l3flowCode
            }
          })
        },
      ])
    }
  }


  const getUnitList = async (e) => {
    const res = await Api.getUnitList({
      l3flowCode: e[1],
      level: e[0]
    })
    if(res.code === 200) {
      setUnitList(res.data)
    }
  }

  const onChange = (value, selectedOptions) => {
    console.log(value, selectedOptions);
    form.setFieldValue('l3ProcessName', selectedOptions[1].label)
    form.setFieldValue('bizUnitCode', null)
    form.setFieldValue('bizUnitName', null)
    getUnitList(value)
  }

  const submit = async () => {
    await form.validateFields()
    const value = form.getFieldsValue([
      'sceneCode', 'sceneName', 'l3ProcessCode', 'l3ProcessName',
      'bizUnitCode', 'bizUnitName', 'callBackUrl', 'comparisonResults',
      'rejectNum', 'id'
    ])
    const param = {
      ...value,
      l3ProcessCode: value.l3ProcessCode[1] || '',
      createUserName: getUserInfo().loginName
    }
    console.log(param);
    const res = await Api.insertOrUpdateScene(param)
    if(res.code === 200) {
      message.success('操作成功')
      props.onClose(true)
    }
  }

  useEffect(() => {
    if(!props.visible) {
      form.resetFields()
      return
    }
    console.log(props.row);
    if(props.row) {
      form.setFieldsValue({
        ...props.row,
      })
      flowList.length && flowList.forEach(item => {
        const i = item.children.find(it => it.value === props.row.l3ProcessCode)
        if(i) {
          form.setFieldValue('l3ProcessCode', [item.value, i.value])
          getUnitList([item.value, i.value])
        }
      })
    }


  }, [props.visible]);

  useEffect(() => {
    getFlowList()
  }, []);


  return (
    <QmModal
      visible={props.visible}
      title={'新建场景'}
      width={800}
      onCancel={props.onClose}
      onClose={props.onClose}
    >
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Form.Item name={'sceneCode'} label={'场景ID：'} rules={[{ required: true, max: 20},{
          pattern: /^[a-zA-Z0-9]*$/, // 正则表达式匹配英文和数字
          message: '只能输入英文和数字!', // 校验失败时的提示信息
        },]}>
          <Input/>
        </Form.Item>
        <Form.Item name={'sceneName'} label={'场景名称：'} rules={[{ required: true, max: 20}]}>
          <Input/>
        </Form.Item>
        <Form.Item name={'l3ProcessCode'} label={'L3/L4流程：'} rules={[{ required: true}]}>
          <Cascader options={flowList} onChange={onChange} placeholder="请选择" />
        </Form.Item>
        <Form.Item name={'bizUnitCode'} label={'业务单元：'} rules={[{ required: true}]}>
          <Select options={unitList} fieldNames={{label: 'bizUnitName', value: 'bizUnitCode'}}
            onChange={(e, v) => form.setFieldValue('bizUnitName', v.bizUnitName)}
          />
        </Form.Item>
        <Form.Item name={'callBackUrl'} label={'回调URL'} rules={[{ required: true}]}>
          <Input />
        </Form.Item>
        <Form.Item name={'comparisonResults'} label={'对比结果：'} rules={[{ required: true}]}>
            <Radio.Group>
              <Radio value={1}>人工</Radio>
              <Radio value={0}>ai</Radio>
            </Radio.Group>
        </Form.Item>
        <Form.Item shouldUpdate={(prevValues, currentValues) => prevValues.comparisonResults !== currentValues.comparisonResults} wrapperCol={{span: 24}}>
          {
            ({getFieldValue}) => {
              return getFieldValue('comparisonResults') === 1 &&
                <Form.Item name={'rejectNum'} label={'驳回转人工次数'}  rules={[{ required: true},{
                  pattern: /^-?\d+$/, // 正则表达式匹配整数（可选负号）
                  message: '只能输入整数!', // 校验失败时的提示信息
                },]}  labelCol={{ span: 6 }}>
                  <InputNumber min={1} />
                </Form.Item>
            }
          }
        </Form.Item>

      </Form>
      <div style={{width: '100%', alignItems: 'center', textAlign: 'center'}}>
        <Button type={'primary'} onClick={submit}>提交</Button>
      </div>
    </QmModal>
  );
};
export default Insert