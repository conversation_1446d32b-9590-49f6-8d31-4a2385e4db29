import CryptoJS from 'crypto-js';
import { safeStringify } from './jsonUtil';
const SECRET_KEY = 'LINGXIAOXI_2368&*12'

export const encrypt = (value: string): string => {
  return CryptoJS.AES.encrypt(value, SECRET_KEY).toString();
};

export const decrypt = (value: string): string => {
  const bytes = CryptoJS.AES.decrypt(value, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};
/**
 *  设置localStorage项目(带加密)
 * @param key 
 * @param obj 
 * @returns 
 */
export const setLocalStorageItem = (key: string, obj: object): boolean => {
    try {
        localStorage.setItem(key, encrypt(safeStringify(obj)));
        return true;
    } catch (error) {
        console.error('Error setting localStorage item:', error);
        return false;
    }
};
/**
 * 获取localStorage项目(带解密)
 * @param key 
 * @returns 
 */
export const getLocalStorageItem = (key: string): (object | null) => {
    try {
        const encryptedValue = localStorage.getItem(key);
        if (encryptedValue) {
            const decryptedValue = decrypt(encryptedValue);
            return JSON.parse(decryptedValue);
        }
        return null;
    } catch (error) {
        console.error('Error getting localStorage item:', error);
        localStorage.removeItem(key);
        return null;
    }
}

/**
 * 删除localStorage项目
 * @param key 
 * @returns 
 */
export const removeLocalStorageItem = (key: string): boolean => {
    try {
        localStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('Error removing localStorage item:', error);
        return false;
    }
}


/**
 *  设置sessionStorage项目(带加密)
 * @param key 
 * @param obj 
 * @returns 
 */
export const setSessionStorageItem = (key: string, obj: object): boolean => {
    try {
        sessionStorage.setItem(key, encrypt(safeStringify(obj)));
        return true;
    } catch (error) {
        console.error('Error setting sessionStorage item:', error);
        return false;
    }
};
/**
 * 获取sessionStorage项目(带解密)
 * @param key 
 * @returns 
 */
export const getSessionStorageItem = (key: string): (object | null) => {
    try {
        const encryptedValue = sessionStorage.getItem(key);
        if (encryptedValue) {
            const decryptedValue = decrypt(encryptedValue);
            return JSON.parse(decryptedValue);
        }
        return null;
    } catch (error) {
        console.error('Error getting sessionStorage item:', error);
        sessionStorage.removeItem(key);
        return null;
    }
}

/**
 * 删除sessionStorage项目
 * @param key 
 * @returns 
 */
export const removeSessionStorageItem = (key: string): boolean => {
    try {
        sessionStorage.removeItem(key);
        return true;
    } catch (error) {
        console.error('Error removing sessionStorage item:', error);
        return false;
    }
}