import axios from '@/api/fetch'

export class Api {
  static async getAiSceneList (params) {
    return axios.post('/aio/aiScene/getAiSceneList', params)
  }

  static async getFlowList (params) {
    return axios.post('/aio/eng/getFlowList', params)
  }

  static async getUnitList (params) {
    return axios.post('/aio/eng/getUnitList', params)
  }

  static async insertOrUpdateScene (params) {
    return axios.post('/aio/aiScene/insertOrUpdate', params)
  }

  static async insertOrUpdateModel (params) {
    return axios.post('/aio/aiAuditModel/insertOrUpdate', params)
  }

  static async getAiAuditModelList (params) {
    return axios.post('/aio/aiAuditModel/getAiAuditModelList', params)
  }

  static async getAiTechnologyModelList (params) {
    return axios.post('/aio/aiAuditModel/getAiTechnologyModelList', params)
  }
}