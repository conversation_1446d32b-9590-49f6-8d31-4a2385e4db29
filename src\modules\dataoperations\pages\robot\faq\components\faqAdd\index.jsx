import React, { useEffect, useImperativeHandle, useState, forwardRef } from 'react';
import { DatePicker, Form, Input, Button, Select, Radio, TreeSelect, Popconfirm } from 'antd';
import { DownOutlined, DeleteOutlined } from '@/icons';
import { knowledgedetail } from '@/modules/dataoperations/api/robot';
import Editor from '../ckEditor';
import moment from 'moment';
import css from './index.module.less';
import cs from 'classnames';
import { convertToInlineStyles } from '../ckEditor/classToStyle'
import BatchAddModal from './batchAddModal';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const FaqAdd = forwardRef(({ treeData, actionData }, ref) => {
  const [form] = Form.useForm();
  const [form1] = Form.useForm();
  const [inputArr, setInputArr] = useState([{ similarQuestion: '' }]);
  const [radioValue, setRadioValue] = useState('00');
  const [detail, setDetail] = useState({});
  const [selectValue, setSelectValue] = useState('');
  const [batchAddOpen, setBatchAddOpen] = useState(false);

  useEffect(() => {
    async function getDetail() {
      let res = await knowledgedetail(actionData.rowData);
      if (res.code != 200) return;
      setDetail(res.data);
      setInputArr(
        res.data.similarKnowledges.length == 0
          ? [{ similarQuestion: '' }]
          : res.data.similarKnowledges.map((item) => ({
            id: item.id,
            similarQuestion: item.similarQuestion,
          }))
      );
      let obj = {
        categoryId: res.data.knowledge.categoryId,
        question: res.data.knowledge.question,
        effectiveType: res.data.knowledge.effectiveType,
        rangeDateValue: res.data.knowledge.effectiveStartTime
          ? [
            moment(res.data.knowledge.effectiveStartTime, 'YYYY-MM-DD'),
            moment(res.data.knowledge.effectiveEndTime, 'YYYY-MM-DD'),
          ]
          : undefined,
      };

      let obj1 = {
        answerType: res.data.knowledge.answerType,
        answer: res.data.knowledge.answer,
      };
      setSelectValue(res.data.knowledge.effectiveType);
      setRadioValue(res.data.knowledge.answerType);
      form.setFieldsValue(obj);
      form1.setFieldsValue(obj1);
    }
    if (actionData.type != 'add') {
      getDetail();
    } else if (actionData.type == 'add') {
      setRadioValue('00');
      setDetail({});
      setInputArr([{ similarQuestion: '' }]);
      form.resetFields();
      form1.resetFields();
      // 检查 actionData.rowData 中是否存在 categoryId，如果存在，则设置为表单的默认值。
      // 这样做可以避免在没有选择任何类目时，设置一个 undefined 的值。
      if (actionData.rowData.categoryId) {
        form.setFieldsValue({ categoryId: actionData.rowData.categoryId });
      }
    }
  }, [actionData, form1, form]);

  const handleAdd = () => {
    let obj = {
      similarQuestion: '',
    };
    setInputArr([...inputArr, obj]);
  };
  const handleBatchAdd = () => {
    setBatchAddOpen(true);
  }
  const batchAddFinish = (addList) => {
    setInputArr(oldArr => {
      if (oldArr.length === 1 && !oldArr[0].similarQuestion) {
        return addList?.map(item => ({similarQuestion : item}))
      }
      return oldArr.concat(addList?.map(item => ({similarQuestion : item})));
    })
  }

  useImperativeHandle(ref, () => ({
    getData: (callback) => {
      form.validateFields().then((values) => {
        let arr = [];
        inputArr.map((item) => {
          if (item.similarQuestion) {
            arr.push(item.similarQuestion);
          }
        });
        values.similarQuestionList = actionData.type == 'add' ? arr : inputArr;
        values.effectiveStartTime = values.rangeDateValue
          ? values.rangeDateValue[0].format('YYYY-MM-DD')
          : '';
        values.effectiveEndTime = values.rangeDateValue
          ? values.rangeDateValue[1].format('YYYY-MM-DD')
          : '';
        form1.validateFields().then((values1) => {
          if (values1.answerType === '01') {
            let finalAnswer = values1.answer;
            const trimmedAnswer = finalAnswer.trim(); // 去除首尾空格以便精确判断

            // 当且仅当 answer 不是以 '<div class="ck-content">' 开头
            // 且不是以 '</div>' 结尾时，才进行包裹
            if (!trimmedAnswer.startsWith('<div class="ck-content"') || !trimmedAnswer.endsWith('</div>')) {
              finalAnswer = `<div class="ck-content">${finalAnswer}</div>`;
            }

            const inlinedData = convertToInlineStyles(finalAnswer);
            callback({ ...values, ...values1, answer: inlinedData });
          } else {
            callback({ ...values, ...values1, });
          }
        });
      });
    },
    formReset: () => {
      form.resetFields();
      form1.resetFields();
    },
  }));
  return (
    <div className={css.content}>
      <div className={cs(css.leftCon, css.con)}>
        <div className={css.title}>
          <div className={css.title_num}>1</div>
          <div className={css.title_txt}>问题配置</div>
        </div>
        <div className={css.part}>
          <div className={css.part_txt}>知识详情</div>
          <DownOutlined />
        </div>
        <div>
          <Form form={form} layout="vertical">
            <Form.Item name="categoryId" label="知识类目" rules={[{ required: true }]}>
              <TreeSelect
                showSearch
                style={{ width: '100%' }}
                placeholder="请选择知识类目"
                allowClear
                treeDefaultExpandAll
                treeData={treeData}
              />
            </Form.Item>
            <Form.Item name="question" label="FAQ题目" rules={[{ required: true }]}>
              <Input placeholder="请输入（不超过120字）" length={120} />
            </Form.Item>

            <Form.Item label={<div className={css.formLabel}>生效时间</div>}>
              <Form.Item
                name="effectiveType"
                rules={[{ required: true, message: '请选择类型' }]}
                style={{ display: 'inline-block', width: 'calc(20% - 8px)' }}
              >
                <Select
                  placeholder="请选择"
                  style={{ width: '100%' }}
                  onChange={(val) => {
                    setSelectValue(val);
                  }}
                >
                  <Option value="00">永久有效</Option>
                  <Option value="01">选择时间</Option>
                  <Option value="10">不生效</Option>
                </Select>
              </Form.Item>
              <Form.Item
                name="rangeDateValue"
                rules={[
                  {
                    required: selectValue == '01',
                    message: '请选择日期时间',
                  },
                ]}
                style={{ display: 'inline-block', width: 'calc(80% - 8px)', margin: '0 8px' }}
              >
                <RangePicker style={{ width: '100%' }} disabled={selectValue != '01'} />
              </Form.Item>
            </Form.Item>
          </Form>
        </div>
        <div className={css.part}>
          <div className={css.part_txt}>相似问法</div>
          <DownOutlined />
        </div>
        <div className={css.inputPart}>
          {inputArr.map((item, index) => {
            return (
              <div key={index} className={css.inputBox}>
                <Input
                  placeholder="请输入（不超过120字）"
                  length={120}
                  value={item.similarQuestion}
                  onChange={(e) => {
                    inputArr[index].similarQuestion = e.target.value;
                    setInputArr(inputArr.map((item) => item));
                  }}
                />
                {inputArr.length != 1 && (
                  <Popconfirm
                    title="确定删除？"
                    onConfirm={() => {
                      let arr = JSON.parse(JSON.stringify(inputArr));
                      arr.splice(index, 1);
                      setInputArr(arr);
                    }}
                    okText="确定"
                    cancelText="取消"
                  >
                    <DeleteOutlined style={{ color: 'red', cursor: 'pointer' }} />
                  </Popconfirm>
                )}
              </div>
            );
          })}
          <div className= {css.leftBtnArea}>
            <Button type="primary" onClick={handleAdd}>
              添加
            </Button>
            <Button type="primary" onClick={handleBatchAdd}>
              批量添加
            </Button>
          </div>
        </div>
      </div>
      <div className={cs(css.rightCon, css.con)}>
        <div className={css.title}>
          <div className={css.title_num}>2</div>
          <div className={css.title_txt}>答案配置</div>
        </div>

        <Form
          form={form1}
          layout="vertical"
          initialValues={{
            answerType: '00',
          }}
        >
          <Form.Item name="answerType" label="答案类型" rules={[{ required: true }]}>
            <Radio.Group
              onChange={(e) => {
                setRadioValue(e.target.value);
                let oldHTML = detail.knowledge?.answer;
                var newHtml = oldHTML && oldHTML.replace(/&nbsp;|<[^>]+>/g, '');
                if (e.target.value == '00') {
                  form1.setFieldValue('answer', newHtml);
                } else {
                  form1.setFieldValue('answer', oldHTML);
                }
              }}
              value={radioValue}
            >
              <Radio value="00">纯文本</Radio>
              <Radio value="01">富文本</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item name="answer" label="答案内容" rules={[{ required: true }]}>
            {radioValue == '00' ? (
              <TextArea
                showCount
                maxLength={9999}
                placeholder="请输入答案内容"
                style={{ height: 200, resize: 'none' }}
              />
            ) : (
              <Editor
                actionData={actionData}
                data={detail.knowledge?.answer}
                onChange={(val) => {
                  form1.setFieldValue('answer', val);
                }}
              />
            )}
          </Form.Item>
        </Form>
      </div>
      <BatchAddModal isOpen={batchAddOpen} setIsOpen={setBatchAddOpen} finish={batchAddFinish}/>
    </div>
  );
});

FaqAdd.displayName = 'FaqAdd';
export default FaqAdd;