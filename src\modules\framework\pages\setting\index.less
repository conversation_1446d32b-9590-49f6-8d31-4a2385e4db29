/*
 * @Author: 焦质晔
 * @Date: 2021-07-20 16:35:57
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-03-07 09:18:41
 */
.app-sys-setting {
  height: 100%;
  .sys-setting-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 4px;
    transition: all 0.3s ease;
    cursor: pointer;
    &:hover {
      background-color: rgba(0, 0, 0, 0.045);
    }
  }
  .icon {
    color: @textColorSecondary;
    font-size: 18px;
    cursor: pointer;
  }
}
