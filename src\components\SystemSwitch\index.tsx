/*
 * @Author: 焦质晔
 * @Date: 2022-08-18 09:48:50
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:21:55
 */
import React from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from '@/store';
import { createMicroMenu, createIframeMenu } from '@/store/actions';
import { useTool, useLocale } from '@/hooks';
import { Message } from '@/utils';
import { getUserInfo, setUserInfo } from '@/utils/cookies';
import type { AppState } from '@/store/reducers/app';

import { getSysList, setLastSystem } from '@/api/application';

import { Dropdown, Menu } from '@jiaozhiye/qm-design-react';
import { DownOutlined } from '@/icons';

import './index.less';

type IProps = {
  onChange: (title: string) => void;
};

type ISysItem = {
  systemId: string;
  systemName: string;
};

const SystemSwitch: React.FC<IProps> = (props) => {
  const { onChange } = props;
  const { tabMenus, lang } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { t } = useLocale();

  const [sysItems, setSysItems] = React.useState<ISysItem[]>([]);
  const { reloadView } = useTool();
  const { pathname } = useLocation();
  const { systemId, tenantId, id: userId } = getUserInfo();

  const getSysInfo = async () => {
    try {
      const res = await getSysList({ userId: getUserInfo().id, userType: 0 });
      if (res.code === 200) {
        const items = res.data.dataList || [];
        const sysName = items.find((x) => x.systemId === systemId)?.systemName || '';
        setSysItems(items);
        onChange(sysName);
      }
    } catch (err) {
      // ...
    }
  };

  const setSystem = async (sysId: string) => {
    try {
      await setLastSystem({ userId, systemId: sysId, tenantId });
    } catch (err) {
      // ...
    }
  };

  React.useEffect(() => {
    getSysInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lang]);

  const systemChange = (key: string) => {
    if (key === systemId) return;
    const sysName = sysItems.find((x) => x.systemId === key)!.systemName;
    setUserInfo(Object.assign({}, getUserInfo(), { systemId: key ?? '' }));
    setSystem(key);
    // 清空页签缓存
    tabMenus.forEach((x) => {
      if (x.path === pathname) return;
      dispatch(createMicroMenu(x.path, 'remove'));
      dispatch(createIframeMenu(x.path, 'remove'));
    });
    // 刷新当前页签
    reloadView();
    onChange(sysName);
    Message(`${t('app.workbench.systemCutText')}：${sysName}`, 'success');
  };

  const renderDropWrap = () => {
    return (
      <Menu
        selectable
        selectedKeys={[systemId]}
        items={sysItems.map((x) => ({
          key: x.systemId,
          label: x.systemName,
        }))}
        onClick={({ domEvent: ev, key }) => {
          ev.stopPropagation();
          systemChange(key);
        }}
      />
    );
  };

  return (
    <Dropdown
      dropdownRender={() => renderDropWrap()}
      overlayClassName="system-switch__popper"
      placement="bottomRight"
      trigger={['click']}
      disabled={!sysItems.length}
    >
      <DownOutlined className={`icon`} />
    </Dropdown>
  );
};

export default SystemSwitch;
