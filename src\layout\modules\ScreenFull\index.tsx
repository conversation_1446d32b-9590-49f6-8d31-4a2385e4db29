/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:12:16
 */
import React from 'react';
import classNames from 'classnames';
import screenfull from 'screenfull';

import { FullscreenOutlined, FullscreenExitOutlined } from '@/icons';

import './index.less';

const ScreenFull: React.FC = () => {
  const [isFullscreen, setFullscreen] = React.useState<boolean>(false);

  React.useEffect(() => {
    initial();
    return () => destroy();
  }, []);

  const initial = () => {
    if (screenfull.isEnabled) {
      screenfull.on('change', changeHandler);
    }
  };

  const clickHandle = () => {
    if (screenfull.isEnabled) {
      screenfull.toggle();
    }
  };

  const changeHandler = () => {
    if (screenfull.isEnabled) {
      setFullscreen(screenfull.isFullscreen);
    }
  };

  const destroy = () => {
    if (screenfull.isEnabled) {
      screenfull.off('change', changeHandler);
    }
  };

  return (
    <div className={classNames('app-screen-full')} onClick={clickHandle}>
      <span className={classNames('ant-dropdown-trigger')}>
        {!isFullscreen ? (
          <FullscreenOutlined className={`icon`} />
        ) : (
          <FullscreenExitOutlined className={`icon`} />
        )}
      </span>
    </div>
  );
};

export default ScreenFull;
