/*
 * @Author: 焦质晔
 * @Date: 2021-07-14 16:15:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-01-04 14:53:49
 */
.app-header {
  .app-user-center {
    height: 100%;
    .ant-dropdown-trigger {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 0 6px;
      transition: all 0.3s ease;
      cursor: pointer;
      &:hover {
        background-color: rgba(0, 0, 0, 0.045);
      }
      .name {
        margin-left: 8px;
        font-size: @textSize;
      }
    }
  }

  // size
  &__sm {
    .app-user-center .ant-dropdown-trigger .name {
      font-size: @textSize - 1px;
    }
  }
}

.app-user-center__popper {
  min-width: 120px !important;
  .ant-dropdown-menu-title-content {
    display: flex;
    align-items: center;
  }
}
