/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:40:32
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-10-09 10:14:14
 */
import { lazy } from 'react';
import { t } from '@/locale';
import config from '@/config';
import moduleRoutes from './routes';
import type { IRoute } from '@/utils/types';

import Redirect from '@/pages/redirect';
import Nomatch from '@/pages/nomatch';

const Login = lazy(() => import('@/modules/framework/pages/login'));
const Home = lazy(() => import('@/modules/framework/pages/home'));
const Dashboard = lazy(() => import('@/modules/framework/pages/dashboard'));
const BasicLayout = lazy(() => import('@/layout/BasicLayout'));
const BlankLayout = lazy(() => import('@/layout/BlankLayout'));
const MicroLayout = lazy(() => import('@/layout/MicroLayout'));

const flattenRoutes: IRoute[] = moduleRoutes.map((x) => x.routes).flat();

export const deepFindRoute = (list: IRoute[], path: string): IRoute | null => {
  let res: IRoute | null = null;
  for (let i = 0; i < list.length; i++) {
    if (Array.isArray(list[i].routes)) {
      res = deepFindRoute(list[i].routes!, path);
    }
    if (res) {
      return res;
    }
    if (list[i].path === path) {
      return list[i];
    }
  }
  return res;
};

const createRouteItem = (route: IRoute, prefixPath: string) => {
  const { path, meta, exact, props, iframePath, component = BlankLayout, routes } = route;
  const item: IRoute = { path: prefixPath + path, meta, exact, props, component };
  if (iframePath) {
    Object.assign(item, { iframePath, component: MicroLayout });
  }
  if (Array.isArray(routes)) {
    item.meta = { ...meta, noAuth: true };
    item.routes = routes.map((x) => createRouteItem(x, item.path));
  }
  return item;
};

const formatRoutes = (routes: IRoute[]) => {
  return routes.map((route) => {
    const item: IRoute = Object.assign({}, route);
    if (route.iframePath || config.microType) {
      Object.assign(item, {
        iframePath: route.iframePath
          ? route.iframePath
          : config.microType === 'iframe'
          ? `${config.baseRoute}/iframe${route.path}`
          : '',
        component: BlankLayout,
      });
    }
    if (Array.isArray(route.routes)) {
      item.meta = { ...route.meta, noAuth: true };
      item.routes = formatRoutes(route.routes);
    }
    return item;
  });
};

const getMicroRoutes = () => {
  let result: IRoute[] = [];
  // for micro-app
  if (config.powerByMicro) {
    result = flattenRoutes.map((x) => createRouteItem(x, `/${config.system}`));
    result.unshift({
      path: `/${config.system}/dashboard`,
      meta: { title: t('app.global.dashboard') },
      component: Dashboard,
    });
    result.push({
      path: '/404',
      component: Nomatch,
    });
  }
  return result;
};

const getIframeRoutes = () => {
  let result: IRoute[] = [];
  result = flattenRoutes.map((x) => createRouteItem(x, '/iframe'));
  result.unshift({
    path: `/iframe/dashboard`,
    meta: { title: t('app.global.dashboard') },
    component: Dashboard,
  });
  result.push({
    path: '/iframe/*',
    component: Nomatch,
  });
  return result;
};

const routes: IRoute[] = [
  {
    path: '/login',
    meta: { title: t('app.login.title') },
    component: Login,
  },
  ...moduleRoutes.map((x) => x.public).flat(),
  ...getIframeRoutes(),
  ...getMicroRoutes(),
  {
    path: '/',
    meta: {},
    component: BasicLayout,
    routes: [
      {
        path: '/',
        exact: true,
        redirect: '/home',
      },
      {
        path: '/home',
        meta: { title: t('app.global.home') },
        component: Home,
      },
      {
        path: '/dashboard',
        meta: { title: t('app.global.dashboard') },
        component: Dashboard,
      },
      ...formatRoutes(flattenRoutes),
      {
        path: '/redirect/:path(.*)',
        component: Redirect,
      },
      {
        path: '/404',
        component: Nomatch,
      },
      {
        path: '*',
        redirect: !config.isMainApp ? '/404' : '',
      },
    ],
  },
];

export default routes;
