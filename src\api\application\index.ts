/*
 * @Author: 焦质晔
 * @Date: 2021-02-14 14:59:10
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-05-26 13:46:26
 */
import axios from '@/api/fetch';
import env from '@/config/envMaps';
import { getUserInfo } from '@/utils/cookies';
const { prefix } = env;

// 获取菜单
export const getMenuList = (params) => axios.post(`${prefix}/qfc-base-auth/menu/wbMenuAll`, params);

// 获取数据字典
export const getDictList = (params) =>
  axios.post(`${prefix}/qfc-base-public/dicmain/getAllDict`, params);

// 获取界面权限
export const getAuthList = (params) =>
  axios.post(`${prefix}/qfc-base-auth/auth/page/authInfo`, params);

// 获取按钮权限
export const getAuthBtn = (params) =>
  axios.post(`${prefix}/qfc-base-auth/auth/permissionbutton/listForUnifiedWork`, params);

// 获取收藏导航
export const getStarMenuList = (params) =>
  axios.post(`${prefix}/qfc-base-public/collect/query`, params);

// 设置收藏导航
export const setStarMenuList = (params) =>
  axios.post(`${prefix}/qfc-base-public/collect/insert`, params);

// 获取常用导航
export const getCommonMenus = (params) =>
  axios.post(`${prefix}/qfc-base-public/collect/search`, params);

// 设置常用导航
export const setCommonMenu = (params) =>
  axios.post(`${prefix}/qfc-base-public/collect/save`, params);

// 菜单埋点
export const createMenuPoint = (params) =>
  axios.post(`${prefix}/sys/sysLogin/user/clickMenu`, params);

// =====================

// 获取工作台系统列表
export const getWorkbenchList = (params) =>
  axios.post(`${prefix}/qfc-base-auth/roleGroups/WbGroupByUserId`, params);

// 获取系统列表
export const getSysList = (params) =>
  axios.post(`${prefix}/qfc-base-user/systemUser/select`, params);

// 设置默认工作台
export const setDefaultWorkbench = (params) =>
  axios.post(`${prefix}/qfc-base-user/user/setUserWbGroup`, params);

// 设置默认系统
export const setLastSystem = (params) =>
  axios.post(`${prefix}/qfc-base-user/systemUser/updataLastLogin`, params);

// 获取自定义首页信息
export const getIndexDefine = (params) =>
  axios.post(`${prefix}/qfc-base-public/userComponent/getUserComponents`, params);

// 获取组件定义信息
export const getComponentConfig = (params) =>
  axios.post(`${prefix}/qfc-base-user-profiles/tableconfig/query`, {
    ...params,
    userId: getUserInfo().id,
  });

// 保存组件定义信息
export const saveTableColumnsConfig = (params) =>
  axios.post(`${prefix}/qfc-base-user-profiles/tableconfig/insertupdate`, {
    ...params,
    userId: getUserInfo().id,
  });
