import React, { useEffect, useRef, useState, useCallback } from 'react';
import { QmForm, QmTable, Image, QmDrawer, Pagination } from '@jiaozhiye/qm-design-react';
import css from './index.module.less';
import img from '../../../assets/img.png';
import img1 from '../../../assets/img1.png';
import classNames from 'classnames';
import {
  aiRateShow,
  billApproveRightRate,
  getCount,
  getfileInfosByTraceId,
  getHumanResultList,
  getPythonInParamByTraceId, getTaskRules,
  ruleApproveRightRate,
} from '@/modules/dataoperations/api';
import { CheckCircleOutlined, CloseCircleOutlined } from '@/icons';
import ReactJson from 'react-json-view';
import dayjs from "dayjs";

const AuditDetails = ({ item, taskTypeItem }) => {
  const refFrom = useRef(null);
  const ckcl = useRef(null);
  const [params, setParams] = useState({ systemId: 'YSZC-ESC',     pageNo: 1,
    pageSize: 20,aiCheckTimeStart: `${dayjs().subtract(7, 'day').format('YYYY-MM-DD')} 00:00:00`,
    aiCheckTimeEnd:`${dayjs().format('YYYY-MM-DD')} 23:59:59`});
  const [data, setData] = useState({});
  const [billRightRateFlag, setBillRightRateFlag] = useState('0');
  const [ruleCheckRateFlag, setRuleCheckRateFlag] = useState('0');
  const [loading, setLoading] = useState(false);
  const [table, setTable] = useState([]);
  const [imgList, setImgList] = useState([]);
  const [pageInfo, setPageInfo] = useState({
    pageNo: 1,
    pageSize: 20,
  });

  const [total, setTotal] = useState(0)

  const [billData, setBillData] = useState({});
  const [ruleData, setRuleData] = useState({});
  const [countData, setCountData] = useState({});

  const [pythonInParam, setPythonInParam] = useState('');
  const [drawerpShow, setDrawerpShow] = useState(false);
  const [drawerShow, setDrawerShow] = useState(false);
  const getcolumns = () => {
    return [
      {
        title: '项目',
        align: 'left',
        theadAlign: 'left',
        width: 150,
        dataIndex: 'sysTemName',
      },
      {
        title: '业务ID',
        align: 'left',
        theadAlign: 'left',
        width: 170,
        dataIndex: 'bizId',
      },
      {
        title: '单据ID',
        align: 'left',
        theadAlign: 'left',
        width: 350,
        dataIndex: 'batchId',
      },
      {
        title: '单据AI审核结果',
        align: 'left',
        theadAlign: 'left',
        width: 150,
        dataIndex: 'aiResultFinal',
        render(val) {
          return val == '1' ? (
            <span className={css.color}>
              <CheckCircleOutlined />
              通过
            </span>
          ) : val == '0' ? (
            <span className={css.color1}>
              <CloseCircleOutlined />
              驳回
            </span>
          ) : (
            '-'
          );
        },
      },
      {
        title: '单据人工审核结果',
        align: 'left',
        theadAlign: 'left',
        width: 150,
        dataIndex: 'humanCheckResultFinal',
        render(val) {
          return val == '1' ? (
            <span className={css.color}>
              <CheckCircleOutlined />
              通过
            </span>
          ) : val == '0' ? (
            <span className={css.color1}>
              <CloseCircleOutlined />
              驳回
            </span>
          ) : (
            '-'
          );
        },
      },
      {
        title: '规则名称',
        align: 'left',
        width: 150,
        theadAlign: 'left',
        dataIndex: 'taskName',
      },
      {
        title: '规则ID',
        align: 'left',
        width: 150,
        theadAlign: 'left',
        dataIndex: 'traceId',
      },
      {
        title: '规则材料链接',
        align: 'left',
        theadAlign: 'left',
        width: 130,
        dataIndex: '__action__',
        render(_, row) {
          return (
            <span
              title={row.traceId}
              style={{ color: '#3A2AE4', cursor: 'pointer' }}
              onClick={() => {
                getUrl(row.traceId, row.batchId);
              }}
            >
              查看
            </span>
          );
        },
      },
      // {
      //   title: 'python入参',
      //   align: 'left',
      //   theadAlign: 'left',
      //   width:130,
      //   dataIndex: 'python',
      //   render(_,row){
      //     return <span title = { row.traceId  } style={{color:'#3A2AE4',cursor:'pointer'}} onClick={()=>{getPythonInParamByTraceIdFn(row.traceId,row.batchId)}}>查看</span>
      //   }
      // },
      {
        title: 'AI审核结果',
        align: 'left',
        theadAlign: 'left',
        width: 100,
        dataIndex: 'aiResultSingle',
        render(val) {
          return val == '1' ? (
            <span className={css.color}>
              <CheckCircleOutlined />
              通过
            </span>
          ) : val == '0' ? (
            <span className={css.color1}>
              <CloseCircleOutlined />
              驳回
            </span>
          ) : (
            '-'
          );
        },
      },
      {
        title: '人工审核结果',
        align: 'left',
        theadAlign: 'left',
        width: 120,
        dataIndex: 'humanCheckResultSingle',
        render(val) {
          return val == '1' ? (
            <span className={css.color}>
              <CheckCircleOutlined />
              通过
            </span>
          ) : val == '0' ? (
            <span className={css.color1}>
              <CloseCircleOutlined />
              驳回
            </span>
          ) : (
            '-'
          );
        },
      },
      {
        title: 'AI审核原因',
        align: 'left',
        theadAlign: 'left',
        width: 200,
        dataIndex: 'aiExplain',
      },
      {
        title: '人工审核原因',
        align: 'left',
        theadAlign: 'left',
        width: 200,
        dataIndex: 'humanRefuseReason',
      },
      {
        title: 'AI审核时间',
        align: 'left',
        width: 150,
        theadAlign: 'left',
        dataIndex: 'aiResultTime',
      },
      {
        title: '人工审核时间',
        align: 'left',
        width: 150,
        theadAlign: 'left',
        dataIndex: 'humanCheckTime',
      },
    ];
  };

  const [columns, setcolumns] = useState(getcolumns());

  useEffect(() => {
    aiRateShowFn(params);
    // getTaskRules(params);
    billApproveRightRate(params).then((res) => {
      if (res.code === 200) {
        setBillData(res.data);
      }
    });
    ruleApproveRightRate(params).then((res) => {
      if (res.code === 200) {
        setRuleData(res.data);
      }
    });
    getCount(params).then((res) => {
      if (res.code === 200) {
        setCountData(res.data);
      }
    });
  }, [params]);

  const aiRateShowFn = async (params) => {
    try {
      setLoading(true);
      const res = await getHumanResultList({ ...params });
      setLoading(false);
      if (res.code == 200) {
        setData(res.data ?? {});
        // filterTableData(changeList(res.data.kanBanBillDatas))
        setTable(res.data.data ?? []);
        setTotal(res.data.totalNum ?? 0);
      } else {
        setData({});
        // filterTableData([])
        setTable([]);
        setTotal(0)
      }
    } catch {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (item.length) {
      refFrom.current?.SET_FIELDS_VALUE({ systemId: params.systemId });
      refFrom.current?.SET_FIELDS_VALUE({ time: [`${dayjs().subtract(7, 'day').format('YYYY-MM-DD')} 00:00:00`, `${dayjs().format('YYYY-MM-DD')} 23:59:59`] });
    }
  }, [item]);

  const getUrl = async (traceId, batchId) => {
    setImgList([]);
    setPythonInParam('');
    setDrawerShow(true);
    ckcl.current.START_LOADING();
    const res = await getfileInfosByTraceId({ traceId, batchId });
    ckcl.current.STOP_LOADING();
    if (res.code == 200) {
      setImgList(res.data ?? []);
    } else {
      setImgList([]);
    }
    getPythonInParamByTraceIdFn(traceId, batchId);
  };

  const getPythonInParamByTraceIdFn = async (traceId, batchId) => {
    const res = await getPythonInParamByTraceId({ traceId, batchId });
    if (res.code == 200) {
      setPythonInParam(res.data ?? '');
    }
  };

  return (
    <>
      <QmForm
        ref={refFrom}
        isFieldsDefine={false}
        formType="search"
        layout="vertical"
        items={[
          {
            type: 'SELECT',
            label: '项目名称',
            placeholder: '请选择',
            fieldName: 'systemId',
            options: {
              itemList: item,
            },
          },
          {
            type: 'RANGE_DATE',
            label: 'AI审核日期',
            placeholder: '请选择',
            fieldName: 'time',
          },
          {
            type: 'RANGE_DATE',
            label: '人工审核日期',
            placeholder: '请选择',
            fieldName: 'time1',
          },
          {
            type: 'INPUT',
            label: '单据id',
            placeholder: '请输入',
            fieldName: 'batchId',
          },
          {
            type: 'INPUT',
            label: '规则id',
            placeholder: '请输入',
            fieldName: 'traceId',
          },
          {
            type: 'SELECT',
            label: '规则名称',
            placeholder: '请选择',
            fieldName: 'taskType',
            options: {
              itemList: taskTypeItem,
            },
          },
        ]}
        cols={4}
        isCollapse={false}
        onFinish={(formValues) => {
          const [aiCheckTimeStart, aiCheckTimeEnd] = formValues.time ?? [];
          const [humanCheckTimeStart, humanCheckTimeEnd] = formValues.time1 ?? [];
          setPageInfo({pageNo:1, pageSize: 20})
          setParams({
            aiCheckTimeStart,
            aiCheckTimeEnd,
            humanCheckTimeStart,
            humanCheckTimeEnd,
            billRightRateFlag,
            ruleCheckRateFlag,
            ...formValues,
            pageNo:1,
            pageSize: 20
          });
        }}
      />
      <div className={css.auditdetailsList}>
        <div
          className={classNames(billRightRateFlag === '1' ? css.active : '', css.auditdetailsItem)}
          onClick={() => {
            setBillRightRateFlag('1');
            setRuleCheckRateFlag('0');
            setPageInfo({
              pageNo:1,
              pageSize:20
            })
            setParams({
              ...params,
              billRightRateFlag: '1',
              ruleCheckRateFlag: '0',
              pageNo:1,
              pageSize:20
            });
          }}
        >
          <img src={img} alt="" />
          <div className={css.itemContent}>
            <div className={css.itemName}>单据审核准确率</div>
            <div className={css.itemText}>
              <span>{billData?.quotient ?? '-'}%</span>（{billData.dividend ?? '-'} /{' '}
              {billData?.divisor ?? '-'}）
            </div>
          </div>
        </div>
        <div
          className={classNames(ruleCheckRateFlag === '1' ? css.active : '', css.auditdetailsItem)}
          onClick={() => {
            setBillRightRateFlag('0');
            setRuleCheckRateFlag('1');
            setPageInfo({
              pageNo:1,
              pageSize:20
            })
            setParams({
              ...params,
              billRightRateFlag: '0',
              ruleCheckRateFlag: '1',
              pageNo:1,
              pageSize:20
            });
          }}
        >
          <img src={img1} alt="" />
          <div className={css.itemContent}>
            <div className={css.itemName}>规则审核准确率</div>
            <div className={css.itemText}>
              <span>{ruleData?.quotient ?? '-'}%</span>（{ruleData?.dividend ?? 0}/{' '}
              {ruleData?.divisor ?? '-'}）
            </div>
          </div>
        </div>
      </div>
      <div className={css.auditCard}>
        <div className={classNames(css.auditCardList, css.cardTop)}>
          <div className={css.auditCardText}>
            <div>
              AI审核单据数量<i>{countData?.aiBillApproveCount ?? '-'}</i>
              <span>
                人工复审单据数量<i>{countData?.humanBillApproveCount ?? '-'}</i>
              </span>
            </div>
          </div>
        </div>
        <div className={css.auditCardList}>
          <div className={classNames(css.auditCardText, css.adopt)}>
            <div>
              AI审核通过<i>{countData?.aiApprovePassCount ?? '-'}</i>准确率
              <i>{countData?.tpRightRate ?? '-'}%</i>
              <span>
                ( 人工复审通过<i>{countData?.tpCount ?? '-'}</i> 人工复审驳回{' '}
                <i>{countData?.tnCount ?? '-'}</i>)
              </span>
            </div>
          </div>
          <div className={classNames(css.auditCardText, css.reject)}>
            <div>
              AI审核驳回<i>{countData?.aiApproveRejectCount ?? '-'}</i>准确率
              <i>{countData?.fnRightRate ?? '-'}%</i>
              <span>
                ( 人工复审通过<i>{countData?.fpCount ?? '-'}</i>人工复审驳回
                <i>{countData?.fnCount ?? '-'}</i>)
              </span>
            </div>
          </div>
        </div>
      </div>

      <QmTable
        rowKey={(row) => row.batchId + row.traceId}
        columns={columns}
        columnsChange={(columns) => {
          setcolumns(columns);
        }}
        height={'600px'}
        loading={loading}
        dataSource={table}
        showColumnDefine={false}
        border={false}
        rowHeight={40}
        showSelectCollection={false}
        showFullScreen={false}
        showTableInfo={false}
        infoBarConfig={{ hideAlert: false, position: 'bottom' }}
      ></QmTable>
      <Pagination total={total} current={pageInfo.pageNo} pageSize={pageInfo.pageSize}
      onChange={(page, size) => {
        aiRateShowFn({...params, pageNo: page, pageSize: size});
        setPageInfo({ pageNo: page, pageSize: size});
      }} style={{textAlign: 'right'}}/>

      <QmDrawer
        ref={ckcl}
        width={'800px'}
        open={drawerShow}
        title="查看材料"
        bodyStyle={{ padding: '16px 16px 60px' }}
        showFullScreen={false}
        onClose={() => {
          setDrawerShow(false);
        }}
        loading={false}
      >
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <div style={{ lineHeight: '32px', fontWeight: 'bold', width: '100%' }}>规则材料：</div>
          {!imgList.length ? (
            <div style={{ width: '100%' }}>暂无材料</div>
          ) : (
            <Image.PreviewGroup>
              {imgList.map((item, index) => (
                <Image width={300} style={{ padding: '16px' }} key={index} src={item.fileDownUrl} />
              ))}
            </Image.PreviewGroup>
          )}
          <div style={{ lineHeight: '32px', fontWeight: 'bold', width: '100%' }}>python入参：</div>
          {!pythonInParam ? (
            <div style={{ width: '100%' }}>暂无python入参</div>
          ) : (
            <div style={{ width: '100%', wordBreak: 'break-word' }}>
              <ReactJson src={JSON.parse(pythonInParam)} />
            </div>
          )}
        </div>
      </QmDrawer>

      <QmDrawer
        width={'800px'}
        open={drawerpShow}
        title="python入参"
        bodyStyle={{ padding: '16px 16px 60px' }}
        showFullScreen={false}
        onClose={() => {
          setDrawerpShow(false);
        }}
        loading={false}
      >
        <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          {!pythonInParam ? (
            <div>暂无数据</div>
          ) : (
            <div style={{ width: '100%', wordBreak: 'break-word' }}>{pythonInParam}</div>
          )}
        </div>
      </QmDrawer>
    </>
  );
};

export default AuditDetails;
