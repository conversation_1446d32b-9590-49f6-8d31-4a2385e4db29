import React, { useState, useEffect, useRef } from 'react';
import { Drawer, Select, Radio, Button, Input, Switch, Tooltip, Typography, Tag, message, Divider, Space, Spin, Slider, InputNumber, Row, Col } from 'antd';
import { DeleteOutlined, CopyOutlined, DownOutlined, RightOutlined, SendOutlined, InfoCircleOutlined, CloseOutlined } from '@ant-design/icons';
import styles from './TestChatDrawer.module.less';
import eraser from '@/modules/dataoperations/assets/eraser.svg';
import sendCircle from '@/modules/dataoperations/assets/circleSend.svg';
import sendCircleGray from '@/modules/dataoperations/assets/circleSendGray.svg';
import { faqChat, robotlist } from '@/modules/dataoperations/api/robot';

const { Option } = Select;
const { TextArea } = Input;
const { Text, Link } = Typography;

const TestDrawer = ({ open, onClose, detail }) => {
    const [robots, setRobots] = useState([]);
    const [selectedRobotId, setSelectedRobotId] = useState(null);
    const [selectedEnv, setSelectedEnv] = useState('test'); // 默认测试环境
    const [isDebugMode, setIsDebugMode] = useState(true); // 调试模式默认开启
    const [similarityThreshold, setSimilarityThreshold] = useState(0.8); // 相似度阈值状态
    const [chatMessages, setChatMessages] = useState([]);
    const [inputValue, setInputValue] = useState('');
    const [loadingRobots, setLoadingRobots] = useState(false);
    const [isSending, setIsSending] = useState(false); // 标记是否正在发送消息
    const chatContentRef = useRef(null);

    // --- "详细数据" 相关状态 ---
    const [currentDetailedData, setCurrentDetailedData] = useState(null);

    // FAQ块的展开状态
    const [expandedFaq, setExpandedFaq] = useState({}); // { messageId: true/false }


    useEffect(() => {
        if (open) {
            setLoadingRobots(true);
            robotlist({ pageSize: 99999, pageNum: 1 }).then(res => {
                if (res.code === 200) {
                    const robotData = res.data.records.map(robot => ({
                        id: robot.id,
                        name: robot.robotName,
                    }));
                    setRobots(robotData);
                    if (robotData.length > 0) {
                        setSelectedRobotId(prev => {
                            if (!prev || prev !== detail.id) {
                                return detail.id || robotData[0].id; // 默认选择第一个机器人
                            }
                            return prev;
                        });
                    }
                } else {
                    message.error('获取机器人列表失败，请稍后重试');
                }
            }).catch(err => {
                console.error('获取机器人列表失败:', err);
                message.error('获取机器人列表失败，请稍后重试');
            }).finally(() => {
                setLoadingRobots(false);
            });

            if (chatMessages.length === 0) {
                setChatMessages([{
                    id: Date.now(),
                    type: 'bot',
                    text: '您好！智能对话机器人为您服务，请问有什么可以帮您？',
                }]);
            }
        }
    }, [open, detail.id]);

    useEffect(() => {
        if (chatContentRef.current) {
            chatContentRef.current.scrollTop = chatContentRef.current.scrollHeight;
        }
    }, [chatMessages]);


    const handleRobotChange = (value) => {
        setSelectedRobotId(value);
        setChatMessages([{
            id: Date.now(),
            type: 'bot',
            text: `您好！${robots.find(r => r.id === value)?.name || ''}为您服务，请问有什么可以帮您？`,
        }]);
        setCurrentDetailedData(null);
    };

    const handleEnvChange = (e) => {
        setSelectedEnv(e.target.value);
    };

    const handleDebugChange = (checked) => {
        setIsDebugMode(checked);
    };

    const handleCopyId = (id, type = "ID") => {
        if (!id || id === 'N/A') {
            message.warning('没有可复制的内容');
            return;
        }
        navigator.clipboard.writeText(id).then(() => {
            message.success(`${type} 已复制: ${id}`);
        }).catch(err => {
            message.error('复制失败');
        });
    };

    const toggleFaqExpand = (messageId) => {
        setExpandedFaq(prev => ({ ...prev, [messageId]: !prev[messageId] }));
    };

    const handleSendMessage = async () => {
        if (!inputValue.trim() || isSending) return;
        if (!selectedRobotId) {
            message.warning('请先选择一个机器人！');
            return;
        }

        setIsSending(true);
        const currentQuery = inputValue;

        const userMessage = {
            id: `user-${Date.now()}`,
            type: 'user',
            text: currentQuery,
        };

        setChatMessages(prev => [...prev, userMessage]);
        setInputValue('');

        try {
            const robRes = await faqChat({
                robotId: selectedRobotId,
                env: selectedEnv,
                query: currentQuery,
                debugMode: isDebugMode ? 'open' : 'close',
                similarityThreshold: similarityThreshold,
            });

            if (robRes.code !== 200 || !robRes.data) {
                message.error(robRes.message || '机器人回复失败，请稍后重试');
                const errorBotMessage = {
                    id: `bot-error-${Date.now()}`,
                    type: 'bot',
                    text: `抱歉，请求失败：${robRes.message || '未知错误'}`
                };
                setChatMessages(prev => [...prev, errorBotMessage]);
                return;
            }

            const apiData = robRes.data;
            const newMessages = [];

            const botMessage = {
                id: `bot-${Date.now()}`,
                type: 'bot',
                text: apiData.answer || '抱歉，我暂时无法回答您的问题，请试试其他问法。',
            };
            newMessages.push(botMessage);

            if (isDebugMode) {
                const isMatched = !!apiData.answer;

                // --- MODIFICATION START: Build matches from suggestions ---
                let matchesData = [];
                if (isMatched && apiData.suggestions && Array.isArray(apiData.suggestions) && apiData.suggestions.length > 0) {
                    // If suggestions array exists, map it
                    matchesData = apiData.suggestions.map((s, index) => ({
                        key: `${s.originalKnowledgeId}-${index}`,
                        line1Text: s.originalQuestion || 'N/A',
                        line2Text: `类目: ${s.categoryName || 'N/A'}`,
                        matchDegree: `${(s.score * 100).toFixed(2)}%`,
                        idToCopy: s.originalKnowledgeId || 'N/A',
                    }));
                } else if (isMatched) {
                    // Fallback for older API versions or if suggestions is empty
                    matchesData = [{
                        key: apiData.originalKnowledgeId || `match-${Date.now()}`,
                        line1Text: apiData.originalQuestion || 'N/A',
                        line2Text: `类目: ${apiData.categoryName || 'N/A'}`,
                        matchDegree: `${(apiData.score * 100).toFixed(2)}%`,
                        idToCopy: apiData.originalKnowledgeId || 'N/A',
                    }];
                }
                // --- MODIFICATION END ---

                const faqBlockContent = {
                    id: `faq-${Date.now()}`,
                    type: 'faq',
                    originalQuery: currentQuery,
                    chatId: apiData.conversationId,
                    knowledgeId: apiData.originalKnowledgeId,
                    knowledgeTitle: apiData.originalQuestion || 'N/A',
                    categoryPath: apiData.categoryName || 'N/A',
                    timeTaken: `${apiData.timeTaken || 0}ms`,
                    matchStatusTag: isMatched ? '有匹配' : '无匹配',
                    displayItems: [
                        { label: 'ChatID', value: apiData.conversationId || 'N/A', copyable: true },
                        { label: '知识ID', value: apiData.originalKnowledgeId || 'N/A', copyable: true },
                        { label: '知识标题', value: apiData.originalQuestion || 'N/A' },
                        { label: '类目路径', value: apiData.categoryName || 'N/A' },
                        { label: '耗时', value: `${apiData.timeTaken || 0}ms` },
                    ],
                    detailedDataForLeftPanel: {
                        queryText: currentQuery,
                        segments: '分词信息暂未提供',
                        timeTaken: apiData.timeTaken || 0,
                        matchStatus: isMatched ? '有匹配' : '无匹配',
                        matches: matchesData, // Use the new matchesData array
                    }
                };
                newMessages.push(faqBlockContent);
            }

            setChatMessages(prev => [...prev, ...newMessages]);

        } catch (err) {
            console.error('发送消息异常:', err);
            message.error('发送消息异常，请检查网络或联系管理员');
            const errorBotMessage = {
                id: `bot-error-${Date.now()}`,
                type: 'bot',
                text: '抱歉，系统出现异常，请稍后再试。'
            };
            setChatMessages(prev => [...prev, errorBotMessage]);
        } finally {
            setIsSending(false);
        }
    };


    const handleClearChat = () => {
        setChatMessages([{
            id: Date.now(),
            type: 'bot',
            text: `您好！${robots.find(r => r.id === selectedRobotId)?.name || '智能对话机器人'}为您服务，请问有什么可以帮您？`,
        }]);
        setCurrentDetailedData(null);
        message.success('对话已清除');
    };

    const handleShowFaqDetailInLeftPanel = (data) => {
        setCurrentDetailedData(data);
        message.info('详细数据已更新至左侧面板。');
    };

    const selectedRobot = robots.find(r => r.id === selectedRobotId);

    return (
        <Drawer
            title="测试设置"
            placement="right"
            onClose={onClose}
            open={open}
            width="85%"
            destroyOnClose
            bodyStyle={{ padding: 0, height: 'calc(100vh - 55px)' }}
            maskClosable={false}
        >
            <div className={styles.drawerContainer}>
                {/* 左侧设置面板 */}
                <div className={styles.settingsPanel}>

                    <div className={styles.settingItem}>
                        <Text className={styles.label}><Text type="danger">*</Text> 选择机器人</Text>
                        <Select
                            style={{ width: '100%' }}
                            placeholder="请选择机器人"
                            value={selectedRobotId}
                            onChange={handleRobotChange}
                            loading={loadingRobots}
                            showSearch
                            filterOption={(input, option) =>
                                option.children.toLowerCase().includes(input.toLowerCase())
                            }
                        >
                            {robots.map(robot => (
                                <Option key={robot.id} value={robot.id}>{robot.name}</Option>
                            ))}
                        </Select>
                    </div>

                    <div className={styles.settingItem}>
                        <Text className={styles.label}><Text type="danger">*</Text> 选择环境</Text>
                        <Radio.Group onChange={handleEnvChange} value={selectedEnv} optionType='default'>
                            <Radio value="prod">正式环境</Radio>
                            <Radio value="test">测试环境</Radio>
                        </Radio.Group>
                    </div>

                    <div className={styles.settingItem}>
                        <Text className={styles.label}>相似度</Text>
                        <Row align="middle">
                            <Col span={18}>
                                <Slider
                                    min={0}
                                    max={1}
                                    onChange={setSimilarityThreshold}
                                    value={typeof similarityThreshold === 'number' ? similarityThreshold : 0}
                                    step={0.1}
                                />
                            </Col>
                            <Col span={4}>
                                <InputNumber
                                    min={0}
                                    max={1}
                                    style={{ margin: '0 16px' }}
                                    step={0.1}
                                    value={similarityThreshold}
                                    onChange={(value) => setSimilarityThreshold(value === null ? 0 : value)}
                                />
                            </Col>
                        </Row>
                    </div>

                    {currentDetailedData && (
                        <>
                            <Typography.Title level={5} className={styles.sectionTitle}>详细数据</Typography.Title>
                            <div className={styles.flexBetween}>
                                <div className={styles.titleStart}>FAQ问答</div>
                                <div className={styles.flexEnd12}>
                                    <div className={styles.grayText}>耗时: {currentDetailedData.timeTaken}ms</div>
                                    <Tag color={currentDetailedData.matchStatus === '有匹配' ? 'success' : 'warning'}>
                                        {currentDetailedData.matchStatus}
                                    </Tag>
                                </div>
                            </div>
                            <div className={styles.detailDataSection}>
                                {currentDetailedData.matches?.length > 0 ? (
                                    // --- MODIFICATION START: Render list and highlight first item ---
                                    currentDetailedData.matches.map((item, index) => (
                                        <div
                                            key={item.key}
                                            className={`${styles.detailItem} ${index === 0 ? styles.active : ''}`} // Only highlight the first item
                                        >
                                            <div className={styles.itemContent}>
                                                <div className={styles.line1}>{item.line1Text}</div>
                                                <div className={styles.line2}>
                                                    <Text className={styles.line2Text}>{item.line2Text}</Text>
                                                    <Button
                                                        type="link"
                                                        size="small"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            handleCopyId(item.idToCopy, "ID");
                                                        }}
                                                        className={styles.copyButton}
                                                    >
                                                        复制ID
                                                    </Button>
                                                </div>
                                            </div>
                                            <div className={styles.itemMatchDegree}>
                                                匹配度: <span className={styles.degreeValue}>{item.matchDegree}</span>
                                            </div>
                                        </div>
                                    ))
                                    // --- MODIFICATION END ---
                                ) : (
                                    <div style={{ padding: '20px', textAlign: 'center', color: '#888' }}>无匹配结果</div>
                                )}
                            </div>
                        </>
                    )}

                </div>

                {/* 右侧聊天面板 */}
                <div className={styles.chatPanel}>
                    <div className={styles.chatHeader}>
                        <Text className={styles.robotName}>{selectedRobot ? selectedRobot.name : '请选择机器人'}</Text>
                        <div className={styles.actions}>
                            <Space>
                                <Text>调试</Text>
                                <Switch checked={isDebugMode} onChange={handleDebugChange} />
                            </Space>
                            <Tooltip title="清除对话记录">
                                <img src={eraser} onClick={handleClearChat} alt="清除" style={{ width: 24, height: 24, cursor: 'pointer' }} />
                            </Tooltip>
                        </div>
                    </div>

                    <div className={styles.chatContent} ref={chatContentRef}>
                        {chatMessages.map(msg => (
                            <div key={msg.id} className={`${styles.message} ${msg.type === 'user' ? styles.userMessage : styles.botMessage}`}>
                                {msg.type !== 'faq' && (
                                    <div className={styles.messageBubble}>
                                        <Text>
                                            <div className= {styles.innerHtml} dangerouslySetInnerHTML={{ __html: msg.text }} />
                                        </Text>
                                    </div>
                                )}
                                {msg.type === 'faq' && (
                                    <div className={styles.faqBlock}>
                                        <div className={styles.faqHeader}>
                                            <div>
                                                <Text strong className={styles.faqTitleFromResp}>
                                                    {msg.knowledgeTitle}
                                                </Text>
                                            </div>
                                            <div>
                                                <Text className={styles.timeInfo}>耗时: {msg.timeTaken}</Text>
                                                <Tag className={`${styles.matchTag} ${msg.matchStatusTag === '有匹配' ? styles.matched : styles.notMatched}`}>
                                                    {msg.matchStatusTag}
                                                </Tag>
                                            </div>
                                        </div>

                                        {msg.displayItems.map(item => (
                                            <div key={item.label} className={styles.faqItem}>
                                                <Text className={styles.label}>{item.label}:</Text>
                                                <Text copyable={item.copyable ? { tooltips: [`复制 ${item.label}`, '已复制'] } : false}>
                                                    {item.value}
                                                </Text>
                                            </div>
                                        ))}

                                        <Divider style={{ margin: '12px 0' }} />

                                        <Button
                                            type="primary"
                                            ghost
                                            onClick={() => handleShowFaqDetailInLeftPanel(msg.detailedDataForLeftPanel)}
                                        >
                                            详细数据
                                        </Button>
                                    </div>
                                )}
                            </div>
                        ))}
                        {isSending && (
                            <div className={`${styles.message} ${styles.botMessage}`}>
                                <div className={styles.messageBubble}>
                                    <Spin size="small" />
                                    <Text style={{ marginLeft: 8 }}>正在思考中...</Text>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className={styles.chatInputArea}>
                        <TextArea
                            className={styles.input}
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            onPressEnter={(e) => {
                                if (!e.shiftKey) {
                                    e.preventDefault();
                                    handleSendMessage();
                                }
                            }}
                            placeholder="请输入问题试试..."
                            disabled={isSending}
                        />
                        <div className={styles.btnArea}>
                            {inputValue.trim() && !isSending ?
                                <img className={styles.sendImg} src={sendCircle} width={30} height={30} onClick={handleSendMessage} alt="send" />
                                :
                                <img className={styles.notAllowd} src={sendCircleGray} width={30} height={30} alt="cannot send" />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </Drawer>
    );
};

export default TestDrawer;