/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-03-07 15:31:31
 */
import React from 'react';
import classNames from 'classnames';
import { getUserInfo } from '@/utils/cookies';

import { getZuhuList, getSysList } from '@framework/api/setting';

import { Select } from '@jiaozhiye/qm-design-react';

import css from './index.module.less';

const Main = () => {
  const [zuhuItems, setZuhuItems] = React.useState([]);
  const [sysItems, setSysItems] = React.useState([]);

  const getZuhuInfo = async () => {
    const res = await getZuhuList({ userId: getUserInfo().id });
    if (res.code === 200) {
      setZuhuItems(res.data || []);
    }
  };

  const getSysInfo = async () => {
    const res = await getSysList({ userId: getUserInfo().id });
    if (res.code === 200) {
      setSysItems(res.data.dataList || []);
    }
  };

  React.useEffect(() => {
    getZuhuInfo();
    getSysInfo();
  }, []);

  const { tenantId, systemId } = getUserInfo();

  return (
    <div className={classNames(css.sys_setting_main)}>
      <div className={classNames(css.items)}>
        <h5>租户设置</h5>
        <Select defaultValue={tenantId} style={{ width: '100%' }}>
          {zuhuItems.map((x) => (
            <Select.Option key={x.tenantId} value={x.tenantId}>
              {x.tenantName}
            </Select.Option>
          ))}
        </Select>
      </div>
      <div className={classNames(css.items)}>
        <h5>系统设置</h5>
        <Select defaultValue={systemId} style={{ width: '100%' }}>
          {sysItems.map((x) => (
            <Select.Option key={x.systemId} value={x.systemId}>
              {x.systemName}
            </Select.Option>
          ))}
        </Select>
      </div>
    </div>
  );
};

export default Main;
