/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 14:18:12
 */
import React from 'react';
import classNames from 'classnames';
import { useLocale } from '@/hooks';

import NavList from './NavList';
import { Menu } from '@jiaozhiye/qm-design-react';
import { AppstoreFilled } from '@/icons';

import './index.less';

const AllNav: React.FC = () => {
  const { t } = useLocale();

  const [visible, setVisible] = React.useState<boolean>(false);
  const wrapperRef = React.useRef<HTMLDivElement>(null);

  const visibleChange = ({ domEvent: ev }) => {
    ev.stopPropagation();
    setVisible(!visible);
  };

  const closeHandle = () => {
    setVisible(false);
  };

  const getOuterWidth = () => {
    return wrapperRef.current?.offsetWidth;
  };

  const cls = {
    [`app-all-nav`]: true,
    selected: visible,
  };

  const items = [
    {
      key: 'all-nav',
      icon: <AppstoreFilled />,
      label: t('app.sidebar.allNavTitle'),
      onClick: visibleChange,
    },
  ];

  return (
    <div ref={wrapperRef} className={classNames(cls)}>
      <Menu mode="inline" theme="dark" inlineIndent={20} selectable={false} items={items} />
      <NavList visible={visible} getWidth={getOuterWidth} onChange={closeHandle} />
    </div>
  );
};

export default AllNav;
