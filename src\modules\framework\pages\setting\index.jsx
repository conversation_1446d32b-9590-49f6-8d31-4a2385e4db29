/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-03-07 09:37:17
 */
import React from 'react';

import { QmDrawer } from '@jiaozhiye/qm-design-react';
import { SettingOutlined } from '@/icons';

import Main from './main';

import './index.less';

const Setting = () => {
  const [visible, setVisible] = React.useState(false);

  const clickHandle = () => {
    setVisible(true);
  };

  return (
    <>
      <div className={`app-sys-setting`} onClick={() => clickHandle()}>
        <span className={`sys-setting-trigger`}>
          <SettingOutlined className={`icon`} />
        </span>
      </div>
      <QmDrawer
        visible={visible}
        title="系统设置"
        width={300}
        loading={false}
        maskClosable={true}
        onClose={() => {
          setVisible(false);
        }}
      >
        <Main />
      </QmDrawer>
    </>
  );
};

export default Setting;
