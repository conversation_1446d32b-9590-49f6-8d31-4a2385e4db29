/*
 * @Author: 焦质晔
 * @Date: 2021-07-07 07:30:32
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-06 13:35:26
 */
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.svg';
declare module '*.png';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.gif';
declare module '*.bmp';
declare module '*.tiff';

declare let __webpack_public_path__: string;

declare namespace JSX {
  interface IntrinsicElements {
    'micro-app': any;
  }
}

declare interface Navigator {
  msSaveBlob?: (blob: Blob, defaultName?: string) => boolean;
}

declare interface Window {
  microApp: any;
  __MAIM_APP_ENV__: boolean;
  __MICRO_APP_ENVIRONMENT__: boolean;
  __MICRO_APP_PUBLIC_PATH__: string;
  __MICRO_APP_NAME__: string;
  __dict_data__: Record<string, any>;
  __auth_data__: Record<string, any>;
  __auth_btn_data__: Record<string, any>;
}
