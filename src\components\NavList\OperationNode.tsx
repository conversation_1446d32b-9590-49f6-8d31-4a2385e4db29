/*
 * @Author: 焦质晔
 * @Date: 2023-11-18 17:07:25
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-19 00:35:23
 */
import React from 'react';
import classNames from 'classnames';

import { Dropdown, Menu } from '@jiaozhiye/qm-design-react';
import { EllipsisOutlined, DownOutlined, ThreeDownIcon } from '@/icons';

import type { INavMenu } from '@/store/reducers/app';

type IProps = {
  items: INavMenu[];
  className: string | boolean;
  scrollToTab: (key: string) => void;
  onItemClick: (item: INavMenu, path: string, ev) => void;
};

const OperationNode: React.FC<IProps> = (props) => {
  const { items, className, scrollToTab, onItemClick } = props;

  const renderMenus = () => {
    const list = items.map((x) => ({
      key: x.id,
      label: (
        <>
          {x.shortName || x.title}
          {x.type === 0 && <DownOutlined className={`icon`} />}
          {x.type === 2 && (
            <span className={`anticon anticon-down icon`}>
              <ThreeDownIcon />
            </span>
          )}
        </>
      ),
      onClick: ({ domEvent }) => {
        scrollToTab(x.id);
        !x.virtual && onItemClick(x, x.key, domEvent);
      },
    }));
    return <Menu items={list} />;
  };

  return (
    <div className={classNames(`operations`, className)}>
      <Dropdown overlayClassName={`app-nav-list__popper`} dropdownRender={() => renderMenus()}>
        <span className={`more icon`}>
          <EllipsisOutlined />
        </span>
      </Dropdown>
    </div>
  );
};

export default OperationNode;
