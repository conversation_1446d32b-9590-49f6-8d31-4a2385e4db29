/*
 * @Author: 焦质晔
 * @Date: 2023-09-28 07:33:58
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-14 10:29:40
 */
import React from 'react';
import { useTool } from '@/hooks';

import { Tooltip } from '@jiaozhiye/qm-design-react';

import './index.less';

type IProps = {
  title: string;
  search?: string;
  style?: React.CSSProperties;
};

const ToCustomPage: React.FC<IProps> = (props) => {
  const { title, search = '', style } = props;
  const { openView } = useTool();

  return (
    <Tooltip placement="left" title={title}>
      <div
        className={`app-to-custom-page`}
        style={style}
        onClick={() => openView('/chp/setting' + search)}
      />
    </Tooltip>
  );
};

export default ToCustomPage;
