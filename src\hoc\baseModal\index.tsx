/*
 * @Author: 焦质晔
 * @Date: 2023-10-24 10:17:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-10-25 13:18:05
 */
import React from 'react';
import { Notification, destroyAlert } from '@/utils';

type NormalizeConfig = {
  id?: string | number;
  type?: 'add' | 'edit' | 'show';
  isExposed?: boolean;
  businessKey?: string; // for BPM
  processInstanceId?: string; // for BPM
  onClose?: (...args: any[]) => void;
};

type BaseModalProps = {
  normalizeConfig?: NormalizeConfig;
} & Record<string, any>;

const NOOP = () => {};

/**
 * @description BaseModal 高阶组件
 */
const withBaseModal = <T extends BaseModalProps>(
  WrappedComponent: React.ComponentType<Omit<T, 'normalizeConfig'>>
): any => {
  const InnerComponent = React.forwardRef<any, T>((props, ref) => {
    const { normalizeConfig = {}, ...rest } = props;

    // 标准化参数名
    const normalKeys = ['id', 'type', 'isExposed', 'businessKey', 'processInstanceId', 'onClose'];

    const msgNodeList: React.ReactNode[] = [];

    const results1: string[] = [];
    Object.keys(normalizeConfig).forEach((x) => {
      if (!normalKeys.includes(x)) {
        results1.push(x);
      }
    });
    if (results1.length) {
      // prettier-ignore
      msgNodeList.push(
        `参数 [${results1.join(', ')}] 为非标准化参数，请修改其名称或者从 \`normalizeConfig\` 中移除！`
      );
    }

    const results2: string[] = [];
    Object.keys(rest).forEach((x) => {
      if (normalKeys.includes(x)) {
        results2.push(x);
      }
    });
    if (results2.length) {
      msgNodeList.push(
        `参数名 [${results2.join(', ')}] 属于标准化参数，请在 \`normalizeConfig\` 中设置！`
      );
    }

    // 注入必要的默认参数
    const paramKeys = [...Object.keys(normalizeConfig), ...Object.keys(rest)];
    if (!paramKeys.includes('type')) {
      Object.assign(rest, { type: 'add' });
    }
    if (!paramKeys.includes('onClose')) {
      Object.assign(rest, { onClose: NOOP });
    }

    React.useEffect(() => {
      if (msgNodeList.length) {
        Notification(
          msgNodeList.map((x, i) => <li key={i}>{x}</li>) as unknown as string,
          'warning'
        );
      }
      return () => destroyAlert();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return <WrappedComponent ref={ref} {...normalizeConfig} {...rest} />;
  });

  InnerComponent.displayName = 'InnerComponent';

  return InnerComponent;
};

export default withBaseModal;
