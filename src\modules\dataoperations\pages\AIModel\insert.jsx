import React, {useEffect, useState} from "react";
import { Button, Cascader, Input, message, QmModal, Radio, Space } from '@jiaozhiye/qm-design-react';
import {Form, Select} from 'antd'
import { Api } from '@/modules/dataoperations/api/AiScene';
import { getUserInfo } from '@/utils/cookies';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';

export const Insert = (props) => {

  const [form] = Form.useForm();

  const [sceneList, setSceneList] = useState([]);

  const [modelList, setModelList] = useState([]);

  const getAiTechnologyModelList = async () => {
    const res = await Api.getAiTechnologyModelList({})
    if(res.code === 200) {
      setModelList(res.data)
    }
  }

  const getAiSceneList = async () => {
    const res = await Api.getAiSceneList({
      pageNum: 1,
      pageSize: 999
    })
    if(res.code === 200) {
      setSceneList(res.data.list)
    }
  }


  const submit = async () => {
    await form.validateFields()
    const value = form.getFieldsValue([
      'auditModelName', 'aiAuditModelSceneList', 'aiAuditModelPointList', 'aiAuditModelTechnologyList', 'id'
    ])
    const param = {
      ...value,
      createUserName: getUserInfo().loginName
    }
    console.log(param);
    const res = await Api.insertOrUpdateModel(param)
    if(res.code === 200) {
      message.success('操作成功')
      props.onClose(true)
    }
  }

  useEffect(() => {
    if(!props.visible) {
      form.resetFields()
      return
    }
    console.log(props.row);
    if(props.row) {
      form.setFieldsValue({
        ...props.row,
        aiAuditModelScene: props.row.aiAuditModelSceneList?.map((item) => item.sceneCode),
        aiAuditModelTechnology: props.row.aiAuditModelTechnologyList?.map((item) => item.technologyModelCode)
      })
    }


  }, [props.visible]);

  useEffect(() => {
    getAiTechnologyModelList()
    getAiSceneList()
  }, []);


  return (
    <QmModal
      visible={props.visible}
      title={'AI审核模型'}
      width={800}
      onCancel={props.onCancel}
      onClose={props.onClose}
    >
      <Form form={form}  labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Form.Item name={'auditModelName'} label={'AI审核模型名称'} rules={[{ required: true, max: 20}]}>
          <Input max={20}/>
        </Form.Item>
        <Form.Item name={'aiAuditModelScene'} label={'AI审核场景'} rules={[{ required: true}]}>
          <Select options={sceneList} mode={'multiple'} placeholder={'请选择'}
                  fieldNames={{label: 'sceneName', value: 'sceneCode'}}
                  onChange={(e, value) => {
                    console.log(value);
                    form.setFieldValue('aiAuditModelSceneList', value)
                  }}
          />
        </Form.Item>
        <Form.List name="aiAuditModelPointList" initialValue={[{}]} wrapperCol={{ span: 24 }}>
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                  <Form.Item
                    key={key}
                    {...restField}
                    name={[name, 'auditPointName']}
                    label={`审核点${name + 1}`}
                    rules={[{ required: true}]}
                    labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}
                  >
                    <Input placeholder="请输入" style={{width: '100%'}}
                           suffix={
                             name !== 0 && <MinusCircleOutlined onClick={() => remove(name)} />
                           }
                    />
                  </Form.Item>
              ))}
              <Form.Item wrapperCol={{ span: 24 }}>
                {
                  fields.length < 10 && <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                    添加审核点
                  </Button>
                }
              </Form.Item>
            </>
          )}
        </Form.List>
        <Form.Item name={'aiAuditModelTechnology'} label={'技术模型'} rules={[{ required: true}]}>
          <Select options={modelList} mode={'multiple'} placeholder={'请选择'}
                  fieldNames={{label: 'technologyModelName', value: 'technologyModelCode'}}
                  onChange={(e, value) => {
                    console.log(value);
                    form.setFieldValue('aiAuditModelTechnologyList', value)
                  }}
                  max={30}
          />
        </Form.Item>
      </Form>
      <div style={{width: '100%', alignItems: 'center', textAlign: 'center'}}>
        <Button type={'primary'} onClick={submit}>提交</Button>
      </div>
    </QmModal>
  );
};
export default Insert