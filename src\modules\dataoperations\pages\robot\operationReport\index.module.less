// OperationReport.module.less

.pageContainer {
    padding: 24px;
}

.filterCard {
    :global(.ant-card-body) {
        padding: 16px 24px;
    }
}

.filterLabel {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
}

.card1 {
    :global {
        .ant-card-body {
            background-image: url('../../../assets/bcbn1.svg');
            background-position: right top;
            background-repeat: no-repeat;
        }
    }
}

.card2 {
    :global {
        .ant-card-body {
            background-image: url('../../../assets/bcbn2.svg');
            background-position: right top;
            background-repeat: no-repeat;
        }
    }
}

.card3 {
    :global {
        .ant-card-body {
            background-image: url('../../../assets/bcbn3.svg');
            background-position: right top;
            background-repeat: no-repeat;
        }
    }
}

.statCard {
    height: 104px;

    :global {
        .ant-card-body {
            padding: 20px 24px;
            height: 100%;
        }
    }

    .statContent {
        flex: 1;
    }

    .statLabel {
        font-size: 16px;
        font-weight: 500;
        line-height: 16px;
        letter-spacing: 0px;

        color: #262626;
        margin-bottom: 16px;
        display: block;
    }

    .statValue {
        font-family: Qimiao Variable Type;
        font-size: 20px;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 0px;

        color: #262626;
    }

    .statDetail {

        font-family: Qimiao Variable Type;
        font-size: 12px;
        font-weight: normal;
        line-height: 12px;
        text-align: start;
        letter-spacing: 0px;

        color: #666672;
        div {
            margin-bottom: 8px;
        }
    }

    .statIconWrapper {
        background-color: #e6f7ff;
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .statIcon {
        font-size: 28px;
        color: #1890ff;
    }
}

.listItem {
    padding: 12px 0 !important; // 覆盖antd默认padding
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }
}

.flexGap24 {
    display: flex;
    gap: 24px;
    align-items: center;
    justify-content: start;
}

.rankBadgeBase {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    border-radius: 50%;
    color: #fff;
    font-size: 12px;
    margin-right: 12px;
}

.rankBadgeTop3 {
    .rankBadgeBase();
    background-color: #1890ff;
}

.rankBadgeDefault {
    .rankBadgeBase();
    background-color: #bfbfbf;
}

.hitCount {
    color: #595959;
}
.cards2Area {
    :global {
        .ant-card-head {
            border-bottom: none;
        }
        .ant-card-head-title {
            padding: 24px 0 4px 0;
        }
        .ant-card-extra {
            padding-bottom: 0;
        }
        .ant-card-body {
            padding: 12px 16px 16px 16px;
        }
        .ant-table-thead > tr > th {
            background-color: #fff;
        }
    }
}