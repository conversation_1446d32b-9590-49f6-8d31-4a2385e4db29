/*
 * @Author: 焦质晔
 * @Date: 2022-05-29 08:54:35
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-20 10:54:42
 */
import React from 'react';
import classNames from 'classnames';

import type { TimeoutHandle } from '@/utils/types';

import './index.less';

type IProps = {
  offset: number;
  min: number;
  max: number;
  threshold?: number;
  disabled?: boolean;
  hidden?: boolean;
  style?: React.CSSProperties;
  onDrag: (value: number) => void;
  onDragEnd?: (value: number) => void;
  onDragChange?: (bool: boolean) => void;
};

const SplitView: React.FC<IProps> = (props) => {
  const { offset, min, max, threshold, disabled, hidden, style, onDrag, onDragEnd, onDragChange } =
    props;

  const [active, setActive] = React.useState<boolean>(false);
  const [hover, setHover] = React.useState<boolean>(false);
  const timerRef = React.useRef<TimeoutHandle>();

  const dragStart = (ev) => {
    ev.preventDefault();
    setActive(true);
    document.addEventListener('mousemove', moving, { passive: true });
    document.addEventListener('mouseup', dragStop, { passive: true, once: true });
    onDragChange?.(true);
  };

  const dragStop = () => {
    setActive(false);
    document.removeEventListener('mousemove', moving);
    onDragChange?.(false);
    onDragEnd?.(offset);
  };

  const moving = (ev: MouseEvent) => {
    const { pageX } = ev;
    let offset = pageX;
    // 阈值
    if (threshold) {
      if (offset > min && offset <= (min + threshold) / 2) {
        offset = min;
      }
      if (offset < threshold && offset > (min + threshold) / 2) {
        offset = threshold;
      }
    }
    if (offset < min) {
      offset = min;
    }
    if (offset > max) {
      offset = max;
    }
    onDrag(offset);
  };

  const hoverHandle = () => {
    clearTimeout(timerRef.current);
    timerRef.current = setTimeout(() => setHover(true), 200);
  };

  const leaveHandle = () => {
    clearTimeout(timerRef.current);
    setHover(false);
  };

  const cls = {
    [`resize-bar`]: true,
    [`vertical`]: true,
    [`hover`]: hover,
    [`active`]: active,
    [`disabled`]: disabled,
  };

  return !hidden ? (
    <div className={`app-split-view horizontal`} style={style}>
      <div
        className={classNames(cls)}
        style={{ left: offset }}
        onMouseDown={dragStart}
        onMouseOver={hoverHandle}
        onMouseLeave={leaveHandle}
      />
    </div>
  ) : null;
};

export default SplitView;
