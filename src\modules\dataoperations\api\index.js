import axios from '@/api/fetch';

/*export const getSystemInfo = (params) => {
  return axios.post('/kanBan/getSystemInfo' , params)
}*/

export const getfileInfosByTraceId = (params) => {
  return axios.post('/kanBan/getfileInfosByTraceId' , params)
}

export const aiRateShow = (params) => {
  return axios.post('/kanBan/aiRateShow' , params)
}

export const aiTrendChard = (params) => {
  return axios.post('/kanBan/aiTrendChard' , params)
}

export const getTaskRules = (params) => {
  return axios.post('/kanBan/getTaskRules' , params)
}


export const getPythonInParamByTraceId = (params) => {
  return axios.post('/kanBan/getPythonInParamByTraceId' , params)
}


export const getHumanResultList = (params) => {
  return axios.post('/aio/operationCenter/getHumanResultList' , params)
}

export const getSystemInfo = (params) => {
  return axios.post('/aio/operationCenter/getSystemInfo' , params)
}

/*export const getTaskRules = (params) => {
  return axios.post('/aio/operationCenter/getTaskRules' , params)
}*/

export const billApproveRightRate = (params) => {
  return axios.post('/aio/operationCenter/billApproveRightRate' , params)
}

export const ruleApproveRightRate = (params) => {
  return axios.post('/aio/operationCenter/ruleApproveRightRate' , params)
}

export const getCount = (params) => {
  return axios.post('/aio/operationCenter/getCount' , params)
}

export const getFileInfosByTraceId = (params) => {
  return axios.post('/aio/operationCenter/getFileInfosByTraceId' , params)
}

/**
 * 保存会话记录
 * @param {*} params 
 * @returns 
 */
export const saveSessionRecord = (params) => {
  return axios.post('/aio/aiChat/saveSessionRecord' , params)
}

/**
 * 查询会话记录
 */
export const querySessionRecord = (params) => {
  return axios.post('/aio/aiChat/querySessionRecord' , params)
}
/**
 * 查询会话详情
 * id 对话id
 */
export const querySessionDetail = (params) => {
  return axios.post('/aio/aiChat/querySessionDetail' , {...params, delFlag: 0})
}
/**
 * 删除会话记录
 * @param id
 */
export const deleteSessionRecord = (params) => {
  return axios.post('/aio/aiChat/deleteSessionRecord' , params)
}