/*
 * @Author: 焦质晔
 * @Date: 2023-07-03 14:29:26
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-28 14:42:39
 */
import React from 'react';
import localforage from 'localforage';
import { useSelector, useDispatch } from '@/store';
import { createCustomNav } from '@/store/actions';
import { getUserInfo } from '@/utils/cookies';
import config from '@/config';

import type { AppState, INavMenu } from '@/store/reducers/app';

export const useCustomNavs = () => {
  const { navList, customNavList, workbench } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();

  const tempValue = React.useRef<INavMenu[]>([]);
  const customNavKey = `${getUserInfo().id}_${workbench}_nav`;

  const wbNavList = React.useMemo<INavMenu[]>(
    () => navList.filter((x) => x.wbCode === workbench),
    [navList, workbench]
  );

  React.useLayoutEffect(() => {
    config.isMainApp ? setCustomNavList() : dispatch(createCustomNav(wbNavList));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [wbNavList]);

  const setCustomNavList = async () => {
    tempValue.current = wbNavList;
    const _navList = await getLocalNavList();
    let results: INavMenu[] | undefined;
    if (_navList) {
      results = tempValue.current
        .map((x) => {
          const i = _navList.findIndex((k) => k.id === x.id);
          return {
            ...x,
            hideInNav: !!_navList[i]?.hidden,
            _sort: i < 0 ? tempValue.current.length : i,
          };
        })
        .sort((a, b) => a._sort - b._sort);
    }
    dispatch(createCustomNav(results || tempValue.current));
  };

  const getLocalNavList = async (): Promise<any[] | null> => {
    try {
      return await localforage.getItem(customNavKey);
    } catch (err) {
      // ...
    }
    return null;
  };

  const setLocalNavList = async (list) => {
    try {
      await localforage.setItem(customNavKey, list);
    } catch {
      // ...
    }
    setCustomNavList();
  };

  return {
    customNavList,
    getLocalNavList,
    setLocalNavList,
  };
};
