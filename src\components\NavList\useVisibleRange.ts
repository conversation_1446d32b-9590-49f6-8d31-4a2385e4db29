/*
 * @Author: 焦质晔
 * @Date: 2023-11-18 17:18:09
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-18 20:35:01
 */
import * as React from 'react';

import type { INavMenu } from '@/store/reducers/app';

export interface TabOffset {
  width: number;
  left: number;
}
export type TabOffsetMap = Map<React.Key, TabOffset>;

const DEFAULT_SIZE: TabOffset = { width: 0, left: 0 };

export const useVisibleRange = (
  tabOffsets: TabOffsetMap,
  containerSize: { width: number; left: number },
  tabContentNodeSize: { width: number },
  tabs: INavMenu[]
): [number, number] => {
  const unit = 'width';
  const position = 'left';
  const transformSize = Math.abs(containerSize.left);

  const basicSize = containerSize[unit];
  // const tabContentSize = tabContentNodeSize[unit];

  const mergedBasicSize = basicSize;

  return React.useMemo(() => {
    if (!tabs.length) {
      return [0, 0];
    }

    const len = tabs.length;
    let endIndex = len;
    for (let i = 0; i < len; i += 1) {
      const offset = tabOffsets.get(tabs[i].id) || DEFAULT_SIZE;
      if (offset[position] + offset[unit] > transformSize + mergedBasicSize) {
        endIndex = i - 1;
        break;
      }
    }

    let startIndex = 0;
    for (let i = len - 1; i >= 0; i -= 1) {
      const offset = tabOffsets.get(tabs[i].id) || DEFAULT_SIZE;
      if (offset[position] < transformSize) {
        startIndex = i + 1;
        break;
      }
    }

    return [startIndex, endIndex];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabOffsets, transformSize, mergedBasicSize, tabs.map((tab) => tab.id).join('#')]);
};
