// 头部卡片样式
.infoCardGrid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.infoCard {
  // 按要求使用一个虚构的背景图路径
  background: #f0f5ff url('../../../../../assets/taskCardBg.svg') no-repeat center center;
  background-size: cover;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: center;
  gap: 16px;
}

.cardLabel {
  font-size: 16px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0px;

  /* 主色/黑色-3色重 */
  color: #828290;
}

.cardValue {
  font-family: Qimiao Variable Type;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  letter-spacing: 0px;

  font-variation-settings: "opsz" auto;
  /* 主色/黑色主色 */
  color: #262629;
}

// 筛选区域
.filterBar {
  margin-bottom: 16px;
}

// 表格操作列
.actionColumn {
  // 你可以为操作按钮添加样式
}

.reannotateLink {
  padding-left: 8px;
}

.annotationTag {
  min-width: 80px;
  text-align: center;
}

// 表格匹配类型单元格
.matchTypeTag {
  padding: 2px 8px;
  border-radius: 4px;
  color: #fff;

  &.clarify {
    background-color: #1890ff;
  }

  &.no_answer {
    background-color: #faad14;
  }
}

// 表格匹配明细单元格
.matchDetail {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.moreLink {
  font-size: 12px;
}


// 分页器样式
.pagination {
  margin-top: 24px;
  display: flex;
  justify-content: flex-end;
}

// 抽屉底部
.drawerFooter {
  display: flex;
  justify-content: flex-end;
}