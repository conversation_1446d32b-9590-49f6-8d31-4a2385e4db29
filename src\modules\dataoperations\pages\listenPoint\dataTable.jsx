// src/components/DataTable/index.jsx
import React from 'react';
import { Table, Tooltip, Pagination, Button } from 'antd';
import SmartEllipsis from '../../components/SmartEllipsis';
import ContentEllipsis from './components/ContentEllipsis';

const DataTable = ({ dataSource, pagination, loading, onPageChange, onDownload, downloadBtnLoading }) => {
  const columns = [
    {
      title: '序号',
      key: 'index',
      width: 80,
      render: (text, record, index) => (pagination.current - 1) * pagination.pageSize + index + 1,
    },
    {
      title: '用户姓名',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 120,
    },
    {
      title: '用户电话',
      dataIndex: 'customerMobile',
      key: 'customerMobile',
      width: 150,
    },
    {
      title: '提问时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 220,
    },
    {
      title: '问题',
      dataIndex: 'question',
      key: 'question',
      render: (text) => (
        <ContentEllipsis width={200}>
          {text}
        </ContentEllipsis>
      ),
    },
    {
      title: '答案',
      dataIndex: 'answer',
      key: 'answer',
      render: (text) => (
        <ContentEllipsis width={340}>
          {text}
        </ContentEllipsis>
      ),
    },
    {
      title: '用户行为',
      dataIndex: 'userConduct',
      key: 'userConduct',
      width: 100,
      render: (text) => {
        //   0无 1点赞 2点踩
        return text === 0 ? '无' : text === 1 ? '点赞' : text === 2 ?  '点踩' : '-';
      }
    },
  ];

  return (
    <div style={{ background: '#fff', padding: '8px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <h3 style={{ margin: 0 }}></h3>
        <Button type="primary" onClick={onDownload} loading={downloadBtnLoading}>下载数据</Button>
      </div>
      <Table
        columns={columns}
        dataSource={dataSource}
        rowKey="id"
        pagination={false}
        loading={loading}
      />
      <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '16px' }}>
        <Pagination
          current={pagination.current}
          pageSize={pagination.pageSize}
          total={pagination.total}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `共 ${total} 条`}
          onChange={onPageChange}
          onShowSizeChange={onPageChange}
        />
      </div>
    </div>
  );
};

export default DataTable;
