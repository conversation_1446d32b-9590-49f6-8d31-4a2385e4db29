/*
 * @Author: 焦质晔
 * @Date: 2023-12-02 12:15:57
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-12-03 09:42:10
 */
/** @jsxRuntime classic */
/** @jsx jsxCustomEvent */
import jsxCustomEvent from '@micro-zoe/micro-app/polyfill/jsx-custom-event';
import React from 'react';
import { getMicroEvent, emitter as microEvent } from '@/utils/mitt';
import config from '@/config';

type IProps = {
  name: string;
  host: string;
  path: string;
  extra?: Record<string, any>;
  className?: string;
  style?: React.CSSProperties;
  ownerEvent?: boolean;
  destroyOnClose?: boolean;
  onMounted?: (ev: any) => void;
};

const MicroView: React.FC<IProps> = (props) => {
  const {
    name,
    host,
    path,
    extra,
    ownerEvent,
    destroyOnClose = false,
    className,
    style,
    onMounted,
  } = props;

  return (
    <micro-app
      name={name}
      url={host}
      baseroute="/"
      ignore=""
      destroy={destroyOnClose}
      clear-data
      data={{
        isMainEnv: window.__MAIM_APP_ENV__ ?? config.isMainApp,
        isWidget: true,
        microEvent: !ownerEvent ? getMicroEvent() : microEvent,
        pathRoute: path,
        extraProps: extra,
      }}
      class={className}
      style={{ display: 'block', height: '100%', ...style }}
      onMounted={onMounted}
    />
  );
};

export default MicroView;
