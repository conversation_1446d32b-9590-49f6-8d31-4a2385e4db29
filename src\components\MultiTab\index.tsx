/*
 * @Author: 焦质晔
 * @Date: 2021-07-07 13:44:13
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-08-22 14:34:30
 */
import React from 'react';
import screenfull from 'screenfull';
import classNames from 'classnames';
import { useLocation } from 'react-router-dom';
import { useSelector } from '@/store';
import { useLocale, useUpdateEffect } from '@/hooks';
import { useTabTool } from './useTabTool';
import { isRedirect } from '@/router';
import useContextMenu from './useContextMenu';

import type { AppState } from '@/store/reducers/app';

import { QmTabs, Dropdown } from '@jiaozhiye/qm-design-react';
import {
  TabFillIcon,
  ReloadOutlined,
  DownOutlined,
  ExpandOutlined,
  CompressOutlined,
  CloseOutlined,
  VerticalLeftIcon,
  VerticalRightIcon,
  ColumnWidthOutlined,
} from '@/icons';

import './index.less';

const { TabPane } = QmTabs;

const getParentNode = (el: HTMLElement, selector: string) => {
  let parent = el;

  while (parent) {
    if (parent.classList?.contains(selector)) {
      return parent;
    }
    parent = parent.parentNode as HTMLElement;
  }

  return null;
};

const MultiTab: React.FC = () => {
  const { tabMenus } = useSelector((state: AppState) => state.app);
  const { pathname } = useLocation();
  const { refreshHandler, changeHandler, closeHandler } = useTabTool(pathname);
  const { t } = useLocale();

  const multiTabRef = React.useRef<HTMLDivElement>(null);

  const [activeKey, setActiveKey] = React.useState<string>(pathname);
  const [isFullscreen, setFullscreen] = React.useState<boolean>(false);

  const editHandler = (path: string, action: string) => {
    if (action !== 'remove') return;
    closeHandler(path);
  };

  useUpdateEffect(() => {
    if (isRedirect(pathname)) return;
    if (activeKey !== pathname) {
      setActiveKey(pathname);
    }
  }, [pathname]);

  React.useEffect(() => {
    initial();
    return () => {
      if (screenfull.isEnabled) {
        screenfull.off('change', toggleHandler);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const initial = () => {
    if (screenfull.isEnabled) {
      screenfull.on('change', toggleHandler);
    }
  };

  const toggleHandler = () => {
    if (screenfull.isEnabled) {
      setFullscreen(screenfull.isFullscreen);
    }
  };

  // ===================================================

  const closeTagPage = (dir: string, path: string) => {
    const index = tabMenus.findIndex((x) => x.path === path);
    if (index < 0) return;
    tabMenus.forEach((x, i) => {
      if (dir === 'right' && i > index) {
        if (tabMenus[i].path === activeKey) {
          changeHandler(path);
        }
        closeHandler(x.path, true);
      }
      if (dir === 'left' && i < index) {
        if (tabMenus[i].path === activeKey) {
          changeHandler(path);
        }
        closeHandler(x.path, true);
      }
      if (dir === 'other') {
        if (i === index) {
          activeKey !== path && changeHandler(path);
          return;
        }
        closeHandler(x.path, true);
      }
    });
  };

  const renderMenus = (path?: string) => {
    const pathValue = path || activeKey;
    const disableLeft = tabMenus.findIndex((x) => x.path === pathValue) === 0;
    const disableRight = tabMenus.findIndex((x) => x.path === pathValue) === tabMenus.length - 1;
    const disableOthers = tabMenus.length <= 1;
    const menuItemCls = `ant-dropdown-menu-item`;
    return (
      <ul className={`ant-dropdown-menu multi-tab__popper`}>
        <li
          className={classNames(menuItemCls)}
          onClick={() => {
            closeMenu();
            closeHandler(pathValue);
          }}
        >
          <CloseOutlined className={`icon`} />
          {t('app.multiTab.closeCurrent')}
        </li>
        <li
          className={classNames(menuItemCls, { [`${menuItemCls}-disabled`]: disableRight })}
          onClick={() => {
            if (disableRight) return;
            closeMenu();
            closeTagPage('right', pathValue);
          }}
        >
          <span className={`anticon icon`}>
            <VerticalRightIcon />
          </span>
          {t('app.multiTab.closeRight')}
        </li>
        <li
          className={classNames(menuItemCls, { [`${menuItemCls}-disabled`]: disableLeft })}
          onClick={() => {
            if (disableLeft) return;
            closeMenu();
            closeTagPage('left', pathValue);
          }}
        >
          <span className={`anticon icon`}>
            <VerticalLeftIcon />
          </span>
          {t('app.multiTab.closeLeft')}
        </li>
        <li
          className={classNames(menuItemCls, { [`${menuItemCls}-disabled`]: disableOthers })}
          onClick={() => {
            if (disableOthers) return;
            closeMenu();
            closeTagPage('other', pathValue);
          }}
        >
          <ColumnWidthOutlined className={`icon`} />
          {t('app.multiTab.closeOthers')}
        </li>
      </ul>
    );
  };

  const extraContent = (
    <>
      <span className={`extra-btn`} onClick={() => refreshHandler()}>
        <ReloadOutlined />
      </span>
      <Dropdown dropdownRender={() => renderMenus()} placement="bottomRight" trigger={['click']}>
        <span className={`extra-btn`}>
          <DownOutlined />
        </span>
      </Dropdown>
      <span className={`extra-btn`} onClick={() => screenfull.isEnabled && screenfull.toggle()}>
        {!isFullscreen ? <ExpandOutlined /> : <CompressOutlined />}
      </span>
    </>
  );

  const { contextMenu, closeMenu } = useContextMenu({
    ref: multiTabRef,
    getTriggerContainer: (el) => el?.querySelector(`.ant-tabs .ant-tabs-nav-list`) || null,
    popup: (ev: MouseEvent) => {
      const $tab = getParentNode(ev.target as HTMLElement, 'ant-tabs-tab');
      const path = $tab?.getAttribute(`data-node-key`) || '';
      return renderMenus(path);
    },
  });

  return (
    <div ref={multiTabRef} className={classNames('app-multi-tab')}>
      <QmTabs
        type="editable-card"
        size="small"
        activeKey={activeKey}
        hideAdd
        popupClassName="multi-tab__popper"
        tabBarGutter={0}
        tabBarExtraContent={extraContent}
        onChange={changeHandler}
        onEdit={editHandler}
      >
        {tabMenus.map((item) => (
          <TabPane
            key={item.path}
            tab={
              <>
                <i className={`tab-icon`}>
                  <TabFillIcon />
                </i>
                <span>{item.title}</span>
              </>
            }
            closable
          />
        ))}
      </QmTabs>
      {contextMenu}
    </div>
  );
};

export default MultiTab;
