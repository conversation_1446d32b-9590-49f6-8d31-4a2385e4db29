/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:21:26
 */
import React from 'react';
import classNames from 'classnames';
import { useLocale } from '@/hooks';

import { Dropdown, Badge, QmDrawer, QmTabs } from '@jiaozhiye/qm-design-react';
import { BellOutlined } from '@/icons';

import MyMessage from './MyMessage';

import './index.less';

const { TabPane } = QmTabs;

const MessageCenter: React.FC = () => {
  const { t } = useLocale();

  const [visible, setVisible] = React.useState<boolean>(false);
  const [visibleMyMessage, setVisibleMyMessage] = React.useState<boolean>(false);

  const renderList = () => {
    return (
      <div className={`msg-list`}>
        <ul>
          <li>
            <div className={`list-item-meta`}>
              <div className={`avatar`}></div>
              <div className={`content`}>
                <h4 className={`text_overflow_cut`}>这种模板可以应用多个业务场景</h4>
                <div className={`description`}>1 天前</div>
              </div>
            </div>
          </li>
          <li>
            <div className={`list-item-meta`}>
              <div className={`avatar`}></div>
              <div className={`content`}>
                <h4 className={`text_overflow_cut`}>这种模板可以应用多个业务场景</h4>
                <div className={`description`}>1 天前</div>
              </div>
            </div>
          </li>
          <li>
            <div className={`list-item-meta`}>
              <div className={`avatar`}></div>
              <div className={`content`}>
                <h4 className={`text_overflow_cut`}>这种模板可以应用多个业务场景</h4>
                <div className={`description`}>1 天前</div>
              </div>
            </div>
          </li>
          <li>
            <div className={`list-item-meta`}>
              <div className={`avatar`}></div>
              <div className={`content`}>
                <h4 className={`text_overflow_cut`}>这种模板可以应用多个业务场景</h4>
                <div className={`description`}>1 天前</div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    );
  };

  const renderContent = () => {
    return (
      <div className={`container`}>
        <QmTabs defaultActiveKey="1">
          <TabPane tab={t('app.insideLetter.notice')} key="1">
            {renderList()}
          </TabPane>
          <TabPane tab={t('app.insideLetter.message')} key="2">
            {renderList()}
          </TabPane>
          <TabPane tab={t('app.insideLetter.waiting')} key="3">
            {renderList()}
          </TabPane>
        </QmTabs>
        <div className={`bottom-bar`}>
          <div>{t('app.insideLetter.clearMsg')}</div>
          <div onClick={() => openMoreHandle()}>{t('app.insideLetter.showMore')}</div>
        </div>
      </div>
    );
  };

  const openMoreHandle = () => {
    setVisible(false);
    setTimeout(() => setVisibleMyMessage(true), 300);
  };

  return (
    <div className={classNames('app-message-center')}>
      <Dropdown
        open={visible}
        dropdownRender={() => renderContent()}
        overlayClassName="ant-select-dropdown app-message__popper"
        placement="bottomRight"
        trigger={['click']}
        onOpenChange={(visible) => setVisible(visible)}
      >
        <span>
          <Badge size="small" count={6} offset={[2, 0]}>
            <BellOutlined className={`icon`} />
          </Badge>
        </span>
      </Dropdown>
      <QmDrawer
        visible={visibleMyMessage}
        title={'我的消息'}
        width={'40%'}
        loading={false}
        bodyStyle={{ paddingBottom: 52 }}
        onClose={() => setVisibleMyMessage(false)}
      >
        <MyMessage onClose={() => setVisibleMyMessage(false)} />
      </QmDrawer>
    </div>
  );
};

export default MessageCenter;
