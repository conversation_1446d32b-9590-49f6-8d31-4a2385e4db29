import React, { useState, useEffect, useCallback, useRef } from 'react'; // 新增 useRef
import { Table, Button, Input, Select, Tag, Space, Modal, Dropdown, Menu, message, Spin } from 'antd';
import { SearchOutlined, PlusOutlined, EllipsisOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import NewTaskModal from './components/newTaskModel';
import StatsModal from './components/statsModal';
import TaskDetailDrawer from './components/taskDetailDrawer';

import {
  getAnnotationTaskList,
  deleteAnnotationTask,
  exportAnnotationTask
} from '@/modules/dataoperations/api/taskAnnotation';
import styles from './index.module.less';

// 文件下载辅助函数
const downloadFile = (blob, filename) => {
  const url = window.URL.createObjectURL(new Blob([blob]));
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', filename);
  document.body.appendChild(link);
  link.click();
  link.parentNode.removeChild(link);
  window.URL.revokeObjectURL(url);
}

//可复用的 useDebounce Hook
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    // 设置一个定时器，在 delay 毫秒后更新 debouncedValue
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // useEffect 的清理函数：在下一次 effect 执行前或组件卸载时执行
    // 这确保了如果 value 在 delay 时间内再次改变，旧的定时器会被清除
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]); // 仅在 value 或 delay 改变时重新运行 effect

  return debouncedValue;
};


const TaskAnnotation = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [filters, setFilters] = useState({ taskName: '', status: 'all' });

  // 新增: 用于管理输入框实时值的 state
  const [taskNameInput, setTaskNameInput] = useState('');
  // 新增: 使用 useDebounce Hook 获取防抖后的搜索词
  const debouncedTaskName = useDebounce(taskNameInput, 500); // 500ms 延迟

  const [refetchIndex, setRefetchIndex] = useState(0);

  const [isNewTaskModalVisible, setIsNewTaskModalVisible] = useState(false);
  const [isStatsModalVisible, setIsStatsModalVisible] = useState(false);
  const [isDetailDrawerVisible, setIsDetailDrawerVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  const { current, pageSize } = pagination;

  const fetchTasks = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        pageNum: current,
        pageSize: pageSize,
        // 修改: 使用 filters 中的 taskName
        taskName: filters.taskName || null,
        status: filters.status === 'all' ? null : filters.status,
      };
      const res = await getAnnotationTaskList(params);
      if (res.data && res.data.list) {
        setTasks(res.data.list);
        setPagination(prev => ({ ...prev, total: res.data.total }));
      }
    } catch (error) {
      message.error('获取任务列表失败');
      console.error(error);
    } finally {
      setLoading(false);
    }
  }, [current, pageSize, filters, refetchIndex]);

  useEffect(() => {
    fetchTasks();
  }, [fetchTasks]);

  // 新增: 一个 useEffect 专门用于监听防抖后的搜索词变化
  // 当 debouncedTaskName 变化时，才真正更新 filters
  useEffect(() => {
    // 这里的 isInitialMount.current 是为了防止组件初次加载时就执行搜索
    // 但在当前逻辑下，初次加载时 debouncedTaskName 和 filters.taskName 都是空字符串，
    // 所以即使执行一次 handleFilterChange 也无妨，为了代码简洁可以省略 isInitialMount 的判断。
    handleFilterChange('taskName', debouncedTaskName);
  }, [debouncedTaskName]); // 仅在防抖后的搜索词变化时触发

  const handleTableChange = (newPagination) => {
    setPagination(prev => ({ ...prev, current: newPagination.current, pageSize: newPagination.pageSize }));
  };

  const handleFilterChange = (key, value) => {
    // 只有当值真正发生变化时才更新 state，避免不必要的重渲染
    if (filters[key] === value) return;

    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 修改: 状态选择器的 onChange 现在直接调用 handleFilterChange
  const handleStatusChange = (value) => {
    handleFilterChange('status', value);
  };

  // handleSearch 函数现在可以移除或保留用于回车键的显式触发
  const handleSearch = () => {
    // 触发搜索，本质上是让 debouncedTaskName 的最新值立即生效
    // 但我们的逻辑是 debouncedTaskName 变化后自动触发，
    // 所以这里只需要确保 filters 更新并重置页码即可。
    // 由于 handleFilterChange 已经实现了此逻辑，这里可以简化。
    // 如果用户回车，我们希望立即搜索，而不是再等500ms，
    // 所以直接调用 handleFilterChange 即可。
    handleFilterChange('taskName', taskNameInput);
  }

  // --- 其他函数保持不变 ---
  const handleNewTaskSuccess = () => {
    message.success('新建任务成功');
    setIsNewTaskModalVisible(false);
    if (pagination.current !== 1) {
      setPagination(prev => ({ ...prev, current: 1 }));
    } else {
      setRefetchIndex(prev => prev + 1);
    }
  };

  const handleTaskComplete = (taskId) => {
    setRefetchIndex(prev => prev + 1);
  };

  const showStatsModal = (record) => {
    setSelectedTask(record);
    setIsStatsModalVisible(true);
  };

  const showDetailDrawer = (record) => {
    setSelectedTask(record);
    setIsDetailDrawerVisible(true);
  };

  const handleDeleteTask = (id) => {
    Modal.confirm({
      title: '确认删除此任务吗？',
      icon: <ExclamationCircleOutlined />,
      content: '删除后任务将无法恢复。',
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const res = await deleteAnnotationTask(id);
          if (res.code == 200) {
            message.success('任务已删除');
          }
          setRefetchIndex(prev => prev + 1);
        } catch (error) {
          message.error('删除任务失败');
        }
      },
    });
  };

  const handleExport = async (taskId, taskName) => {
    try {
      message.loading({ content: '正在导出...', key: 'export' });
      const response = await exportAnnotationTask(taskId);
      downloadFile(response.data, `${taskName}-导出数据.xlsx`);
      message.success({ content: '导出成功!', key: 'export' });
    } catch (error) {
      message.error({ content: '导出失败!', key: 'export' });
    }
  }

  // columns 定义保持不变
  const columns = [
    // ... 省略未改变的 columns 定义
    { title: '序号', key: 'index', render: (_, __, index) => (pagination.current - 1) * pagination.pageSize + index + 1, width: 80 },
    { title: '任务名称', dataIndex: 'taskName', key: 'taskName' },
    {
      title: '状态', dataIndex: 'status', key: 'status', width: 100,
      render: (status) => (
        <Tag color={status === 'processing' ? 'processing' : 'success'}>
          {status === 'processing' ? '进行中' : '已完成'}
        </Tag>
      ),
    },
    {
      title: '时间范围', key: 'timeRange',
      render: (_, record) => `${dayjs(record.startTime).format('YYYY-MM-DD')}至${dayjs(record.endTime).format('YYYY-MM-DD')}`,
    },
    { title: '创建时间', dataIndex: 'createdAt', key: 'createdAt', width: 180, render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss') },
    { title: '创建人', dataIndex: 'creatorName', key: 'creatorName', width: 150 },
    {
      title: '操作', key: 'action', width: 220, fixed: 'right',
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => showDetailDrawer(record)} className={styles.actionLink}>进入任务详情</a>
          <a onClick={() => showStatsModal(record)} className={styles.actionLink}>数据统计</a>
          <Dropdown
            overlay={
              <Menu>
                {record.status === 'completed' && <Menu.Item key="export" onClick={() => handleExport(record.id, record.taskName)}>导出</Menu.Item>}
                <Menu.Item key="delete" onClick={() => handleDeleteTask(record.id)}>删除</Menu.Item>
              </Menu>
            }
          >
            <a onClick={(e) => e.preventDefault()}><EllipsisOutlined /></a>
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div className={styles.pageContainer}>
      <div className={styles.header}>
        <h3>任务标注</h3>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => setIsNewTaskModalVisible(true)}>新建标注任务</Button>
      </div>

      <div className={styles.filterBar}>
        <Space>
          <Input
            placeholder="搜索任务名称"
            // 修改: value 和 onChange 绑定到新的 state
            value={taskNameInput}
            onChange={(e) => setTaskNameInput(e.target.value)}
            onPressEnter={handleSearch}
            style={{ width: 240 }}
            suffix={<SearchOutlined onClick={handleSearch} />}
          />
          <Select
            value={filters.status}
            // 修改: onChange 调用 handleStatusChange
            onChange={handleStatusChange}
            style={{ width: 150 }}
          >
            <Select.Option value="all">全部状态</Select.Option>
            <Select.Option value="processing">进行中</Select.Option>
            <Select.Option value="completed">已完成</Select.Option>
          </Select>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          pagination={pagination}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Spin>

      {/* --- Modal 和 Drawer 部分保持不变 --- */}
      <NewTaskModal
        visible={isNewTaskModalVisible}
        onCancel={() => setIsNewTaskModalVisible(false)}
        onOk={handleNewTaskSuccess}
      />

      {selectedTask && <StatsModal
        visible={isStatsModalVisible}
        onCancel={() => setIsStatsModalVisible(false)}
        taskData={selectedTask}
      />}

      {selectedTask && <TaskDetailDrawer
        visible={isDetailDrawerVisible}
        onClose={() => { setIsDetailDrawerVisible(false); setSelectedTask(null) }}
        taskData={selectedTask}
        onTaskComplete={handleTaskComplete}
      />}
    </div>
  );
};

export default TaskAnnotation;