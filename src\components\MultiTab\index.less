/*
 * @Author: 焦质晔
 * @Date: 2021-07-07 13:44:34
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-06-05 13:17:49
 */
.app-multi-tab {
  height: 34px;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  margin-bottom: 6px;
  padding: 0 10px;
  background: #fff;
  .ant-tabs {
    width: 100%;
    .ant-tabs-nav {
      &::before {
        display: none;
      }
      margin: 0;
      .ant-tabs-nav-wrap {
        flex: 1;
        width: 0;
        .ant-tabs-nav-list {
          margin-top: 4px;
          .ant-tabs-tab {
            display: flex;
            padding: 6px 10px 4px;
            font-size: @textSizeSecondary + 1px;
            background: #fff;
            border: 0;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
            position: relative;
            & + .ant-tabs-tab {
              &::before {
                content: '';
                position: absolute;
                left: 0;
                top: 7px;
                width: 0;
                height: 16px;
                border-left: 1px solid @borderColorSecondary;
              }
              margin-left: -1px !important;
            }
            &-btn {
              display: flex;
              align-items: center;
              .tab-icon {
                width: 18px;
                height: 18px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-size: @textSize;
                border-radius: 50%;
                margin-right: 6px;
                background-color: rgba(0, 0, 0, 0.15);
                svg {
                  color: #fff;
                }
              }
              span {
                margin-top: -1px;
              }
            }
            .ant-tabs-tab-remove {
              padding: 2px;
              line-height: 1;
              border-radius: 2px;
              .anticon {
                transform: scale(1, 0.9);
              }
              &:hover {
                background: @backgroundColor;
              }
            }
            &-active {
              .ant-tabs-tab-btn {
                .tab-icon {
                  background-color: @primaryColor;
                }
              }
              &::before {
                display: none;
              }
              background: #f0f2f5;
            }
          }
        }
      }
      .ant-tabs-nav-operations {
        .ant-tabs-nav-more {
          padding: 4px 8px;
        }
      }
      .ant-tabs-extra-content {
        display: flex;
        height: 16px;
        .extra-btn {
          width: 34px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: @textColorTertiary;
          border-left: 1px solid @borderColorSecondary;
          cursor: pointer;
          .anticon {
            font-size: 14px !important;
          }
        }
      }
    }
    .ant-tabs-content {
      display: none;
    }
  }
}

.multi-tab__popper {
  .tab-icon {
    width: 18px;
    height: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: @textSize;
    border-radius: 50%;
    margin-right: 6px;
    background-color: rgba(0, 0, 0, 0.15);
    transform: translateY(1px);
    svg {
      color: #fff;
    }
  }
  .icon {
    margin-right: 5px;
  }
}
