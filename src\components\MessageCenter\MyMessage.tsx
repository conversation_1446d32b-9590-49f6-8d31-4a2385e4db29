/*
 * @Author: 焦质晔
 * @Date: 2022-04-21 12:14:34
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 11:13:02
 */
import React from 'react';
import { useLocale } from '@/hooks';

import { QmButton, QmSpace } from '@jiaozhiye/qm-design-react';

type IProps = {
  onClose: () => void;
};

const MyMessage: React.FC<IProps> = (props) => {
  const { t } = useLocale();

  const saveHandle = () => {
    // ....
    cancelHandle();
  };

  const cancelHandle = () => {
    props.onClose();
  };

  return (
    <>
      <div>我的消息</div>
      <QmSpace className={`fixed-footer`}>
        <QmButton onClick={() => cancelHandle()}>{t('app.button.close')}</QmButton>
        <QmButton type="primary" click={() => saveHandle()}>
          {t('app.button.confirm')}
        </QmButton>
      </QmSpace>
    </>
  );
};

export default MyMessage;
