@keyframes typing {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

.typing-indicator {
    display: flex;
    gap: 4px;
    height: 20px;
}

.typing-indicator div {
    width: 6px;
    height: 6px;
    background: #ddd;
    border-radius: 50%;
    animation: typing 1s infinite;
}

.typing-indicator div:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-indicator div:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes charPulse {
    0%, 100% { 
        opacity: 0.5;
        transform: scale(0.9);
    }
    50% { 
        opacity: 1;
        transform: scale(1.1);
    }
}

.last-char-animation {
    display: inline-block;
    animation: charPulse 0.6s infinite;
    color: #333;
}

