import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import './index.less';
import css from './index.module.less';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import sendCircle from '../../assets/md-send-circle.svg'
import sendCircleGray from '../../assets/md-send-circle-gray.svg'
import classNames from 'classnames';
import stopSvg from '../../assets/stop.svg'
import newChatSvg from '../../assets/new_chat.svg'
import copySvg from '../../assets/copy.svg'
import refreshSvg from '../../assets/refresh.svg'
import likeSvg from '../../assets/like.svg';
import dislikeSvg from '../../assets/dislike.svg';
import sidebarSvg from '../../assets/sidebar.svg'
import likeAlSvg from '../../assets/like_al.svg';
import dislikeAlSvg from '../../assets/dislike_al.svg';
import trashSvg from '../../assets/trash.svg'
import userIcon from '../../assets/userIcon.png';
import logoMov from '../../assets/logoMov.gif'
import newBlack from '../../assets/new_chat_black.png';
import zhankaiIcon1 from '../../assets/zhankai_icon1.png';
import { cleanChatLog, getChatLog, getSessionId, renewSessionId, saveNewChatLog, updateChatLog } from '../../utils/aiUtil';
import { copyToClipboard } from '../../utils/copyUtil';
import { Dropdown, message, Modal, Space, Spin } from '@jiaozhiye/qm-design-react';
import { APPID, DEFAULT_TITLE, modalList, DEFAULT_MODAL } from './constants';
import { fetchAIStreamResponse1 } from './viewModal';
import dayjs from 'dayjs';
import { deleteSessionRecord, querySessionDetail, querySessionRecord } from '../../api';
import { getUserInfo } from '@/utils/cookies';
import Record from './components/record';
import { DownOutlined } from '@/icons';
import { getChatSetting, saveChatSetting } from '../../utils/aiSettingUtil';
import { createUidKey } from '@/utils';



const ChatInterface = () => {
    const [currentSessionId, setCurrentSessionId] = useState(getSessionId());
    const [chatTitle, setChatTitle] = useState(DEFAULT_TITLE);
    const [currentModal, setCurrentModal] = useState(getChatSetting()?.modal || DEFAULT_MODAL);
    const [showSidebar, setShowSidebar] = useState(false);
    const [chatLogList, setChatLogList] = useState([]);
    const [loadingContent, setLoadingContent] = useState(false);

    useEffect(() => {
        const modal = getChatSetting()?.modal || DEFAULT_MODAL;
        setCurrentModal(modal);
    }, [])

    // 全局异常处理
    // useEffect(() => {
    //     const handleUnhandledError = (event) => {
    //         console.error("Unhandled error:", event.error);
    //         // 上报错误或显示全局提示
    //         event.preventDefault(); // 阻止默认崩溃行为
    //         cleanChatLog();
    //         setMessages([]);
    //         setChatTitle(DEFAULT_TITLE);
    //         setCurrentSessionId(renewSessionId());
    //     };

    //     window.addEventListener("error", handleUnhandledError);
    //     return () => window.removeEventListener("error", handleUnhandledError);
    // }, []);

    const toggleSidebar = () => {
        setShowSidebar(show => !show);
    }
    const changeActiveChat = async (item) => {
        try {
            setLoadingContent(true);
            const res = await querySessionDetail({ id: item.id });
            if (res.code === 200) {
                const messages = res.data?.map(item => ({
                    ...item,
                    isAI: item.type ? true : false,
                    content: item.chatText,
                    updatedAt: dayjs(item.updateTime).valueOf(),
                    createdAt: dayjs(item.createTime).valueOf(),
                }));
                chatInfo.current = {
                    title: item.title,
                    sessionId: item.sessionId,
                    messages: messages,
                    titleFlag: (item.title && item.title !== DEFAULT_TITLE) ? true : false,
                }
                renewSessionId(item.sessionId);
                setCurrentSessionId(item.sessionId);
                setChatTitle(item.title);
                setMessages(messages);
                setBtnStatus(0);
                setInputText('');
                saveNewChatLog(item.sessionId, messages, item.title, false, item.id);
            }
            // chatInfo.current = {
            //     title: item.title,
            //     sessionId: item.sessionId,
            //     messages: ,
            //     titleFlag: (item.title && item.title !== DEFAULT_TITLE) ? true : false
            // }

        } catch (e) {
            console.error(e)
        } finally {
            setLoadingContent(false);
        }


    }
    const chatInfo = useRef({
        title: '',
        sessionId: '',
        messages: [],
        titleFlag: false
    })
    const [messages, setMessages] = useState([]);
    const [inputText, setInputText] = useState('');
    const [btnStatus, setBtnStatus] = useState(0); // 0: 禁用 1: 启用 2: loading
    const [loadingList, setLoadingList] = useState(false);
    const fetchList = useCallback(async () => {
        setLoadingList(true);
        try {
            const res = await querySessionRecord({
                appId: APPID,
                userId: getUserInfo().loginName,
            });
            if (res.code === 200) {
                setChatLogList(res.data?.sort((a, b) => {
                    return dayjs(b.updatedAt).valueOf() - dayjs(a.updatedAt).valueOf();
                })?.map(item => ({
                    ...item,
                    title: item.chatTitle,
                })))
            }
        } catch (e) {
            console.error(e);
        } finally {
            setLoadingList(false);
        }

    }, []);
    useEffect(() => {
        if (showSidebar) {
            fetchList();
        }
    }, [fetchList, showSidebar]);

    useEffect(() => {
        const chatLog = getChatLog();
        if (!chatLog?.sessionId) {
            return;
        }
        setMessages(chatLog?.chatLog || []);
        setChatTitle(chatLog?.title || DEFAULT_TITLE);
        setCurrentSessionId(chatLog.sessionId);
    }, [])
    const handleModalChange = (modal) => {
        saveChatSetting((oldSetting) => {
            return {
                ...oldSetting,
                modal
            }
        })
        setCurrentModal(modal);
    }
    const modalMenuItems = useMemo(() => (modalList?.map(item => ({
        key: item.value,
        label: <div onClick={() => {
            handleModalChange(item);
        }}>
            {item.label}
        </div>
    }))), [])




    useEffect(() => {
        if (messages.length === 2 && !messages[messages.length - 1].isStreaming) {
            if (chatInfo.current.sessionId === getSessionId() && (!chatInfo.current.titleFlag || !chatInfo.current.title)) {
                const history = messages.map(item => `${item.isAI ? 'AI' : 'User'}: ${item?.content}`).concat(';\n');
                const prompt = `根据以下对话内容，给出一个简短的标题(不能超过10个汉字或者20个英文字符,不要包括其它任何无关内容,请直接输出标题文字)：\n${history}`;
                setChatTitle('');
                chatInfo.current.title = '';
                fetchAIStreamResponse1(prompt, (chunk, errorStatus) => {
                    if (errorStatus) {
                        return;
                    }
                    setChatTitle(chunk);
                    chatInfo.current.title = chunk

                }, () => {
                    chatInfo.current.titleFlag = true;
                    saveNewChatLog(getSessionId(), messages, chatInfo.current.title, true);
                    setTimeout(() => {
                        fetchList();
                    }, 1500)
                    // setChatLogList(chatList);
                }, DEFAULT_MODAL.value, createUidKey())
            }
        }
    }, [fetchList, messages])

    useEffect(() => {
        setBtnStatus(status => {
            if (status !== 2) {
                return inputText.trim() ? 1 : 0;
            } else {
                return status;
            }
        })
    }, [inputText])
    const messagesEndRef = useRef(null);

    // 自动滚动到底部
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };

    useEffect(() => {
        scrollToBottom();
    }, [messages]);


    const handleSend = useCallback(async (mess) => {
        if (!mess && (!inputText.trim() || btnStatus !== 1)) return;
        const reqContent = mess || inputText;
        // 添加用户消息
        const userMessage = {
            content: reqContent,
            isAI: false,
            id: Date.now(),
            updatedAt: dayjs().valueOf(),
            createdAt: dayjs().valueOf(),
        };

        setMessages(prev => [...prev, userMessage]);
        setInputText('');
        setBtnStatus(2);

        // 添加初始AI消息（空内容）
        const aiMessageId = Date.now() + 1;
        setMessages(prev => [...prev, {
            content: '',
            isAI: true,
            id: aiMessageId,
            isStreaming: true
        }]);
        // 获取AI流式回复
        fetchAIStreamResponse1(reqContent, (chunk) => {
            setMessages(prev => prev.map(msg =>
                msg.id === aiMessageId ? { ...msg, content: chunk, isStreaming: true } : msg
            ))
        }, () => {
            // 流式传输完成
            setMessages(prev => {
                const newMess = prev.map(msg =>
                    msg.id === aiMessageId ? { ...msg, isStreaming: false } : msg
                )
                chatInfo.current = {
                    title: '',
                    sessionId: getSessionId(),
                    messages: newMess,
                    titleFlag: false,
                    createdAt: dayjs().valueOf(),
                    updatedAt: dayjs().valueOf()
                }

                saveNewChatLog(getSessionId(), newMess, chatTitle, true);
                setTimeout(() => {
                    fetchList();
                }, 1500)
                // setChatLogList(logList);
                return newMess;
            });
            setBtnStatus(inputText.trim() ? 1 : 0);
        })

    }, [btnStatus, chatTitle, fetchList, inputText]);
    const handleStop = () => {
        if (window.currentAbortController) {
            window.currentAbortController.abort();
            setBtnStatus(0);
        }
    }
    const newChatHandle = useCallback(() => {
        if (messages.length === 0) {
            return;
        }
        setMessages([]);
        setChatTitle(DEFAULT_TITLE)
        const sessionID = renewSessionId();
        setCurrentSessionId(sessionID);
        setInputText('');
        saveNewChatLog(getSessionId(), [], DEFAULT_TITLE, false, '');
        // setChatLogList(chatList);
        // Modal.confirm({
        //     title: '确认重置当前会话吗?',
        //     content: '重置后将无法恢复',
        //     onOk: () => {

        //     },
        //     okText: '确认',
        //     cancelText: '取消'
        // })

    }, [messages?.length])

    // 解决输入法回车触发发送bug
    const [isComposing, setIsComposing] = React.useState(false);

    const handleCompositionStart = () => {
        setIsComposing(true);
    };

    const handleCompositionEnd = () => {
        setIsComposing(false);
    };

    const handleKeyDown = (e) => {
        if (e.key === 'Enter' && !e.shiftKey && !isComposing) {
            e.preventDefault();
            handleSend();
        }
    };

    // 渲染消息内容
    const renderMessageContent = (content, isAI, isStreaming) => {
        if (!content) return null;

        if (isAI) {
            // 对于AI消息，使用Markdown渲染
            const regularText = content

            return (
                <div className={css.aiMessFlex}>
                    <img src={userIcon} alt='' width={40} />
                    <div style={{ lineHeight: 1.8 }}>
                        <ReactMarkdown remarkPlugins={[remarkGfm]}>
                            {regularText}
                        </ReactMarkdown>
                    </div>
                </div>
            );
        } else {
            // 用户消息直接显示
            return content;
        }
    };

    const copyIt = async (text) => {
        await copyToClipboard(text);
        message.success('复制成功');
    }
    const deleteIt = (item) => {
        Modal.confirm({
            title: '确认删除该会话吗?',
            content: '删除后将无法恢复',
            onOk: async () => {
                const res = await deleteSessionRecord({
                    id: item.id
                });
                if (res.code === 200) {
                    message.success('删除成功');
                    if (item.sessionId === currentSessionId) {
                        newChatHandle();
                    }
                    fetchList();
                }
            },
            okText: '确认',
            cancelText: '取消',
            okButtonProps: {
                danger: true
            },
        })
    }
    const refreshIt = useCallback((index) => {
        const prompt = messages[index - 1]?.content;
        if (!prompt) {
            message.info('没有可刷新的消息');
            return;
        }
        setBtnStatus(2);
        // 获取AI流式回复
        fetchAIStreamResponse1(prompt, (chunk) => {
            setMessages(prev => prev.map((msg, xindex) =>
                xindex === index ? { ...msg, content: chunk, isStreaming: true } : msg
            ))
        }, () => {
            // 流式传输完成
            setMessages(prev => {
                const newMess = prev.map((msg, xindex) =>
                    xindex === index ? { ...msg, isStreaming: false } : msg
                )
                saveNewChatLog(getSessionId(), newMess, chatTitle, true);
                return newMess;
            });
            setBtnStatus(inputText.trim() ? 1 : 0);
        })
    }, [chatTitle, inputText, messages])
    const likeIt = (mess, likeFlag) => {
        const newMessages = messages.map((msg) => {
            if (msg.id === mess.id) {
                if (likeFlag === -1) {
                    const newObj = { ...msg };
                    delete newObj.likeFlag;
                    return newObj;
                }
                return { ...msg, likeFlag };
            }
            return msg;
        });
        setMessages(newMessages);
        saveNewChatLog(getSessionId(), newMessages, chatTitle, true);
        // setChatLogList(logList);
        if (likeFlag === 1) {
            message.success('已喜欢');
        } else if (likeFlag === 0) {
            message.success('已点踩');
        } else {
            message.success('已取消');
        }
    }
    const onRecord = (text) => {
        handleSend(text);
    }

    return (
        <div className={css.chatPage}>
            <div className={css.sideBar} style={{ width: showSidebar ? '250px' : '88px', padding: showSidebar ? '10px' : '24px', minWidth: showSidebar ? '100px' : 0 }}>
                {showSidebar && <>
                    <div className={css.sideBarHeaderFlex}>
                        <div className={css.flexIcon}>
                            <img src={userIcon} alt='' width={40} />
                            <div className={css.botName}>AI+用户伙伴</div>
                        </div>
                        <div className={css.sideBarHeader}>

                            <img
                                src={sidebarSvg}
                                width={16}
                                // className={classNames([css.sendBtn])}
                                className={css.pointIcon}
                                onClick={() => {
                                    toggleSidebar();
                                }}
                            />
                        </div>
                    </div>
                    {[0, 1].includes(btnStatus) && <div className={css.newChatBtn} onClick={newChatHandle}>
                        {
                            <img
                                src={newChatSvg}
                                width={22}
                            />
                        }
                        <div>开启新对话</div>
                    </div>}

                    {!loadingList && (chatLogList?.length > 0 ? <div className={css.titleList}>
                        {
                            chatLogList?.map((item) => {
                                return <div key={item.sessionId} className={
                                    classNames([css.titleItem, item.sessionId === currentSessionId ? css.itemActive : ''])
                                } onClick={() => {
                                    changeActiveChat(item);
                                }} >
                                    <div className={css.itemText}>{item.title}</div>
                                    {<img
                                        src={trashSvg}
                                        width={22}
                                        className={css.trashIcon}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            deleteIt(item);
                                        }}
                                    ></img>}
                                </div>
                            })
                        }
                    </div> : <div className={css.emptyListTip}>
                        暂无历史记录
                    </div>)}
                    {
                        loadingList && <div className={css.emptyListTip}>
                            <Spin />
                        </div>
                    }
                </>}
                {
                    !showSidebar && <div>
                        <div className={css.flexIcons}>
                            <img src={userIcon} alt='' width={40} />
                            <div className={css.whiteIcon} onClick={() => toggleSidebar()}>
                                <img src={zhankaiIcon1} alt='' width={22} />
                            </div>
                            {[0, 1].includes(btnStatus) && <div className={css.whiteIcon} onClick={() => newChatHandle()}>
                                <img src={newBlack} alt='' width={22} />
                            </div>}
                        </div>


                    </div>
                }
            </div>
            <div className={css.mainBox}>
                {messages?.length > 0 && <div className={css.header}>
                    <div className={css.title}>
                        {chatTitle}
                    </div>
                    {/* <div className={css.headerLeft}>

                    </div>
                    <div className={css.headerRight}>

                    </div> */}
                </div>}

                <div className={css.chatContainer} >
                    { messages?.length > 0 ? <div className={css.chatList}>
                        <div key={message.id}
                            style={{
                                display: 'flex',
                                flexDirection: 'column',
                                maxWidth: '800px',
                                gap: '15px',
                                width: '100%'
                            }}
                        >
                            {!loadingContent && messages.map((message, index) => (

                                <div
                                    key={message.id}
                                    style={{
                                        alignSelf: message.isAI ? 'flex-start' : 'flex-end',
                                        width: message.isAI ? '100%' : 'fit-content'
                                        // backgroundColor: message.isAI ? '#ffffff' : '#007bff',
                                        // color: message.isAI ? '#333' : 'white',
                                    }}
                                    className={message.isAI ? css.aiMessItem : css.humanMessItem}
                                >
                                    {renderMessageContent(message.content, message.isAI, message.isStreaming)}
                                    {message.isStreaming && message.isAI && !message.content && (
                                        <div className="typing-indicator">
                                            <div></div>
                                            <div></div>
                                            <div></div>
                                        </div>
                                    )}
                                    {
                                        message.isAI && <div className={css.messBtnArea}>
                                            <img src={copySvg} width={17} onClick={() => {
                                                copyIt(message.content);
                                            }} />
                                            <img src={refreshSvg} width={17} onClick={() => {
                                                refreshIt(index);
                                            }} />
                                            {message.likeFlag !== 1 ? <img src={likeSvg} width={17} onClick={() => {
                                                likeIt(message, 1);
                                            }} />
                                                : <img src={likeAlSvg} width={17} onClick={() => {
                                                    likeIt(message, -1);
                                                }} />
                                            }
                                            {message.likeFlag !== 0 ? <img src={dislikeSvg} width={17} onClick={() => {
                                                likeIt(message, 0);
                                            }} />
                                                : <img src={dislikeAlSvg} width={17} onClick={() => {
                                                    likeIt(message, -1);
                                                }} />}
                                        </div>}

                                </div>

                            ))}
                            {
                                loadingContent && <div className={css.emptyListTip} style={{height: '50vh'}}>
                                <Spin />
                            </div>
                            }
                        </div>
                        <div ref={messagesEndRef} />
                    </div> : <div className={css.emptyTipArea}>
                        <img src={logoMov} alt='' width={100} />
                        <div className={css.boldTip}>你好，这里是<span className={css.colorfulTip}>AI+用户伙伴</span></div>
                        <div className={css.emptyChat}>我能为你做些什么</div>
                    </div>}
                    <div className={css.inputArea} style={{ bottom: messages.length > 0 ? '0' : '18%' }}>
                        <div className={css.dropDownArea}>
                            <Dropdown
                                menu={{
                                    items: modalMenuItems,
                                }}
                                placement="bottom"
                            >
                                <div style={{ cursor: 'pointer' }} onClick={(e) => e.preventDefault()}>
                                    <Space>
                                        {currentModal?.label || ''}
                                        <DownOutlined />
                                    </Space>
                                </div>
                            </Dropdown>
                        </div>
                        <div style={{
                            display: 'flex',
                            gap: '10px',
                            alignItems: 'flex-end'
                        }} >

                            <div className={css.textArea}>
                                <textarea
                                    value={inputText}
                                    onChange={(e) => setInputText(e.target.value)}
                                    onKeyDown={handleKeyDown}
                                    onCompositionStart={handleCompositionStart}
                                    onCompositionEnd={handleCompositionEnd}
                                    placeholder="输入消息..."
                                    className={css.textAreaCom}
                                    style={{
                                        flex: 1,
                                        padding: '12px',
                                        resize: 'none',
                                        fontSize: '16px'
                                    }}
                                />

                                <div className={css.btnArea}>
                                    {
                                        btnStatus !== 2 && <Record onRecord={(text) => onRecord(text)} />
                                    }
                                    {btnStatus === 1 && <img
                                        onClick={() => handleSend()}
                                        src={sendCircle}
                                        width={54}
                                        className={css.sendBtn}
                                    />}
                                    {btnStatus === 0 && <img
                                        src={sendCircleGray}
                                        width={30}
                                        className={classNames([css.sendBtn, css.disabled])}
                                    />}
                                    {btnStatus === 2 && <img
                                        onClick={handleStop}
                                        src={stopSvg}
                                        width={30}
                                        className={classNames([css.sendBtn, css.stop])}
                                    />}

                                </div>

                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>

    )
}

export default ChatInterface;