import React, { useState, useEffect, useCallback } from 'react';
import {
    Card,
    Row,
    Col,
    Button,
    DatePicker,
    Select,
    Radio,
    Input,
    Table,
    Typography,
    Space,
    Tooltip,
    Form,
    message,
} from 'antd';
import { ExportOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import styles from './index.module.less';
import classNames from 'classnames';

// 导入机器人列表的 API
import { reportQuery, getKknowledgeList, robotlist } from '../../../api/robot';

const { RangePicker } = DatePicker;

// --- Helper Functions ---

/**
 * 根据关键字计算并返回一个 dayjs 日期范围数组
 * @param {string} key - 'yesterday', 'week', 'month'
 * @returns {[dayjs.Dayjs, dayjs.Dayjs]}
 */
const calculateDateRange = (key) => {
    const today = dayjs();
    // 统一将结束时间设为昨天末，避免包含今天的数据
    const todayEnd = today.endOf('day'); 
    switch (key) {
        case 'yesterday':
            return [today.subtract(1, 'day').startOf('day'), todayEnd.add(-1, 'day')];
        case 'month':
            // 近30天
            return [today.subtract(30, 'day').startOf('day'), todayEnd];
        case 'week':
        default:
             // 近7天
            return [today.subtract(7, 'day').startOf('day'), todayEnd];
    }
};

const OperationReport = () => {
    const [form] = Form.useForm();
    
    // --- State Management ---
    const [loading, setLoading] = useState(false); // 主查询 loading
    const [robots, setRobots] = useState([]); // 机器人列表
    const [loadingRobots, setLoadingRobots] = useState(true); // 机器人列表 loading
    
    const [reportData, setReportData] = useState({
        totalKnowledgeCount: 0,
        effectiveKnowledgeHitRate: '0.00%',
        knowledgeHitSessionRate: '0.00%',
        hotList: [],
        coldList: [],
    });

    const [detailLoading, setDetailLoading] = useState(false);
    const [detailSearchQuery, setDetailSearchQuery] = useState('');
    const [knowledgeDetailsData, setKnowledgeDetailsData] = useState({
        list: [],
        pagination: { current: 1, pageSize: 10, total: 0 },
    });

    // --- Data Fetching and Handling ---

    const getFinalDateRange = (formValues) => {
        const { timeRangeKey, customDateRange } = formValues;
        if (customDateRange && customDateRange[0] && customDateRange[1]) {
            return [dayjs(customDateRange[0]).startOf('day'), dayjs(customDateRange[1]).endOf('day')];
        }
        // 如果没有自定义日期，则根据 radio key 计算
        return calculateDateRange(timeRangeKey);
    };

    const fetchKnowledgeDetails = useCallback(async (pageInfo, query) => {
        setDetailLoading(true);
        try {
            const formValues = form.getFieldsValue(true);
            const finalDateRange = getFinalDateRange(formValues);

            const params = {
                knowledgeName: query,
                robotId: formValues.robot, // 联动机器人ID
                env: formValues.source,
                startTime: finalDateRange[0].format('YYYY-MM-DD HH:mm:ss'),
                endTime: finalDateRange[1].format('YYYY-MM-DD HH:mm:ss'),
                pageNum: pageInfo.current,
                pageSize: pageInfo.pageSize,
            };

            const res = await getKknowledgeList(params);
            if (res && res.code === 200) {
                setKnowledgeDetailsData({
                    list: res.data?.records || [],
                    pagination: { ...pageInfo, total: res.data?.total || 0 },
                });
            } else {
                message.error(res?.message || '获取知识明细失败');
            }
        } catch (error) {
            console.error('请求知识明细异常:', error);
            message.error('请求知识明细异常');
        } finally {
            setDetailLoading(false);
        }
    }, [form]);


    const handleSearch = useCallback(async () => {
        setLoading(true);
        try {
            const formValues = await form.validateFields();
            const finalDateRange = getFinalDateRange(formValues);

            // 1. 获取上方报表数据
            const reportParams = {
                robotId: formValues.robot,
                env: formValues.source,
                startTime: finalDateRange[0].format('YYYY-MM-DD HH:mm:ss'),
                endTime: finalDateRange[1].format('YYYY-MM-DD HH:mm:ss'),
            };
            const res = await reportQuery(reportParams);
            if (res && res.code === 200) {
                setReportData(res.data || { totalKnowledgeCount: 0, effectiveKnowledgeHitRate: '0%', knowledgeHitSessionRate: '0%', hotList: [], coldList: [] });
            } else {
                message.error(res?.message || '获取报表数据失败');
            }

            // 2. [联动] 获取下方知识明细数据 (重置搜索词并回到第一页)
            setDetailSearchQuery(''); 
            const initialPageInfo = { current: 1, pageSize: 10 };
            await fetchKnowledgeDetails(initialPageInfo, '');

        } catch (error) {
            console.error('查询或表单校验失败:', error);
        } finally {
            setLoading(false);
        }
    }, [form, fetchKnowledgeDetails]);


    // --- Form and Event Handlers ---

    // 表单值变化时的联动处理
    const handleFormValuesChange = (changedValues) => {
        if ('timeRangeKey' in changedValues) {
            const key = changedValues.timeRangeKey;
            if (key) {
                const newDateRange = calculateDateRange(key);
                form.setFieldsValue({ customDateRange: newDateRange });
            }
        }
        if ('customDateRange' in changedValues && changedValues.customDateRange) {
            form.setFieldsValue({ timeRangeKey: null });
        }
    };
    
    // 获取默认筛选条件
    const getDefaults = (robotList) => ({
        timeRangeKey: 'week',
        customDateRange: calculateDateRange('week'),
        robot: robotList.length > 0 ? robotList[0].value : undefined,
        source: 'prod',
    });

    // 重置
    const handleReset = () => {
        const defaultValues = getDefaults(robots);
        form.setFieldsValue(defaultValues);
        // 使用 setTimeout 确保表单值更新后再触发查询
        setTimeout(handleSearch, 0);
    };

    // 知识明细内部搜索
    const handleDetailSearch = () => {
        const { pagination } = knowledgeDetailsData;
        const pageInfo = { ...pagination, current: 1 };
        fetchKnowledgeDetails(pageInfo, detailSearchQuery);
    };

    // 知识明细表格分页变化
    const handleTableChange = (pagination) => {
        fetchKnowledgeDetails(pagination, detailSearchQuery);
    };


    // --- Lifecycle ---

    // 页面加载时，获取机器人列表并执行首次查询
    useEffect(() => {
        const initialize = async () => {
            setLoadingRobots(true);
            try {
                const res = await robotlist({ pageSize: 99999, pageNum: 1 });
                if (res.code === 200) {
                    const robotData = res.data.records.map(robot => ({
                        value: robot.id,
                        label: robot.robotName,
                    }));
                    setRobots(robotData);

                    if (robotData.length > 0) {
                        const defaultValues = getDefaults(robotData);
                        form.setFieldsValue(defaultValues);
                        await handleSearch(); // 使用默认值进行首次查询
                    } else {
                        message.warning('未发现任何机器人，无法进行查询。');
                    }
                } else {
                    message.error('获取机器人列表失败，请稍后重试');
                }
            } catch (err) {
                console.error('获取机器人列表失败:', err);
                message.error('获取机器人列表失败，请稍后重试');
            } finally {
                setLoadingRobots(false);
            }
        };
        initialize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // 仅在组件挂载时执行一次


    // --- Column Definitions ---
    const detailColumns = [
        { title: '知识ID', dataIndex: 'knowledgeId', key: 'knowledgeId', width: 150 },
        { title: '知识名称', dataIndex: 'knowledgeName', key: 'knowledgeName' },
        { title: '类目名称', dataIndex: 'categoryName', key: 'categoryName', width: 180 },
        { title: '命中次数', dataIndex: 'hitCount', key: 'hitCount', width: 120, align: 'right', sorter: (a, b) => a.hitCount - b.hitCount },
    ];
    
    const getRankBadgeClass = (index) => index < 3 ? styles.rankBadgeTop3 : styles.rankBadgeDefault;
    const hotListColumns = [
        { title: '排名', key: 'rank', width: 80, align: 'center', render: (_, __, index) => <span className={getRankBadgeClass(index)}>{index + 1}</span> },
        { title: '知识名称', dataIndex: 'knowledgeName', key: 'knowledgeName', ellipsis: true, render: (name) => <Tooltip title={name}>{name}</Tooltip> },
        { title: '命中次数', dataIndex: 'hitCount', key: 'hitCount', width: 100, align: 'right' },
    ];
    const coldListColumns = [
        { title: '知识名称', dataIndex: 'question', key: 'question', ellipsis: true, render: (name) => <Tooltip title={name}>{name}</Tooltip> },
    ];
    
    // --- Render ---
    return (
        <div className={styles.pageContainer}>
            <div className={styles.filterCard}>
                {/* 将 initialValues 移除，由 useEffect 动态设置 */}
                <Form form={form} layout="vertical" onValuesChange={handleFormValuesChange} onFinish={handleSearch}>
                    <Row gutter={24} align="bottom">
                        <Col span={5}>
                            <Form.Item label="时间范围" name="timeRangeKey">
                                <Radio.Group>
                                    <Radio.Button value="yesterday">昨天</Radio.Button>
                                    <Radio.Button value="week">近一周</Radio.Button>
                                    <Radio.Button value="month">近一月</Radio.Button>
                                </Radio.Group>
                            </Form.Item>
                        </Col>
                        <Col span={5}>
                            <Form.Item label="选择时间" name="customDateRange" colon={false}>
                                <RangePicker style={{ width: '100%' }} />
                            </Form.Item>
                        </Col>
                        <Col span={5}>
                            <Form.Item label="机器人" name="robot">
                                <Select style={{ width: '100%' }} options={robots} loading={loadingRobots} placeholder="请选择机器人" />
                            </Form.Item>
                        </Col>
                        <Col span={5}>
                            <Form.Item label="数据来源" name="source">
                                {/* 保持原样，或根据需要动态获取 */}
                                <Select style={{ width: '100%' }} options={[{ value: 'prod', label: '正式环境' }, { value: 'test', label: '测试环境' }]} />
                            </Form.Item>
                        </Col>
                        <Col span={4} style={{ textAlign: 'right', paddingBottom: '24px' }}>
                            <Space>
                                <Button onClick={handleReset}>重置</Button>
                                <Button type="primary" htmlType="submit" loading={loading}>查询</Button>
                            </Space>
                        </Col>
                    </Row>
                </Form>
            </div>

            {/* Stat Cards Section (no changes) */}
            <Row gutter={[16, 16]} style={{ marginTop: 20 }}>
                <Col span={8}><Card className={classNames([styles.statCard, styles.card1])}><div className={styles.statContent}><div className={styles.statLabel}>知识总数</div><div className={styles.statValue}>{reportData.totalKnowledgeCount}</div></div></Card></Col>
                <Col span={8}><Card className={classNames([styles.statCard, styles.card2])}><div className={styles.statContent}><div className={styles.statLabel}>有效知识命中占比</div><div className={styles.statValue}>{reportData.effectiveKnowledgeHitRate}</div></div></Card></Col>
                <Col span={8}><Card className={classNames([styles.statCard, styles.card3])}><div className={styles.statContent}><div className={styles.statLabel}>知识命中占比</div><div className={styles.statValue}>{reportData.knowledgeHitSessionRate}</div></div></Card></Col>
            </Row>

            {/* Hot/Cold Lists Section (no changes) */}
            <Row gutter={[16, 16]} style={{ marginTop: 20 }} className={styles.cards2Area}>
                <Col span={12}><Card title="热门榜-TOP10直接命中"><Table rowKey={(r, i) => `${r.knowledgeName}-${i}`} columns={hotListColumns} dataSource={reportData.hotList} pagination={false} loading={loading} size="small" /></Card></Col>
                <Col span={12}><Card title="冷门榜-无访问知识" ><Table rowKey="id" columns={coldListColumns} dataSource={reportData.coldList} pagination={{pageSize: 10}} loading={loading} size="small" /></Card></Col>
            </Row>

            {/* Knowledge Details Section */}
            <Card style={{ marginTop: 20 }}>
                <Typography.Title level={5} style={{ marginBottom: 16 }}>
                    知识明细
                </Typography.Title>
                <Input.Search
                    placeholder="按知识名称搜索"
                    value={detailSearchQuery}
                    onChange={(e) => setDetailSearchQuery(e.target.value)}
                    onSearch={handleDetailSearch}
                    style={{ width: 260, marginBottom: '16px' }}
                    enterButton
                />
                <Table
                    rowKey="knowledgeId"
                    columns={detailColumns}
                    dataSource={knowledgeDetailsData.list}
                    pagination={knowledgeDetailsData.pagination}
                    loading={detailLoading}
                    onChange={handleTableChange}
                />
            </Card>
        </div>
    );
};

export default OperationReport;