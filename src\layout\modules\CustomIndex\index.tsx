/*
 * @Author: 焦质晔
 * @Date: 2022-11-25 18:41:11
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-02-23 15:49:13
 */
/** @jsxRuntime classic */
/** @jsx jsxCustomEvent */
import jsxCustomEvent from '@micro-zoe/micro-app/polyfill/jsx-custom-event';
import React from 'react';
import { merge } from 'lodash-es';
import classNames from 'classnames';
import RGL, { WidthProvider } from 'react-grid-layout';
import { useSelector, useDispatch } from '@/store';
import { createCustomIndex } from '@/store/actions';
import { useUpdateEffect, useForceUpdate, useLocale, useEvent } from '@/hooks';
import { nextTick } from '@/utils';
import { emitter as microEvent } from '@/utils/mitt';
import { WIDGET_MAIN, WIDGET_SUB } from '@/store/types';

import type { AppState } from '@/store/reducers/app';

import { MicroView, ToCustomPage } from '@/components';

import './index.less';

const ResponsiveGridLayout = WidthProvider(RGL);
const WIDGET_ROWSPAN = 'WIDGET_ROWSPAN';
const EDITABLE_RUNTIME = false;
const DEFAULT_CONF = { cardMargin: 16, cols: 6, rowHeight: 75 };

const CustomIndex: React.FC = () => {
  const { workbench, customIndex } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const forceUpdate = useForceUpdate();
  const { t } = useLocale();

  const [derivedLayout, setDerivedLayout] = React.useState<any[]>([]);

  React.useLayoutEffect(() => {
    dispatch<any>(createCustomIndex());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  React.useEffect(() => {
    microEvent.$on(WIDGET_MAIN, widgetEventHandler);
    return () => microEvent.$off(WIDGET_MAIN, widgetEventHandler);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useUpdateEffect(() => {
    dispatch<any>(createCustomIndex(true));
  }, [workbench]);

  // useUpdateEffect(() => {
  //   // 重要 - 不延迟执行 forceUpdate，在切换工作台时首页会出现 header
  //   nextTick(() => forceUpdate());
  // }, [customIndex]);

  const widgetEventHandler = useEvent((data) => {
    const layout: any[] = customIndex.layout || [];
    if (data.code === WIDGET_ROWSPAN) {
      setDerivedLayout((prev) => {
        return layout.map((x) => {
          x = prev.find((k) => k.i === x.i) || x;
          if (x.i === data.payload.name) {
            return {
              ...x,
              h: layout.find((k) => k.i === x.i).h + data.payload.rows,
            };
          }
          return x;
        });
      });
      return;
    }
    microEvent.$emit(WIDGET_SUB, data);
  });

  const renderGrid = (option) => {
    const mergedLayout = merge([], option.layout, derivedLayout);
    const { cardMargin, cols, rowHeight } = DEFAULT_CONF;
    const RGLProps = !EDITABLE_RUNTIME ? { isDraggable: false, isResizable: false } : {};

    return (
      <div className={`container`}>
        <ResponsiveGridLayout
          layout={mergedLayout}
          autoSize={true}
          useCSSTransforms={false}
          allowOverlap={false}
          margin={[cardMargin, cardMargin]}
          cols={cols}
          rowHeight={rowHeight}
          {...RGLProps}
        >
          {mergedLayout.map((x) => {
            const { id, boxShadow = 1, noAuth = 0, url = '', ...extra } = x.children || {};
            const host = url.split(/\b(?:public|iframe)\b/)[0];
            return (
              <div key={x.i} className={classNames(`grid-item`, { shadow: boxShadow })}>
                {!noAuth ? (
                  url && host ? (
                    <micro-app
                      key={id}
                      name={`widget-home-${x.i}`}
                      baseroute="/"
                      url={host}
                      ignore=""
                      is-widget=""
                      clear-data
                      data={{
                        isMainEnv: true,
                        isWidget: true,
                        microEvent,
                        pathRoute: url.replace(host, '/'),
                        extraProps: extra,
                      }}
                      style={{ display: 'block', height: '100%' }}
                    />
                  ) : null
                ) : (
                  <div className={`no-auth`}>
                    <span>{t('app.global.noAuth')}</span>
                  </div>
                )}
              </div>
            );
          })}
        </ResponsiveGridLayout>
      </div>
    );
  };

  const renderDesign = (url: string) => {
    const host = url.match(/^https?:\/\/[^/]+/)![0];
    return <MicroView name={`home-page`} host={host} path={url.replace(host, '')} ownerEvent />;
  };

  const customIndexRender = (option) => {
    const { homeUrl, bgUrl } = option;

    const _style: React.CSSProperties = {
      backgroundImage: bgUrl && !homeUrl ? `url(${bgUrl})` : '',
    };

    return (
      <div className={classNames('app-layout', 'app-home')} style={_style}>
        <ToCustomPage title={t('app.customPage.homeEdit')} style={{ top: 46 }} />
        {!homeUrl ? renderGrid(option) : renderDesign(homeUrl)}
      </div>
    );
  };

  return customIndexRender(customIndex);
};

export default CustomIndex;
