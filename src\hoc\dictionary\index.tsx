/*
 * @Author: 焦质晔
 * @Date: 2021-07-18 19:57:39
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-01 10:56:25
 */
import React from 'react';
import { getGlobalProp, getWorkbenchId } from '@/utils';

import type { AnyObject, Dictionary, Nullable } from '@/utils/types';

const withDict = <T extends AnyObject<any>>(WrappedComponent: React.ComponentType<T>): any => {
  const C = (props, ref) => {
    const getLocalDict = (): Record<string, Dictionary[]> => {
      return getGlobalProp('__dict_data__') || {};
    };

    /**
     * @description 创建数据字典列表，支持过滤
     * @param {string} code 数据字典的 code 码
     * @param {array} excludes 需要过滤数据字典项的 code 值
     * @param {bool} showStoped 是否显示已停用的数据字典，默认 false
     * @returns {array}
     */
    const createDictList = (
      code: string,
      excludes: string[] | string = [],
      showStoped = false
    ): Dictionary[] => {
      const vals: string[] = Array.isArray(excludes) ? excludes : [excludes];
      const dict = getLocalDict();
      // 兼容工作台数据字典格式
      const dictList = dict[code] || dict[getWorkbenchId()]?.[code];
      let res: Dictionary[] = [];
      if (Array.isArray(dictList)) {
        // 过滤已停用的数据字典项
        res = !showStoped ? dictList.filter((x) => (x as any).stoped !== '1') : dictList;
        res = res.map((x) => ({ text: x.text, value: x.value }));
        res = res.filter((x) => !vals.includes(x.value.toString()));
      }
      return res;
    };

    /**
     * @description 数据字典的翻译
     * @param {string|number} val 数据的值
     * @param {string} code 数据字典的编码
     * @param {bool} showStoped 是否显示已停用的数据字典，默认 false
     * @returns {string} 翻译后的文本
     */
    const createDictText = (val: string | number, code: string, showStoped = false): string => {
      let res = '';
      if (!code) {
        return res;
      }
      const dict = getLocalDict();
      // 兼容工作台数据字典格式
      const dictList = dict[code] || dict[getWorkbenchId()]?.[code];
      if (Array.isArray(dictList)) {
        // 过滤已停用的数据字典项
        const dicts: Dictionary[] = !showStoped
          ? dictList.filter((x) => (x as any).stoped !== '1')
          : dictList;
        const target: Nullable<Dictionary> = dicts.find((x) => x.value == val) || null;
        res = target ? target.text : val.toString();
      }
      return res;
    };

    return (
      <WrappedComponent
        ref={ref}
        {...props}
        createDictList={createDictList}
        createDictText={createDictText}
      />
    );
  };

  C.displayName = `Dict(${WrappedComponent.displayName || WrappedComponent.name})`;

  return React.forwardRef<any, T>(C);
};

export default withDict;
