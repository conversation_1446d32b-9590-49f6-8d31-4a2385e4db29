
/**
 * 序列化对象为 JSON 字符串，支持循环引用
 * @param {any} obj 要序列化的对象
 * @param {number} [space] 缩进空格数
 * @returns {string} JSON 字符串
 */
export const  safeStringify = (obj: any, space?:number): string => {
    const seen = new WeakSet();
    
    return JSON.stringify(obj, (key, value) => {
      if (typeof value === 'object' && value !== null) {
        if (seen.has(value)) {
          // 遇到循环引用时返回一个特殊标记对象
          return { $ref: 'CIRCULAR_REFERENCE' };
        }
        seen.add(value);
      }
      return value;
    }, space);
  }

