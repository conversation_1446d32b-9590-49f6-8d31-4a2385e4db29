/*
 * @Author: 焦质晔
 * @Date: 2024-10-15 11:13:51
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-10-15 14:12:31
 */
.app-info-template {
  .message {
    color: #ff4d4f;
    font-weight: 700;
  }
  .detail-msg {
    color: @textColorSecondary;
    background-color: @backgroundColor;
    padding: 2px 6px;
    margin: 4px 0;
    border-radius: @borderRadius;
  }
  .fold-btn {
    display: inline-block;
    text-align: center;
    text-transform: none;
    text-decoration: none;
    background: transparent;
    color: @primaryColor;
    border: 0;
    outline: 0;
    padding: 4px 6px;
    line-height: 1;
    transition: all 0.3s ease;
    cursor: pointer;
    .icon {
      margin-left: 2px;
    }
  }
  .footer {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
  }
}

.app-fetch-notification {
  padding: 15px !important;
  .ant-notification-notice-message {
    margin-left: 35px !important;
    margin-bottom: 8px !important;
    font-weight: 700;
  }
  .ant-notification-notice-description {
    margin-left: 0 !important;
  }
}
