/*
 * @Author: 焦质晔
 * @Date: 2021-07-18 19:57:39
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-01 11:09:07
 */
import React from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector } from '@/store';
import { getMicroEvent } from '@/utils/mitt';
import { t } from '@/locale';
import {
  OPEN_VIEW,
  CLOSE_VIEW,
  REFRESH_VIEW,
  PREVENT_TAB,
  SUB_EVENT,
  WS_EVENT,
} from '@/store/types';
import config from '@/config';

import type { AppState, IPreventTab } from '@/store/reducers/app';
import type { AnyObject } from '@/utils/types';

const withTool = <T extends AnyObject<any>>(WrappedComponent: React.ComponentType<T>): any => {
  const C = (props, ref) => {
    const { size, lang } = useSelector((state: AppState) => state.app);
    const { pathname } = useLocation();

    // 打开新页签
    const openView = (fullpath: string, reload?: boolean) => {
      window.parent.postMessage({ type: OPEN_VIEW, data: fullpath, reload }, config.postOrigin);
    };

    // 关闭页签
    const closeView = (fullpath: string) => {
      window.parent.postMessage({ type: CLOSE_VIEW, data: fullpath }, config.postOrigin);
    };

    // 刷新页签
    const reloadView = () => {
      window.parent.postMessage({ type: REFRESH_VIEW, data: '' }, config.postOrigin);
    };

    // 获取用例号
    const getCaseCode = () => {
      return pathname.split('/').pop() || '';
    };

    const addControlTab = (path: string, option?: Omit<IPreventTab, 'path'>) => {
      window.parent.postMessage(
        { type: PREVENT_TAB, data: { action: 'add', path, ...option } },
        config.postOrigin
      );
    };

    const removeControlTab = (path: string) => {
      window.parent.postMessage(
        { type: PREVENT_TAB, data: { action: 'remove', path } },
        config.postOrigin
      );
    };

    const eventHandlersRef = React.useRef<Set<({ type, data }) => void>>(new Set());

    // caseCode 用例号
    const attachSubAppEvent = (caseCode: string, fn: (payload: any) => void) => {
      const handler = ({ type, data }) => {
        if (type === 'message') {
          type = data.type;
          data = data.data;
        }
        if (type === `${SUB_EVENT}__${caseCode}`) {
          fn(data);
        }
      };
      if (!eventHandlersRef.current.has(handler)) {
        eventHandlersRef.current.add(handler);
        getMicroEvent()?.$on(SUB_EVENT, handler);
        // iframe 模式的子应用
        if (window.__MAIM_APP_ENV__ && !getMicroEvent()) {
          window.addEventListener('message', handler, false);
        }
      }
    };

    const removeSubAppEvent = () => {
      eventHandlersRef.current.forEach((handler) => {
        getMicroEvent()?.$off(SUB_EVENT, handler);
        window.removeEventListener('message', handler);
      });
      eventHandlersRef.current.clear();
    };

    // fn 需要是一个具名函数
    const attachWsEvent = (code: string, fn: (payload: any) => void) => {
      getMicroEvent()?.$on(`${WS_EVENT}__${code}`, fn);
    };

    const removeWsEvent = (code: string, fn) => {
      getMicroEvent()?.$off(`${WS_EVENT}__${code}`, fn);
    };

    return (
      <WrappedComponent
        ref={ref}
        {...props}
        $t={t}
        size={size}
        lang={lang}
        openView={openView}
        closeView={closeView}
        reloadView={reloadView}
        getCaseCode={getCaseCode}
        addControlTab={addControlTab}
        removeControlTab={removeControlTab}
        removeSubAppEvent={removeSubAppEvent}
        attachSubAppEvent={attachSubAppEvent}
        attachWsEvent={attachWsEvent}
        removeWsEvent={removeWsEvent}
      />
    );
  };

  C.displayName = `Tool(${WrappedComponent.displayName || WrappedComponent.name})`;

  return React.forwardRef(C);
};

export default withTool;
