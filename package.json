{"name": "react-cli", "version": "2.0.0", "author": "<PERSON><PERSON>zhi<PERSON>", "license": "MIT", "private": true, "scripts": {"start": "cross-env NODE_ENV=development ENV_CONFIG=dev node --max-old-space-size=4096 build/dev.js", "dev": "cross-env NODE_ENV=development ENV_CONFIG=dev webpack serve --progress --config build/webpack.dev.conf.js", "_build": "cross-env NODE_ENV=production node --max-old-space-size=32768 build/prod.js", "build": "cross-env ENV_CONFIG=prod npm run _build", "build-dev": "cross-env ENV_CONFIG=dev npm run _build", "build-sit": "cross-env ENV_CONFIG=sit npm run _build", "build-uat": "cross-env ENV_CONFIG=uat npm run _build", "build-pre": "cross-env ENV_CONFIG=pre npm run _build", "build-gray": "cross-env ENV_CONFIG=gray npm run _build", "lint": "eslint ./src --ext .js,.ts,.jsx,.tsx", "lint-fix": "eslint --fix ./src --ext .js,.ts,.jsx,.tsx", "prettier": "prettier -c --write '**/*'"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,ts,jsx,tsx,vue}": ["npm run lint-fix", "git add"], "src/**/*.{less}": "prettier --write"}, "dependencies": {"@ckeditor/ckeditor5-react": "^11.0.0", "@jiaozhiye/qm-design-react": "^1.11.21", "@micro-zoe/micro-app": "^1.0.0-rc.8", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "add-dom-event-listener": "^1.1.0", "axios": "^0.27.2", "ckeditor5": "^46.0.2", "classnames": "^2.3.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "echarts": "^5.6.0", "highlight.js": "^11.10.0", "hoist-non-react-statics": "^3.3.2", "js-cookie": "^3.0.5", "jsencrypt": "^3.3.2", "juice": "^10.0.1", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "marked": "^15.0.3", "marked-highlight": "^2.2.1", "mitt": "^3.0.1", "mockjs": "^1.1.0", "nprogress": "^0.2.0", "prop-types": "^15.8.1", "qiankun": "^2.10.6", "raw-loader": "^4.0.2", "react": "^18.2.0", "react-contextmenu": "^2.14.0", "react-dom": "^18.2.0", "react-grid-layout": "^1.4.4", "react-markdown": "^10.1.0", "react-redux": "^8.1.2", "react-router-dom": "^5.3.4", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "remark-gfm": "^4.0.1", "screenfull": "^5.2.0", "wps-sdk": "^1.0.3"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.24.7", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-transform-class-properties": "^7.24.7", "@babel/plugin-transform-runtime": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@babel/preset-typescript": "^7.24.7", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^5.30.0", "@typescript-eslint/parser": "^5.30.0", "autoprefixer": "^10.4.14", "babel-loader": "^9.1.3", "case-sensitive-paths-webpack-plugin": "^2.4.0", "chalk": "^4.1.2", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^10.0.0", "copy-webpack-plugin": "^10.2.4", "core-js": "^3.38.0", "cross-env": "^10.0.0", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^5.0.1", "dotenv-webpack": "^8.0.0", "eslint": "^8.40.0", "eslint-plugin-prettier": "^4.2.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-webpack-plugin": "^3.2.0", "html-webpack-plugin": "^5.6.0", "husky": "^4.3.8", "less": "^4.2.0", "less-loader": "^11.1.4", "lint-staged": "^10.5.4", "mini-css-extract-plugin": "^2.9.0", "ora": "^5.4.1", "postcss-loader": "^7.3.0", "postcss-safe-parser": "^6.0.0", "prettier": "^2.8.8", "react-dev-utils": "^12.0.1", "react-json-view": "^1.21.3", "rimraf": "^3.0.2", "style-loader": "^3.3.4", "style-resources-loader": "^1.5.0", "terser-webpack-plugin": "^5.3.10", "thread-loader": "^3.0.4", "ts-loader": "^9.4.4", "typescript": "^4.9.5", "webpack": "^5.98.0", "webpack-cli": "^5.1.4", "webpack-custom-theme": "^1.2.0", "webpack-dev-server": "^4.15.2", "webpack-merge": "^5.10.0"}, "resolutions": {"@types/react": "^17.x"}, "engines": {"node": ">= 14.20.0"}}