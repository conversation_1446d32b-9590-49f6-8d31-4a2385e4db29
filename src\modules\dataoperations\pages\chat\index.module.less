.chatPage {
    height: 100%;
    width: 100%;
    display: flex;
    justify-content: start;
    align-items: center;
    gap: 10px;

    :glboal {
        /* Markdown样式增强 */
        pre {
            background-color: #f6f8fa;
            padding: 12px;
            border-radius: 6px;
            overflow-x: auto;
        }

        code {
            font-family: 'SFMono-Regular', <PERSON><PERSON><PERSON>, 'Liberation Mono', Menlo, monospace;
            background-color: rgba(175, 184, 193, 0.2);
            padding: 0.2em 0.4em;
            border-radius: 3px;
            font-size: 85%;
        }

        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }

        th,
        td {
            border: 1px solid #dfe2e5;
            padding: 6px 13px;
        }

        th {
            background-color: #f6f8fa;
        }

        tr:nth-child(even) {
            background-color: #f6f8fa;
        }
    }
}

.mainBox {
    display: flex;
    flex-direction: column;
    height: 100%;
    flex: 1;
}

.sideBar {
    width: 212px;
    height: 100%;
    padding: 10px;
    background: linear-gradient(180deg, #F5F5FA 0%, #FDFDFF 100%);
    transition: all 0.3s ease;
    overflow-x: hidden;
    min-width: 100px;

    .flexIcon {
        display: flex;
        align-items: center;
        justify-content: start;
        gap: 12px;
        margin-top: 5px;
    }

    .botName {
        font-family: Qimiao Variable Type;
        font-size: 13px;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0em;

        color: #3D3D3D;
    }

    .sideBarHeaderFlex {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 10px;
        width: 100%;
        height: 40px;
    }

    .sideBarHeader {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: end;
        gap: 10px;
        // margin-bottom: 10px;
    }

    .newChatBtn {
        margin: 24px 0 0 0;
        height: 38px;
        border-radius: 8px;
        opacity: 1;

        /* 自动布局 */
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 8px 20px;
        gap: 6px;

        background: #FDFDFD;

        box-shadow: 0px 2px 8px 0px rgba(58, 42, 228, 0.04), 0px 2px 20px 0px rgba(58, 42, 228, 0.02);

        font-family: Qimiao Variable Type;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0em;

        color: #3A2AE4;
        cursor: pointer;
    }

    .titleList {
        margin-top: 24px;
        width: 100%;
        height: calc(100% - 40px - 38px - 24px * 2);
        overflow-y: auto;

        &::-webkit-scrollbar {
            width: 8px;
        }

        &::-webkit-scrollbar-track {
            background: rgba(240, 240, 240, 0.5);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(180, 180, 180, 0.6);
            border-radius: 4px;
        }

        &::-webkit-scrollbar-thumb:hover {
            background: rgba(150, 150, 150, 0.8);
        }

        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .titleItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 6px;
        width: 100%;
        cursor: pointer;

        .itemText {
            height: 40px;
            min-height: max-content;
            padding: 5px 8px;

            line-height: 40px - 10px;
            white-space: nowrap;
            /* 禁止换行 */
            overflow: hidden;
            /* 隐藏超出部分 */
            text-overflow: ellipsis;
            /* 显示省略号 */
            font-family: Qimiao Variable Type;
            font-size: 14px;
            font-weight: normal;
            letter-spacing: 0em;

            color: #262629;
        }

        .trashIcon {
            display: none;
            cursor: pointer;
            margin-right: 3px;
        }


        &:hover {
            // background-color: #e5e5e5;
            background: #F0EEFD;
            // font-weight: 500;
            // border-radius: 8px;

            .trashIcon {
                display: block;
            }
        }
    }

    .itemActive {
        background: #F0EEFD;
        // font-weight: 500;
        // border-radius: 8px;
    }
}

.header {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 56px;
    padding: 16px 30px;
}

.headerLeft {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: start;
    gap: 15px;
}

.headerRight {
    height: 100%;
}


.title {
    font-family: Qimiao Variable Type;
    font-size: 15px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0em;

    color: #262629;
}

.chatContainer {
    // max-width: 800px;
    width: 100%;
    margin: 0 auto;
    height: calc(100% - 70px);
    position: relative;
}

.chatList {
    height: calc(100% - 190px);

    overflow-y: auto;
    // padding: 20px;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 15px;

    // background-color: #f0f0f0;
    // background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    &::-webkit-scrollbar {
        width: 8px;
    }

    &::-webkit-scrollbar-track {
        background: rgba(240, 240, 240, 0.5);
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
        background: rgba(180, 180, 180, 0.6);
        border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background: rgba(150, 150, 150, 0.8);
    }
}

.dropDownArea {
    display: flex;
    align-items: center;
    justify-content: end;
    padding: 13px;
}

.inputArea {
    max-width: 800px;
    height: 190px;
    position: absolute;
    bottom: 0;
    // padding: 20px;
    background-color: white;
    // box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
    width: 100%;
    left: 50%;
    transform: translateX(-50%);
}

.emptyTipArea {
    position: absolute;
    bottom: calc(18% + 190px + 32px);
    left: 0;
    right: 0;
    text-align: center;
}

.boldTip {
    margin-top: 32px;
    font-size: 32px;
    font-weight: 600;
    line-height: 32px;
    letter-spacing: 0em;

    color: #181818;
}

.colorfulTip {
    font-family: Qimiao Variable Type;
    font-size: 32px;
    font-weight: bold;
    line-height: 32px;
    letter-spacing: 0em;

    background: linear-gradient(270deg, #7E00ED 0%, #2E5DD1 66%, #139DFF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sendBtn {

    cursor: pointer;
    width: 54px;
    height: 30px;
}

.textArea {
    position: relative;
    width: 100%;

    .textAreaCom {
        border: 1px solid #E3E5E9;

        box-shadow: 0px 2px 8px 0px rgba(46, 93, 209, 0.04), 0px 2px 20px 0px rgba(46, 93, 209, 0.02);

        max-height: 150px;
        // min-height: 120px;
        height: 150px;
        width: 100%;
        border-radius: 20px;

        &:focus {

            border: 1px solid rgba(58, 42, 228, 0.4);

            box-shadow: 0px 2px 8px 0px rgba(46, 93, 209, 0.04), 0px 2px 20px 0px rgba(46, 93, 209, 0.02);
            /* 取消默认的蓝色边框 */
            outline: none;
        }
    }

    .btnArea {
        position: absolute;
        right: 20px;
        bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: end;
        gap: 8px;
    }



    .disabled {
        cursor: not-allowed;
    }

    .loading {
        cursor: not-allowed;
        // 旋转动画
        animation: loading 1s linear infinite;

        @keyframes loading {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    }
}

.messBtnArea {
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 15px;
    margin: 10px 6px 6px 6px;

    img {
        cursor: pointer;
    }
}

.humanMessItem {
    max-width: 80%;
    // padding: 15px;
    // border-radius: 15px;
    // box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    min-height: auto;
    word-break: break-word;
    overflow-wrap: break-word;

    border-radius: 12px 0px 12px 12px;
    opacity: 1;

    align-items: flex-end;
    padding: 8px 12px;
    gap: 4px;

    background: rgba(58, 42, 228, 0.08);
    color: #262629;
    font-size: 15px;
    font-weight: normal;
    line-height: 26px;
}

.aiMessFlex {
    display: flex;
    align-items: start;
    justify-content: start;
    gap: 20px;
    font-size: 15px;
}


.emptyChat {
    margin-top: 24px;
    font-size: 20px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0em;

    color: #767A8A;
}

.emptyListTip {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100% - 190px);
    font-size: 12px;
    color: #999;
}

.pointIcon {
    cursor: pointer;
}

.flexIcons {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: start;
    gap: 24px;
}

.whiteIcon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
    border-radius: 8px;
    opacity: 1;
    padding: 8px;
    gap: 6px;

    background: #FDFDFD;

    box-shadow: 0px 2px 8px 0px rgba(58, 42, 228, 0.04), 0px 2px 20px 0px rgba(58, 42, 228, 0.02);
    cursor: pointer;
}