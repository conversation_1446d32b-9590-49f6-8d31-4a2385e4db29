/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-07-02 08:46:48
 */
import React from 'react';
import classNames from 'classnames';
import { useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from '@/store';
import { createLocaleLang, createMicroMenu, createIframeMenu } from '@/store/actions';
import { emitter as microEvent } from '@/utils/mitt';
import { changeLocale } from '@/locale';
import { useApplication } from '@/hooks';
import { LOCALE_LANG } from '@/store/types';
import config from '@/config';

import type { AppState } from '@/store/reducers/app';
import type { Language } from '@/utils/types';

import { Menu, Dropdown } from '@jiaozhiye/qm-design-react';
import { TranslationOutlined } from '@/icons';

import './index.less';

const LangSetting: React.FC = () => {
  const { iframeMenus, tabMenus, lang } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const { getFrameByName, refreshView } = useApplication();

  const langChangeHandle = (lang: Language) => {
    dispatch(createLocaleLang(lang));
    changeLocale(lang);
    iframeMenus.forEach((x) => {
      const $iframe = getFrameByName(x.key) as HTMLIFrameElement;
      if (!$iframe) return;
      $iframe.contentWindow?.postMessage({ type: LOCALE_LANG, data: lang }, config.postOrigin);
    });
    microEvent.$emit(LOCALE_LANG, lang);
    // 清空页签缓存
    tabMenus.forEach((x) => {
      if (x.path === pathname) return;
      dispatch(createMicroMenu(x.path, 'remove'));
      dispatch(createIframeMenu(x.path, 'remove'));
    });
    // 延迟 - 重要
    setTimeout(() => refreshView(pathname));
  };

  const popupRender = () => {
    const items = [
      {
        key: 'zh-cn',
        disabled: lang === 'zh-cn',
        label: <>CN&nbsp;&nbsp;简体中文</>,
        onClick: () => langChangeHandle('zh-cn'),
      },
      {
        key: 'en',
        disabled: lang === 'en',
        label: <>US&nbsp;&nbsp;English</>,
        onClick: () => langChangeHandle('en'),
      },
    ];
    return <Menu items={items} />;
  };

  return (
    <div className={classNames('app-lang-setting')}>
      <Dropdown trigger={['click']} placement="bottomRight" dropdownRender={() => popupRender()}>
        <span>
          <TranslationOutlined className={`icon`} />
        </span>
      </Dropdown>
    </div>
  );
};

export default LangSetting;
