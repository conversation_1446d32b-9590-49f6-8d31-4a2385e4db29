// index.jsx
import React, { useState, useRef, useEffect } from 'react';
import { Tabs } from '@jiaozhiye/qm-design-react';
import RobotList from './robotList';
import Faq from './faq';
import OperationReport from './operationReport';
import css from './index.module.less';
import TestChatDrawer from './robotList/components/TestChatDrawer/TestChatDrawer';
import editSvg from '@/modules/dataoperations/assets/edit.svg';
import TaskAnnotation from './taskAnnotation';

const Operations = () => {
  const [tabValue, setTabValue] = useState('1');
  const [testDrawerVisible, setTestDrawerVisible] = useState(false);
  const [currentDetail, setCurrentDetail] = useState({});

  // --- 拖拽功能新增代码 开始 ---
  const dragBtnRef = useRef(null); // 用于获取按钮的 DOM 元素
  const [position, setPosition] = useState(null); // 按钮的位置 { top, left }
  const [isDragging, setIsDragging] = useState(false); // 是否正在拖拽
  const dragStartInfo = useRef({ startX: 0, startY: 0, initialTop: 0, initialLeft: 0 }); // 记录拖拽开始时的信息

  const onTestDrawerClose = () => {
    setTestDrawerVisible(false);
  };
  
  // 鼠标按下事件
  const handleMouseDown = (e) => {
    // 阻止默认事件，例如文本选择
    e.preventDefault();
    setIsDragging(true);

    const btnNode = dragBtnRef.current;
    if (btnNode) {
      const { top, left } = btnNode.getBoundingClientRect();
      dragStartInfo.current = {
        startX: e.clientX,
        startY: e.clientY,
        initialTop: top,
        initialLeft: left,
      };
      // 如果是第一次拖动，将当前位置设置为 state
      if (!position) {
        setPosition({ top, left });
      }
    }
  };

  // 使用 useEffect 来处理 window 上的 mousemove 和 mouseup 事件
  useEffect(() => {
    const handleMouseMove = (e) => {
      if (!isDragging) return;

      const { startX, startY, initialTop, initialLeft } = dragStartInfo.current;
      const dx = e.clientX - startX;
      const dy = e.clientY - startY;

      // 更新位置
      setPosition({
        top: initialTop + dy,
        left: initialLeft + dx,
      });
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };
    
    // 只有在拖拽时才添加监听
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    }
    
    // 清除函数，在组件卸载或 isDragging 变为 false 时移除监听
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]); // 依赖项是 isDragging

  // 计算按钮的最终样式
  const btnStyle = position
    ? {
        position: 'fixed', // 使用 fixed 定位，使其相对于视口
        top: `${position.top}px`,
        left: `${position.left}px`,
        right: 'auto', // 覆盖 less 中的 right
        bottom: 'auto', // 覆盖 less 中的 bottom
        cursor: isDragging ? 'grabbing' : 'grab', // 改变鼠标样式
      }
    : {
        cursor: 'grab',
      };
  // --- 拖拽功能新增代码 结束 ---

  return (
    <div>
      <Tabs
        value={tabValue}
        onChange={(val) => {
          setTabValue(val);
        }}
        items={[
          {
            key: '1',
            label: `机器人设置`,
            children: tabValue == 1 && <RobotList setCurrentDetail={setCurrentDetail} />,
          },
          {
            key: '2',
            label: `FAQ设置`,
            children: tabValue == 2 && <Faq />,
          },
          {
            key: '3',
            label: `运营报表`,
            children: tabValue == 3 && <OperationReport />,
          },
          {
            key: '4',
            label: `任务标注`,
            children: tabValue == 4 && <TaskAnnotation />,
          },
        ]}
      />
      
      {/* 应用拖拽逻辑和动态样式 */}
      {(!testDrawerVisible && ['1', '2'].includes(tabValue)) && (
        <div
          ref={dragBtnRef}
          className={css.btnBox}
          style={btnStyle} // 应用动态样式
          onMouseDown={handleMouseDown} // 绑定鼠标按下事件
          onClick={() => {
            // 防止拖拽结束后触发点击事件
            const { startX, startY } = dragStartInfo.current;
            const dist = Math.sqrt(
              Math.pow(event.clientX - startX, 2) + Math.pow(event.clientY - startY, 2)
            );
            if (dist < 5) { // 移动距离小于5像素才认为是点击
              setTestDrawerVisible(true);
            }
          }}
        >
          <img src={editSvg} alt="" />
        </div>
      )}
      
      <TestChatDrawer onClose={onTestDrawerClose} open={testDrawerVisible} detail={currentDetail} />
    </div>
  );
};

export default Operations;