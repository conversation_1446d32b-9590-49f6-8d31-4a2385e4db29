/*
 * @Author: 焦质晔
 * @Date: 2022-04-20 18:00:55
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-09-18 14:15:06
 */
.app-header {
  height: 46px;
  display: flex;
  padding: 0 20px;
  border-bottom: 1px solid @borderColorSecondary;
  background: #f9f9fb;
  transition: box-shadow 0.2s ease;
  .logo {
    width: 210px;
    display: flex;
    align-items: center;
    img {
      display: block;
      -webkit-user-drag: none;
    }
  }
  .nav {
    flex: auto;
    width: 0;
    display: flex;
    overflow: hidden;
    dl {
      margin-left: 40px;
      margin-right: 14px;
      display: flex;
      align-items: center;
    }
  }
  .setting {
    display: flex;
    align-items: center;
    margin-right: -10px;
    .ant-dropdown-trigger {
      padding: 0 6px;
    }
  }
}
