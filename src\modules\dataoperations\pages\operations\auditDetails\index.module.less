.auditdetailsList{
    width: 100%;
    display: flex;
    gap: 20px;
    .auditdetailsItem{
        flex: 1;
        background: linear-gradient(180deg, #F5F6F9 0%, rgba(241, 245, 255, 0) 100%);
        border-radius: 4px;
        padding: 20px;
        display: flex;
        align-items: center;
        &:hover{
            background: linear-gradient(180deg, #F4F1FF 0%, #FFFFFF 100%);
            box-shadow: inset 0px 0px 0px 1px #3A2AE4;
            cursor: pointer;
        }
        >img{
            margin-right: 14px;
        }
        .itemContent{
            .itemName{
                color: #434343;
                font-size: 14px;
            }
            .itemText{
                font-size: 14px;
                color: #191919;
                font-family:'hongqi';
                font-weight: 500;
                span{
                    color: #3A2AE4;
                    font-size: 24px;
                    margin-right: 8px;
                }
            }
        }
    }
    .active{
        background: linear-gradient(180deg, #F4F1FF 0%, #FFFFFF 100%);
        border: 1px solid #3A2AE4;
        &:hover{
            box-shadow: none;
            cursor: pointer;
        }
    }
}
.auditCard{
    padding: 20px;
    background: linear-gradient(270deg, #FFFFFF 0%, #F4F1FF 100%);
    border-radius: 4px;
    margin: 16px 0;
    .auditCardList{
        display: flex;
        .auditCardText{
            color: #3D3D3D;
            margin-right: 32px;
            font-size: 12px;
            i{
                font-style: normal;
                font-family: 'hongqi';
                margin: 0 8px;
                font-weight: 500;
            }
            span{
                color: #666672;
            }
        }
    }
    .cardTop{
        margin-bottom: 8px;
        span{
            padding-left:8px;
            position: relative;
            &::before{
                content: '';
                display: block;
                background: #D8D8D8;
                width: 1px;
                height: 12px;
                position: absolute;
                left: 0;
                top: 3px;
            }
        }
    }
}
.adopt{
    padding-left:12px;
    position: relative;
    &::before{
        content: '';
        display: block;
        background: #00B42A;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top: 6px;  
    }
}
.reject{
    padding-left: 12px;
    position: relative;
    &::before{
        content: '';
        display: block;
        background: #FF4D4F;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        position: absolute;
        left: 0;
        top:6px;  
    }
}
.color{
    >span{
        color: #00B42A;
        margin-right: 8px;
    }
}
.color1{
    >span{
        color: #FF4D4F;
        margin-right: 8px;
    }
}