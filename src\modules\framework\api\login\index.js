/*
 * @Author: 焦质晔
 * @Date: 2021-07-20 16:37:57
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-07-21 17:38:15
 */
import axios from '@/api/fetch';
import SERVER from '../server';

// 执行登录
export const doLogin = (params) =>
  axios.post(`${SERVER.QFC_BASE}/security/loginForUnifiedWork`, params);

// 获取登录密钥
export const getPublicKey = (params) =>
  axios.get(`${SERVER.QFC_BASE}/encrypt/getPublicKey`, { params });

// 修改个人密码
export const getUpdatePwd = (params) =>
  axios.post(`${SERVER.QFC_BASE}/user/updatePwd`, params);

// 修改默认密码
export const forceUupdatePwd = (params) =>
  axios.post(`${SERVER.QFC_BASE}/user/forceUupdatePwd`, params);

// 获取密码校验规则
export const passwordRegex = () =>
  axios.post(`${SERVER.QFC_BASE}/security/passwordRegex`);
