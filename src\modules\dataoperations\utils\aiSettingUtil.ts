import { DEFAULT_MODAL } from "../pages/chat/constants";
import { getLocalStorageItem, removeLocalStorageItem, setLocalStorageItem } from "./storageUril";

const AICHATSETTINGKEY = 'aiChatSetting';

export const getChatSetting = () => {
    const setting = getLocalStorageItem(AICHATSETTINGKEY) || {};
    if (!setting['modal']) {
        setting['modal'] = DEFAULT_MODAL;
        saveChatSetting(setting);
    }
    return setting;
}
export const saveChatSetting = (setting: object | ((currentSetting: object) => object)) => {
    if (typeof setting === 'function') {
        const currentSetting = getChatSetting();
        const newSetting = setting(currentSetting);
        return setLocalStorageItem(AICHATSETTINGKEY, newSetting);
    } else {
        return setLocalStorageItem(AICHATSETTINGKEY, setting);
    }
}
export const cleanChatSetting = () => {
    return removeLocalStorageItem(AICHATSETTINGKEY);
}