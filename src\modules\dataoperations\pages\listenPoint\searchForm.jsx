import React from 'react';
import { Form, Input, Select, DatePicker, Button, Row, Col } from 'antd';

const { RangePicker } = DatePicker;
const { Option } = Select;

const SearchForm = ({ onSearch, onReset }) => {
  const [form] = Form.useForm();

  const handleFinish = (values) => {
    const searchParams = { ...values };
    if (searchParams.questionTime && searchParams.questionTime.length === 2) {
      searchParams.startTime = searchParams.questionTime[0].format('YYYY-MM-DD HH:mm:ss');
      searchParams.endTime = searchParams.questionTime[1].format('YYYY-MM-DD HH:mm:ss');
    }
    delete searchParams.questionTime;
    onSearch(searchParams);
  };

  const handleReset = () => {
    form.resetFields();
    if (onReset) {
      onReset();
    }
  };

  return (
    <div style={{ background: '#fff', padding: '8px' }}>
      <Form
        form={form}
        onFinish={handleFinish}
        initialValues={{ userAction: 'all' }}
        layout='vertical'
      >
        {/* 表单项区域：一行4个，每个占据6 span */}
        <Row gutter={[16, 0]}>
          <Col span={4}>
            <Form.Item label="用户姓名" name="userName">
              <Input placeholder="请输入" allowClear />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="提问问题" name="question">
              <Input placeholder="请输入" allowClear />
            </Form.Item>
          </Col>
          <Col span={4}>
            <Form.Item label="用户行为" name="userConduct">
              <Select placeholder="请选择" allowClear>
                <Option value={0}>无</Option>
                <Option value={1}>点赞</Option>
                <Option value={2}>点踩</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="提问时间" name="questionTime">
              <RangePicker
                showTime
                format="YYYY-MM-DD HH:mm:ss"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={4} style={{ display: 'flex', alignItems: 'end', justifyContent: 'end' }}>
            <Form.Item>
              <Button type="primary" htmlType="submit">
                搜索
              </Button>
              <Button style={{ marginLeft: 8 }} onClick={handleReset}>
                重置
              </Button>
            </Form.Item>

          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default SearchForm;