/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-06-28 17:02:36
 */
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import classNames from 'classnames';
import { useSelector, useDispatch } from '@/store';
import { setStarMenu } from '@/store/actions';
import { useTool, useLocale, useUpdateEffect } from '@/hooks';
import { Message, sleep, isHttpLink } from '@/utils';
import config from '@/config';

import type { AppState, ISideMenu } from '@/store/reducers/app';

import { Select, QmTabs, pinyin } from '@jiaozhiye/qm-design-react';
import { StarOutlined, StarFilled } from '@/icons';

import './index.less';

type IProps = {
  visible: boolean;
  getWidth: () => number | undefined;
  onChange: () => void;
};

const { Option } = Select;
const { TabPane } = QmTabs;

const NavList: React.FC<IProps> = (props) => {
  const { visible, getWidth, onChange } = props;
  const { flattenMenus, starMenus, sideMenus } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const { openView } = useTool();
  const { t } = useLocale();

  const [searchValue, setSearchValue] = React.useState<string>();

  useUpdateEffect(() => {
    if (visible) {
      setSearchValue(flattenMenus.some((x) => x.key === pathname) ? pathname : undefined);
    }
  }, [visible]);

  const changeHandler = async (val: string) => {
    setSearchValue(val);
    !isHttpLink(val) ? openView(val) : window.open(val);
    await sleep(200);
    onChange();
  };

  const starChange = (active: boolean, { id, key, title }: ISideMenu) => {
    const results: ISideMenu[] = active
      ? starMenus.filter((x) => x.key !== key)
      : [...starMenus, { id, key, title }];
    // 最大数量判断
    if (results.length > config.maxCacheNum) {
      return Message(t('app.information.maxStar', { total: config.maxCacheNum }), 'warning');
    }
    dispatch<any>(setStarMenu(results));
  };

  const renderLinkItem = (item: ISideMenu) => {
    return !isHttpLink(item.key) ? (
      <Link to={item.key || ''} target={item.target} onClick={() => onChange()}>
        <span>{item.title}</span>
      </Link>
    ) : (
      <a href={item.key} target={item.target || '_blank'} onClick={() => onChange()}>
        <span>{item.title}</span>
      </a>
    );
  };

  const renderNode = (item: ISideMenu) => {
    const actived = starMenus.some((k) => k.key === item.key);
    return (
      <>
        {React.createElement(actived ? StarFilled : StarOutlined, {
          className: classNames('icon'),
          onClick: () => starChange(actived, item),
        })}
        {renderLinkItem(item)}
      </>
    );
  };

  return (
    <>
      <div
        className={classNames('nav-list-masker', { show: visible })}
        style={{ left: getWidth() }}
        onClick={(ev) => {
          ev.stopPropagation();
          onChange();
        }}
      />
      <div
        className={classNames('nav-list-container', { show: visible })}
        style={{ left: getWidth() }}
        onClick={(ev) => ev.stopPropagation()}
      >
        <div className={classNames('wrapper')}>
          <div className={classNames('search')}>
            <Select
              value={searchValue}
              size="middle"
              placeholder={t('app.sidebar.allNavPlaceholder')}
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option: any) => {
                const pyt: string = pinyin
                  .parse(option.children)
                  .map((v) => {
                    if (v.type === 2) {
                      return v.target.toLowerCase().slice(0, 1);
                    }
                    return v.target;
                  })
                  .join('');
                return `${option.children}|${pyt}`.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }}
              onChange={changeHandler}
            >
              {flattenMenus.map((x) => (
                <Option key={x.key} value={x.key}>
                  {x.title}
                </Option>
              ))}
            </Select>
          </div>
          <div className={classNames('main')}>
            <QmTabs tabBarGutter={0} tabPosition="right" tabBarStyle={{ width: '150px' }}>
              {sideMenus.map((item, index) => (
                <TabPane key={index} tab={item.title}>
                  <div className={classNames('column-wrap')}>
                    {item.children?.map((sub, index) => (
                      <div key={index} className={classNames('box')}>
                        <h4>{sub.title}</h4>
                        <ul>
                          {sub.children?.map((x, i) => {
                            return (
                              <li key={i}>
                                {x.children ? (
                                  <div className={classNames('four-level-box')}>
                                    <span>{x.title}</span>
                                    <ul>
                                      {x.children.map((item, index) => {
                                        return <li key={index}>{renderNode(item)}</li>;
                                      })}
                                    </ul>
                                  </div>
                                ) : (
                                  <>{renderNode(x)}</>
                                )}
                              </li>
                            );
                          })}
                        </ul>
                      </div>
                    ))}
                  </div>
                </TabPane>
              ))}
            </QmTabs>
          </div>
        </div>
      </div>
    </>
  );
};

export default NavList;
