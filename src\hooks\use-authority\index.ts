/*
 * @Author: 焦质晔
 * @Date: 2022-01-17 10:58:59
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-29 12:29:08
 */
import * as React from 'react';
import { get } from 'lodash-es';
import { getGlobalProp } from '@/utils';
import config from '@/config';

import type { IAuthUi } from '@/utils/types';

export default function useAuthority() {
  const getLocalAuth = (): Record<string, IAuthUi> => {
    return getGlobalProp('__auth_data__') || {};
  };

  const getLocalAuthBtn = (): Record<string, string[]> => {
    return getGlobalProp('__auth_btn_data__') || {};
  };

  /**
   * @description 对按钮进行权限控制
   * @param {string} caseCode 用例号
   * @param {string} code 权限按钮的 code 码
   * @returns {boolean}
   */
  const getButtonAuth = (caseCode: string, code: string): boolean => {
    const auth = getLocalAuthBtn();
    const list = auth[caseCode] || auth[config.code]?.[caseCode];
    if (Array.isArray(list)) {
      return list.findIndex((x) => x == code) > -1;
    }
    return false;
  };

  /**
   * @description 获取UI界面权限
   * @param {string} caseCode 用例号
   * @param {string} code 组件编号
   * @returns {boolean}
   */
  const createAuth = (caseCode: string, code: string) => {
    const auth = getLocalAuth();
    const data = auth[caseCode] || auth[config.code]?.[caseCode] || {};
    return get(data, code) ?? null;
  };

  return { createAuth, getButtonAuth };
}
