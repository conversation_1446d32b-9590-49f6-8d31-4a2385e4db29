/*
 * @Author: 焦质晔
 * @Date: 2023-06-09 13:39:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-09-10 20:47:14
 */
.app-action-setting {
  height: 100%;
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 2px !important;
    transition: all 0.3s ease;
    cursor: pointer;
    &:hover {
      background-color: rgba(0, 0, 0, 0.045);
    }
  }
  .icon {
    color: @textColorSecondary;
    font-size: 18px;
    cursor: pointer;
  }
}

.app-action-setting__popper {
  min-width: 140px !important;
  .action-list {
    padding: 8px 15px;
    ul {
      margin: 0;
      li.item {
        line-height: 2;
        color: @textColorSecondary;
        .handle {
          padding: 2px;
          color: @textColorTertiary;
          cursor: s-resize;
          transform: scale(0.9);
        }
      }
    }
  }
}
