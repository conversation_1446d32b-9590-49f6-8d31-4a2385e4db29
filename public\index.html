<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <title></title>
    <style type="text/css">
      body {
        margin: 0;
        padding: 0;
      }
      html,
      body,
      #app {
        height: 100%;
      }
      .spin-wrapper {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }
      .spin-wrapper .spin-dot {
        position: relative;
        display: inline-block;
        width: 32px;
        height: 32px;
      }
      .spin-wrapper .spin-dot > i {
        position: absolute;
        width: 14px;
        height: 14px;
        background-color: var(--qm-primary-color, <%= THEME_COLOR %>);
        border-radius: 100%;
        transform: scale(0.75);
        transform-origin: 50% 50%;
        opacity: 0.3;
        animation: spin-move 1s infinite linear alternate;
      }
      .spin-wrapper .spin-dot > i:nth-child(1) {
        top: 0;
        left: 0;
      }
      .spin-wrapper .spin-dot > i:nth-child(2) {
        top: 0;
        right: 0;
        animation-delay: 0.4s;
      }
      .spin-wrapper .spin-dot > i:nth-child(3) {
        right: 0;
        bottom: 0;
        animation-delay: 0.8s;
      }
      .spin-wrapper .spin-dot > i:nth-child(4) {
        bottom: 0;
        left: 0;
        animation-delay: 1.2s;
      }
      .spin-wrapper .spin-dot-spin {
        transform: rotate(0deg);
        animation: spin-rotate 1.2s infinite linear;
      }
      .spin-wrapper .spin-text {
        padding-top: 5px;
        font-size: 16px;
        color: var(--qm-primary-color, <%= THEME_COLOR %>);
      }
      @keyframes spin-move {
        to {
          opacity: 1;
        }
      }
      @keyframes spin-rotate {
        to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="app">
      <div class="spin-wrapper">
        <span class="spin-dot spin-dot-spin">
          <i></i>
          <i></i>
          <i></i>
          <i></i>
        </span>
        <span class="spin-text">Loading...</span>
      </div>
    </div>
  </body>
</html>
