/*
 * @Author: 焦质晔
 * @Date: 2024-01-28 11:28:17
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-02-20 16:22:11
 */
const __DEV__ = process.env.NODE_ENV === 'development';

let argvs = [];

if (!__DEV__) {
  argvs = process.argv.slice(2);
}

const results = {
  PUBLIC_PATH: process.env.PUBLIC_PATH,
  ENV_BRAND: process.env.ENV_BRAND,
  ENV_APP_NAME: process.env.ENV_APP_NAME,
  ENV_APP_CODE: process.env.ENV_APP_CODE,
};

argvs.forEach((x) => {
  const arr = x.split('=');
  if (arr.length < 2) return;
  if (Object.keys(results).includes(arr[0])) {
    Object.assign(results, { [arr[0]]: arr[1] });
  }
});

module.exports = results;
