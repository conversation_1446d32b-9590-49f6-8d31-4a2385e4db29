/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-10-25 09:36:56
 */
import React from 'react';
import classNames from 'classnames';
import { isEqual } from 'lodash-es';
import { Link, useLocation } from 'react-router-dom';
import { useSelector } from '@/store';
import { useTool } from '@/hooks';
import { isRedirect } from '@/router';
import { addUrlToken, isHttpLink } from '@/utils';

import type { AppState, ISideMenu } from '@/store/reducers/app';

import SvgIcon from '../SvgIcon';
import { Menu } from '@jiaozhiye/qm-design-react';

import './index.less';

const getPath = (fullpath: string) => {
  return fullpath.split('?')[0];
};

const getIcon = (icon?: string) => {
  if (!icon || !isHttpLink(icon)) {
    return null;
  }
  return <SvgIcon svgUrl={icon} />;
};

const conversionPath = (path: string) => {
  if (path.startsWith('http')) {
    return path;
  }
  return `/${path}`.replace(/\/+/g, '/');
};

const deepGetPath = (arr: ISideMenu[], attr: string, valule: string) => {
  for (let i = 0; i < arr.length; i++) {
    if (getPath(arr[i].key) === getPath(valule) && valule.startsWith(arr[i].key)) {
      return [arr[i][attr]];
    }
    if (Array.isArray(arr[i].children)) {
      const temp = deepGetPath(arr[i].children!, attr, valule);
      if (temp) {
        return [arr[i][attr], temp].flat();
      }
    }
  }
};

type IProps = {
  collapsed: boolean;
};

const SideMenu: React.FC<IProps> = (props) => {
  const { collapsed } = props;
  const { sideMenus, flattenMenus } = useSelector((state: AppState) => state.app);
  const { pathname, search } = useLocation();
  const { openView } = useTool();
  const fullpath: string = pathname + search;

  const sideMenuKeys = React.useMemo<string[]>(
    () => flattenMenus.map((x) => x.key),
    [flattenMenus]
  );

  const activeKey = React.useMemo<string>(() => {
    const v = sideMenuKeys.findIndex((x) => getPath(x) === pathname && fullpath.startsWith(x));
    if (v !== -1) {
      return sideMenuKeys[v];
    }
    return pathname;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fullpath, sideMenuKeys]);

  const [openKeys, setOpenKeys] = React.useState<string[]>([]);

  const _setOpenKeys = (keys: string[]) => {
    if (isEqual(openKeys, keys)) return;
    setOpenKeys(keys);
  };

  const _getOpenKeys = (): string[] => {
    const allOpenKeys = deepGetPath(sideMenus, 'id', fullpath) || [];
    return allOpenKeys.slice(0, -1);
  };

  React.useEffect(() => {
    if (isRedirect(pathname)) return;
    _setOpenKeys(_getOpenKeys());
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, sideMenus, collapsed]);

  const createMenuTree = (list: ISideMenu[]) => {
    return list
      .filter((x) => !x.hideInMenu)
      .map((item) => {
        const { title, icon } = item;
        const path: string = conversionPath(item.key || '');
        if (Array.isArray(item.children)) {
          return {
            key: item.id,
            popupClassName: 'ant-submenu-popup-dark',
            icon: getIcon(icon),
            label: title,
            children: createMenuTree(item.children),
          };
        }
        return {
          key: path,
          label: (
            <Link to={path} onClick={(ev) => ev.preventDefault()}>
              {title}
            </Link>
          ),
          onClick: () => {
            if (isHttpLink(item.caseHref!) && item.target === '_blank') {
              window.open(addUrlToken(item.caseHref!), '_blank');
            } else {
              const p = path.split('?');
              openView(p.length > 1 && p[0] === pathname ? `/redirect${path}` : path);
            }
          },
        };
      });
  };

  return (
    <div className={classNames('app-side-menu')}>
      <Menu
        key={sideMenus.length}
        mode="inline"
        theme="dark"
        inlineIndent={20}
        items={createMenuTree(sideMenus)}
        selectedKeys={[activeKey]}
        openKeys={!collapsed ? openKeys : undefined}
        onOpenChange={(keys) => {
          setOpenKeys(keys);
        }}
      />
    </div>
  );
};

export default SideMenu;
