/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-01-28 13:47:12
 */
import React from 'react';
import classNames from 'classnames';
import { useSelector, useDispatch } from '@/store';
import { createTheme } from '@/store/actions';
import { emitter as microEvent } from '@/utils/mitt';
import { useApplication, useLocale } from '@/hooks';
import { THEME_COLOR } from '@/store/types';
import config from '@/config';

import type { AppState } from '@/store/reducers/app';

import { CheckOutlined } from '@/icons';

import './index.less';

const Tag = ({ color, check, ...rest }) => (
  <div
    {...rest}
    style={{
      backgroundColor: color,
    }}
  >
    {check ? <CheckOutlined /> : ''}
  </div>
);

const ThemeColor: React.FC = () => {
  const { iframeMenus, themeColor } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { t } = useLocale();
  const { getFrameByName } = useApplication();

  const colorList = [
    { text: '', value: '#F5222D' },
    { text: '', value: '#FA541C' },
    { text: '', value: '#FAAD14' },
    { text: '', value: '#11A983' },
    { text: '', value: '#13C2C2' },
    { text: '', value: '#52C41A' },
    { text: '', value: '#1890FF' },
    { text: '', value: '#0D5FE9' },
    { text: '', value: '#2A5CA5' },
  ];

  const themeColorChangeHandle = (color: string) => {
    dispatch<any>(createTheme(color));
    iframeMenus.forEach((x) => {
      const $iframe = getFrameByName(x.key) as HTMLIFrameElement;
      if (!$iframe) return;
      $iframe.contentWindow?.postMessage({ type: THEME_COLOR, data: color }, config.postOrigin);
    });
    microEvent.$emit(THEME_COLOR, color);
  };

  return (
    <div className={classNames('themeColor')}>
      <div className={classNames('title')}>{t('app.theme.color')}</div>
      <div>
        {colorList.map(({ value }) => (
          <Tag
            key={value.slice(1)}
            className={classNames('color-block')}
            color={value}
            check={themeColor === value}
            onClick={() => themeColorChangeHandle(value)}
          />
        ))}
      </div>
    </div>
  );
};

export default ThemeColor;
