/*
 * @Author: 焦质晔
 * @Date: 2021-07-19 15:55:42
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-03-07 18:55:45
 */
@textColor: rgba(255, 255, 255, 0.65);
@lineColor: #6b6b6b;

.app-all-nav {
  &.selected {
    .ant-menu-item {
      color: #fff;
    }
  }
  .nav-list-masker {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.65);
    z-index: -2;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.7, 0.3, 0.1, 1);
    transform-origin: 0 50% 0;
    opacity: 0;
    &.show {
      visibility: visible;
      opacity: 0.2;
    }
  }
  .nav-list-container {
    position: absolute;
    top: 0;
    width: 650px;
    height: 100%;
    background: #313538;
    z-index: -1;
    box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
    transform: translate3d(-100%, 0, 0);
    transition: all 0.4s cubic-bezier(0.7, 0.3, 0.1, 1);
    transform-origin: 0 50% 0;
    opacity: 0;
    visibility: hidden;
    &.show {
      transform: translate3d(0, 0, 0);
      opacity: 1;
      visibility: visible;
    }
  }
  .wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    &::after {
      content: '';
      position: absolute;
      width: 150px;
      height: 100%;
      right: 0;
      top: 0;
      background: #272b2e;
      z-index: -1;
    }
    .search {
      margin: 50px 210px 20px 60px;
      .ant-select {
        .ant-select-selector {
          border: 1px solid @lineColor;
          background-color: transparent;
          .ant-select-selection-search > input {
            color: @textColor;
          }
          .ant-select-selection-item {
            color: @textColor;
          }
        }
        .ant-select-arrow {
          color: @textColor;
        }
        &:hover,
        &.ant-select-focused {
          .ant-select-selector {
            outline: none;
            box-shadow: none;
            border-color: @lineColor;
          }
        }
      }
    }
    .main {
      flex: 1;
      padding-bottom: 15px;
      overflow-y: auto;
      .ant-tabs {
        height: 100%;
        .ant-tabs-tab {
          padding-left: 15px;
          padding-right: 10px;
          .ant-tabs-tab-btn {
            color: @textColor;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        .ant-tabs-tab-active > .ant-tabs-tab-btn {
          color: #fff;
        }
        .ant-tabs-nav-operations {
          display: none;
        }
        .ant-tabs-content-holder {
          border-right-color: @lineColor;
          overflow-y: auto;
          &::-webkit-scrollbar {
            display: none;
          }
        }
      }
      .column-wrap {
        column-count: 2;
        column-gap: 0;
        .box {
          padding-left: 20px;
          padding-bottom: 10px;
          -webkit-column-break-inside: avoid;
          page-break-inside: avoid;
          break-inside: avoid;
          h4 {
            color: @textColor;
            line-height: 34px;
            border-bottom: 1px solid @lineColor;
          }
          ul {
            padding: 0;
            margin-top: 8px;
            li {
              line-height: 26px;
              display: flex;
              align-items: center;
              .icon {
                margin-right: 4px;
                font-size: @textSize;
                color: @textColor;
                cursor: pointer;
              }
              a {
                font-size: @textSizeSecondary;
                color: @textColor;
                transition: all 0.3s ease;
                &:hover {
                  color: #fff;
                }
              }
            }
          }
        }
      }
    }
  }
}
