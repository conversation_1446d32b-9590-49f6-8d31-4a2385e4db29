/*
 * @Author: 焦质晔
 * @Date: 2024-08-22 12:53:50
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-08-22 13:42:59
 */
import React from 'react';
import { useSelector } from '@/store';
import { useApplication, useLocale, useTool } from '@/hooks';
import { Confirm, sleep } from '@/utils';
import { emitter as microEvent } from '@/utils/mitt';
import { TAB_MENU } from '@/store/types';
import config from '@/config';

import type { AppState } from '@/store/reducers/app';

export const useTabTool = (pathname: string) => {
  const { tabMenus, preventTabs } = useSelector((state: AppState) => state.app);
  const { getFrameByName, refreshView } = useApplication();
  const { t } = useLocale();
  const { openView, closeView } = useTool();

  const preventTabHandle = async (targetPath: string, callback) => {
    const preventTab = preventTabs.find((x) => x.path === targetPath);
    try {
      if (preventTab) {
        const { title = '' } = tabMenus.find((x) => x.path === targetPath) || {};
        const eventName = `${TAB_MENU}__${targetPath.split('/').pop()}`;
        // 事件派发
        microEvent.$emit(eventName, '');
        getFrameByName(targetPath)?.contentWindow!.postMessage(
          { type: eventName, data: '' },
          config.postOrigin
        );
        if (preventTab.showConfirm) {
          await Confirm(preventTab.message || `${title}${t('app.global.leaveText')}`);
        } else if (preventTab.delay) {
          await sleep(preventTab.delay);
        }
      }
      callback?.(targetPath);
    } catch (err) {
      // ...
    }
  };

  const refreshHandler = async () => {
    await preventTabHandle(pathname, () => {
      refreshView(pathname);
    });
  };

  const changeHandler = (path: string) => {
    const { search = '' } = tabMenus.find((x) => x.path === path) || {};
    openView(path + search);
  };

  const doRemove = (path: string) => {
    if (path === pathname) {
      const v = tabMenus.findIndex((x) => x.path === path);
      const from = tabMenus.find((x) => x.path === tabMenus[v].from) ? tabMenus[v].from : '';
      const nextActiveKey = from || tabMenus[v - 1]?.path || tabMenus[v + 1]?.path || '/home';
      changeHandler(nextActiveKey);
    }
    closeView(path);
  };

  const closeHandler = async (path: string, noJump?: boolean) => {
    await preventTabHandle(path, () => {
      !noJump ? doRemove(path) : closeView(path);
    });
  };

  return { refreshHandler, changeHandler, closeHandler };
};
