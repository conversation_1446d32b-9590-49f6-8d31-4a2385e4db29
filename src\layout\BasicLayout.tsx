/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 13:31:45
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-08-20 16:45:59
 */
/** @jsxRuntime classic */
/** @jsx jsxCustomEvent */
import jsxCustomEvent from '@micro-zoe/micro-app/polyfill/jsx-custom-event';
import React from 'react';
import classNames from 'classnames';
import { useLocation } from 'react-router-dom';
import { matchRoutes } from '@/router';
import { useSelector, useDispatch } from '@/store';
import { useApplication, useWindowSize, useUpdateEffect } from '@/hooks';
import { createStarMenu, createCommonMenu, createDeviceType } from '@/store/actions';
import { emitter as microEvent } from '@/utils/mitt';
import { getSystem, isSameOrigin } from '@/utils';
import { deepFindNav } from '@/store/reducers/app';
import config from '@/config';

import type { AppState } from '@/store/reducers/app';
import type { IRoute } from '@/utils/types';

import { Layout } from '@jiaozhiye/qm-design-react';
import { Header, MultiTab, SideMenu } from '@/components';
import RouteView from './RouteView';
import CustomIndex from './modules/CustomIndex';
import SplitView from './modules/SplitView';
// import Watermark from './modules/Watermark';
// import ContentMasker from './modules/ContentMasker';
// import Logo from './modules/Logo';
// import AllNav from './modules/AllNav';
// import StarNav from './modules/StarNav';
// import SideMenu from './modules/SideMenu';
// import MultiTab from './modules/MultiTab';
// import Actions from './modules/Actions';

// import { MenuUnfoldOutlined, MenuFoldOutlined } from '@/icons';

import './index.less';

const { Content } = Layout;

const MOBILE_WIDTH = 960;

type IProps = {
  route: {
    routes: IRoute[];
  };
};

const BasicLayout: React.FC<IProps> = (props) => {
  const { navList, sideMenus, microMenus, iframeMenus, lang, size } = useSelector(
    (state: AppState) => state.app
  );
  const dispatch = useDispatch();
  const { pathname } = useLocation();
  const { fetchNavMenus, sendLocalStore, sendSiteInfo } = useApplication();
  const { winWidth } = useWindowSize();

  const isMobile = React.useMemo<boolean>(() => winWidth < MOBILE_WIDTH, [winWidth]);
  const [collapsed, setCollapsed] = React.useState<boolean>(isMobile);
  const [locked, setLocked] = React.useState<boolean>(false);
  const [left, setLeft] = React.useState<number>(config.sideWidth[0]);
  const [hideSide, setHideSide] = React.useState<boolean>(false);

  React.useLayoutEffect(() => {
    fetchNavMenus();
    if (!window.__MAIM_APP_ENV__) {
      config.showStarNav && dispatch<any>(createStarMenu());
      config.showCommonNav && dispatch<any>(createCommonMenu());
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useUpdateEffect(() => {
    fetchNavMenus(true);
  }, [lang]);

  useUpdateEffect(() => {
    const curNav = deepFindNav(navList, (x) => x.system === getSystem());
    const _collapse = isMobile || curNav?.sideState === 'C';
    const _hideSide = curNav?.sideState === 'H';
    if (collapsed !== _collapse) {
      setCollapsed(_collapse);
    }
    if (hideSide !== _hideSide) {
      setHideSide(_hideSide);
    }
    if (!curNav && !sideMenus.length) {
      setHideSide(true);
    }
  }, [sideMenus]);

  useUpdateEffect(() => {
    toggle(isMobile); // 重置 collapsed
    dispatch(createDeviceType(isMobile ? 'mobile' : 'desktop'));
  }, [isMobile]);

  const toggle = (value?: boolean) => {
    const newVal = value ?? !collapsed;
    setCollapsed(newVal);
  };

  const createIframeView = (route: IRoute) => {
    return iframeMenus.map((x) => {
      if (!x.keep && route.path !== x.key) {
        return null;
      }
      return (
        <div
          key={x.key}
          className={classNames('app-iframe-container')}
          style={{ display: route.path === x.key ? 'block' : 'none' }}
        >
          <iframe
            name={x.key}
            src={x.value}
            width="100%"
            height="100%"
            frameBorder="0"
            allowFullScreen
            onLoad={(ev) => {
              const $iframe = ev.target as HTMLIFrameElement;
              const navItem = deepFindNav(
                navList,
                (k) => k.system === x.key.match(/^\/+([^/]+)/)?.[1]
              );
              sendSiteInfo(x.key, { title: navItem?.title ?? '', leader: navItem?.leader ?? '' });
              if (!isSameOrigin($iframe)) {
                sendLocalStore(x.key);
              }
              $iframe.focus();
            }}
          />
        </div>
      );
    });
  };

  const createMicroView = (route: IRoute) => {
    return microMenus.map((x) => {
      const { key, value, keep } = x;
      if (!keep && route.path !== key) {
        return null;
      }
      const navItem = deepFindNav(navList, (k) => k.system === key.match(/^\/+([^/]+)/)?.[1]);
      return (
        <micro-app
          key={key}
          name={key.replace(/\/+/g, '-').slice(1)}
          baseroute={key}
          url={value}
          ignore=""
          clear-data
          data={{
            microEvent,
            isMainEnv: config.isMainApp,
            isWidget: true,
            pathRoute: route.microRule,
            extraProps: {
              systemId: navItem?.sysId,
              appCode: navItem?.code,
            },
            siteInfo: {
              title: navItem?.title,
              leader: navItem?.leader,
            },
          }}
          style={{ display: route.path === key ? 'block' : 'none', height: '100%' }}
        />
      );
    });
  };

  const renderHome = (routes: IRoute[]) => {
    if (config.isMainApp) {
      return <CustomIndex />;
    }
    return (
      <div className={classNames('app-layout', 'app-home')}>
        <RouteView routes={routes} />
      </div>
    );
  };

  const renderChp = (route: IRoute) => {
    return (
      <micro-app
        name="wb-chp"
        baseroute={route.microRule}
        url={route.microHost}
        ignore=""
        clear-data
        data={{
          microEvent,
          isMainEnv: config.isMainApp,
        }}
        style={{ display: 'block', width: '100%' }}
      />
    );
  };

  // ==================== Render ====================

  const { routes } = props.route;
  const { route } = matchRoutes(routes, pathname).pop()!;
  const isHomePage = pathname === '/home';

  const cls = {
    [`app-layout`]: !0,
    [`is-locked`]: locked,
    [`app-layout__sm`]: size === 'small',
    [`app-layout__lg`]: size === 'large',
  };

  return (
    <>
      {!window.__MAIM_APP_ENV__ && <Header />}
      {isHomePage ? (
        renderHome(routes)
      ) : (
        <Layout className={classNames(cls)}>
          {!pathname.startsWith('/chp/') ? (
            <>
              <SplitView
                offset={left}
                min={config.sideWidth[0]}
                max={500}
                disabled={collapsed}
                hidden={hideSide}
                style={{ height: 'inherit' }}
                onDrag={(val) => setLeft(val)}
                onDragChange={(active) => setLocked(active)}
              />
              <SideMenu
                width={left}
                collapsed={collapsed}
                hidden={hideSide}
                onChange={(value) => toggle(value)}
              />
              <Layout className={classNames(`app-main`)}>
                <MultiTab />
                <Content className={`app-content`}>
                  <RouteView routes={routes} />
                  {createIframeView(route)}
                  {createMicroView(route)}
                </Content>
              </Layout>
            </>
          ) : (
            <>
              <RouteView routes={routes} />
              {renderChp(route)}
            </>
          )}
        </Layout>
      )}
    </>
  );
};

export default BasicLayout;
