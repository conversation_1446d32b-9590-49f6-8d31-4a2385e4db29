import React from 'react';
import { Mo<PERSON>, Button } from 'antd';
import styles from './index.module.less';

const FAQDetailModal = ({ visible, onClose, faqData }) => {
  if (!faqData) {
    return null;
  }

  return (
    <Modal
      title="FAQ详细信息"
      open={visible}
      onCancel={onClose}
      footer={[
        <Button key="know" type="primary" onClick={onClose}>
          知道了
        </Button>,
      ]}
      width={800}
      destroyOnClose
    >
      <div className={styles.detailContainer}>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>类目:</span>
          <span className={styles.detailValue}>{faqData.category}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>FAQ标题:</span>
          <span className={styles.detailValue}>{faqData.title}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>生效时间:</span>
          <span className={styles.detailValue}>{faqData.effectiveDate}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>相似问题:</span>
          <div className={styles.detailValue}>
            <ol className={styles.similarList}>
              {faqData.similarQuestions?.map((q, index) => (
                <li key={index}>{q}</li>
              ))}
            </ol>
          </div>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>答案:</span>
          <div className={styles.detailValue}>
            <div className={styles.answerHeader}>
              <span className={styles.answerPerspective}>视角: {faqData.answer?.perspective}</span>
              <span>答复类型: {faqData.answerType == '00' ? '纯文本' : '富文本'} </span>
            </div>
            <div className={styles.answerBody}>
              {faqData.answerType == '00' ? faqData.answer?.text : <div dangerouslySetInnerHTML={{ __html: faqData.answer?.text }}></div>}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};

export default FAQDetailModal;