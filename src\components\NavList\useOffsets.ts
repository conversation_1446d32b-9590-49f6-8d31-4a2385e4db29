/*
 * @Author: 焦质晔
 * @Date: 2023-11-18 17:27:50
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-18 20:33:29
 */
import * as React from 'react';

import type { INavMenu } from '@/store/reducers/app';
import type { TabOffset, TabOffsetMap } from './useVisibleRange';

export type TabSizeMap = Map<React.Key, TabOffset>;

const DEFAULT_SIZE: TabOffset = { width: 0, left: 0 };

export const useOffsets = (tabs: INavMenu[], tabSizes: TabSizeMap, holderScrollWidth: number) => {
  return React.useMemo(() => {
    const map: TabOffsetMap = new Map();

    const lastOffset = tabSizes.get(tabs[0]?.id) || DEFAULT_SIZE;
    const rightOffset = lastOffset.left + lastOffset.width;

    for (let i = 0; i < tabs.length; i += 1) {
      const { id } = tabs[i];
      let data = tabSizes.get(id);

      // Reuse last one when not exist yet
      if (!data) {
        data = tabSizes.get(tabs[i - 1]?.id) || DEFAULT_SIZE;
      }

      const entity = (map.get(id) || { ...data }) as TabOffset;

      // Right
      // entity.right = rightOffset - entity.left - entity.width;

      // Update entity
      map.set(id, entity);
    }

    return map;

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tabs.map((tab) => tab.id).join('#'), tabSizes, holderScrollWidth]);
};
