/*
 * @Author: 焦质晔
 * @Date: 2022-09-18 12:58:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-19 10:52:27
 */
import React from 'react';
import classNames from 'classnames';
import raf from 'rc-util/lib/raf';
import { useResizeObserve } from '@/hooks';
import { useNavCore } from './useNavCore';
import { useRefs } from './useRefs';
import { useVisibleRange } from './useVisibleRange';
import { useOffsets } from './useOffsets';
import { useTouchMove } from './useTouchMove';

import type { TabSizeMap } from './useOffsets';

import NavNode from './NavNode';
import PopperNode from './PopperNode';
import OperationNode from './OperationNode';

import './index.less';

const NavList: React.FC = () => {
  const tabsWrapperRef = React.useRef<HTMLDivElement>(null);
  const tabListRef = React.useRef<HTMLUListElement>(null);
  const {
    navMenus,
    activeKey,
    navItem,
    visible,
    openLinkHandle,
    closePopper,
    clearTimer2,
    visibleChange,
    mouseEnterHandle,
    mouseLeaveHandle,
  } = useNavCore(tabsWrapperRef);

  const [getBtnRef, removeBtnRef] = useRefs<HTMLDivElement>();
  const [transformLeft, setTransformLeft] = React.useState<number>(0);
  const [wrapperScrollWidth, setWrapperScrollWidth] = React.useState<number>(0);
  const [wrapperWidth, setWrapperWidth] = React.useState<number>(0);

  const { width: wrapWidth } = useResizeObserve(tabsWrapperRef);
  const [tabSizes, setTabSizes] = React.useState<TabSizeMap>(new Map());
  const tabOffsets = useOffsets(navMenus, tabSizes, wrapperScrollWidth);

  // ========================== Util =========================
  const transformMin = Math.min(0, wrapperWidth - wrapperScrollWidth);
  const transformMax = 0;

  const alignInRange = (value: number): number => {
    if (value < transformMin) {
      return transformMin;
    }
    if (value > transformMax) {
      return transformMax;
    }
    return value;
  };

  const prevent = (ev) => ev.preventDefault;

  // ========================= Mobile ========================
  const touchMovingRef = React.useRef<number>();
  const [lockAnimation, setLockAnimation] = React.useState<number>();

  const doLockAnimation = () => {
    setLockAnimation(Date.now());
  };

  const clearTouchMoving = () => {
    window.clearTimeout(touchMovingRef.current);
  };

  useTouchMove(tabsWrapperRef, (offsetX, offsetY) => {
    const doMove = (setState: React.Dispatch<React.SetStateAction<number>>, offset: number) => {
      setState((value) => {
        const newValue = alignInRange(value + offset);
        return newValue;
      });
    };

    // Skip scroll if place is enough
    if (wrapperWidth >= wrapperScrollWidth) {
      return false;
    }

    doMove(setTransformLeft, offsetX);

    clearTouchMoving();
    doLockAnimation();

    return true;
  });

  React.useEffect(() => {
    clearTouchMoving();
    if (lockAnimation) {
      touchMovingRef.current = window.setTimeout(() => setLockAnimation(0), 100);
    }
    return clearTouchMoving;
  }, [lockAnimation]);

  // ========================= Scroll ========================
  const scrollToTab = (key: string) => {
    const tabOffset = tabOffsets.get(key) || { width: 0, left: 0 };
    let newTransform = transformLeft;

    if (tabOffset.left < -transformLeft) {
      newTransform = -tabOffset.left;
    } else if (tabOffset.left + tabOffset.width > -transformLeft + wrapperWidth) {
      newTransform = -(tabOffset.left + tabOffset.width - wrapperWidth);
    }

    setTransformLeft(alignInRange(newTransform));
  };

  // ========================== Tab ==========================
  const [visibleStart, visibleEnd] = useVisibleRange(
    tabOffsets,
    { width: wrapperWidth, left: transformLeft },
    { width: wrapperScrollWidth },
    navMenus
  );

  const onListHolderResize = () => {
    // Update wrapper records
    const offsetWidth = tabsWrapperRef.current?.offsetWidth || 0;
    setWrapperWidth(offsetWidth);

    const newWrapperScrollWidth = tabListRef.current?.offsetWidth || 0;
    setWrapperScrollWidth(newWrapperScrollWidth);

    // Update buttons records
    setTabSizes(() => {
      const newSizes: TabSizeMap = new Map();
      navMenus.forEach(({ id }) => {
        const btnNode = getBtnRef(id).current;
        if (btnNode) {
          newSizes.set(id, {
            width: btnNode.offsetWidth,
            left: btnNode.offsetLeft,
          });
        }
      });
      return newSizes;
    });
  };

  // ======================== Dropdown =======================
  const operationsHiddenClassName = `operations-hidden`;
  const startHiddenTabs = navMenus.slice(0, visibleStart);
  const endHiddenTabs = navMenus.slice(visibleEnd + 1);
  const hiddenTabs = [...startHiddenTabs, ...endHiddenTabs];

  // =================== Link  ===================
  const [inkStyle, setInkStyle] = React.useState<React.CSSProperties>();

  const activeTabOffset = tabOffsets.get(activeKey);

  // Delay set ink style to avoid remove tab blink
  const inkBarRafRef = React.useRef<number>();
  const cleanInkBarRaf = () => {
    raf.cancel(inkBarRafRef.current!);
  };

  React.useEffect(() => {
    const newInkStyle: React.CSSProperties = {};

    if (activeTabOffset) {
      newInkStyle.left = activeTabOffset.left + 20;
      newInkStyle.width = activeTabOffset.width - 40;
    }

    cleanInkBarRaf();
    inkBarRafRef.current = raf(() => {
      setInkStyle(newInkStyle);
    });

    return cleanInkBarRaf;
  }, [activeTabOffset]);

  // ========================= Effect ========================
  React.useEffect(() => {
    scrollToTab(activeKey);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey, activeTabOffset, tabOffsets]);

  React.useEffect(() => {
    onListHolderResize();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [wrapWidth, activeKey, navMenus.map((tab) => tab.id).join('#')]);

  React.useEffect(() => {
    const $wrapper = tabsWrapperRef.current!;
    // 解决了两指滑动页面切换问题
    // No need to clean up since element removed
    $wrapper.addEventListener('wheel', prevent, false);
    return () => {
      $wrapper.removeEventListener('wheel', prevent);
      clearTimer2();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // ========================= Render ========================
  const renderNodes = () => {
    return navMenus.map((x) => (
      <NavNode
        key={x.id}
        ref={getBtnRef(x.id)}
        item={x}
        activeKey={activeKey}
        onNavClick={openLinkHandle}
        onMouseEnter={mouseEnterHandle}
        onMouseLeave={mouseLeaveHandle}
      />
    ));
  };

  const hasDropdown = !!hiddenTabs.length;
  const pingLeft = transformLeft < 0;
  const pingRight = -transformLeft + wrapperWidth < wrapperScrollWidth;

  const cls = {
    [`wrap`]: true,
    [`wrap-ping-left`]: pingLeft,
    [`wrap-ping-right`]: pingRight,
  };

  return (
    <div className={`nav-list`} onKeyDown={() => doLockAnimation()}>
      <div ref={tabsWrapperRef} className={classNames(cls)}>
        <PopperNode
          visible={visible}
          item={navItem}
          close={closePopper}
          clearTimer2={clearTimer2}
          onVisibleChange={visibleChange}
        >
          <ul
            ref={tabListRef}
            style={{
              transform: `translateX(${transformLeft}px)`,
              transition: lockAnimation ? 'none' : undefined,
            }}
          >
            {renderNodes()}
            <span className={`bar-dot`} style={inkStyle} />
          </ul>
        </PopperNode>
      </div>
      <OperationNode
        items={hiddenTabs}
        className={!hasDropdown && operationsHiddenClassName}
        scrollToTab={scrollToTab}
        onItemClick={openLinkHandle}
      />
    </div>
  );
};

export default NavList;
