/*
 * @Author: 焦质晔
 * @Date: 2022-11-16 10:45:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-01-17 12:59:47
 */
import React from 'react';

const MenuCutIcon: React.FC = () => {
  return (
    <svg
      viewBox="0 0 1024 1024"
      focusable="false"
      data-icon="filter"
      width="1em"
      height="1em"
      fill="currentColor"
      aria-hidden="true"
    >
      <path d="M96 213.333333A32 32 0 0 1 128 181.333333h554.666667a32 32 0 0 1 0 64H128A32 32 0 0 1 96 213.333333zM896 480H128a32 32 0 0 0 0 64h768a32 32 0 0 0 0-64z m-384 298.666667H128a32 32 0 0 0 0 64h384a32 32 0 0 0 0-64z"></path>
    </svg>
  );
};

export default MenuCutIcon;
