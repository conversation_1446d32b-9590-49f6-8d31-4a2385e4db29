/*
 * @Author: 焦质晔
 * @Date: 2023-11-18 16:58:01
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-19 00:54:41
 */
import React from 'react';
import classNames from 'classnames';
import { useSelector } from '@/store';
import { addUrlToken, isHttpLink } from '@/utils';

import type { AppState, INavMenu, ISideMenu } from '@/store/reducers/app';

import { DownOutlined, ThreeDownIcon } from '@/icons';

type IProps = {
  item: INavMenu;
  activeKey: string;
  onNavClick: (item: INavMenu, path: string, ev) => void;
  onMouseEnter: (item: INavMenu, path: string, ev) => void;
  onMouseLeave: (ev) => void;
};

const deepFindFirstNav = (menus: ISideMenu[]) => {
  let item = menus[0];
  if (Array.isArray(item?.children)) {
    item = deepFindFirstNav(item.children);
  }
  return item;
};

export const getVirtualPath = (menuList: ISideMenu[], navItem: INavMenu) => {
  const menus = menuList.find((k) => k.system === navItem.system)?.children || [];
  const { caseHref, target, key } = deepFindFirstNav(menus) || {};
  // 虚拟 app & 菜单的首个用例是新 Tab 页打开
  return caseHref && target === '_blank' ? addUrlToken(caseHref) : key;
};

const NavNode = React.forwardRef<any, IProps>((props, ref) => {
  const { item, activeKey, onNavClick, onMouseEnter, onMouseLeave } = props;
  const { menuList } = useSelector((state: AppState) => state.app);

  let path = item.key;
  if (item.virtual) {
    path = getVirtualPath(menuList, item);
  }

  // 是否显示子导航
  const isSubMunu = item.type === 0; // app 分组
  const isNavTag = !!item.tagName;
  const isWorkbench = item.type === 2; // 工作台

  return (
    <li
      ref={ref}
      onMouseEnter={(ev) => onMouseEnter(item, path, ev)}
      onMouseLeave={(ev) => onMouseLeave(ev)}
    >
      {!isHttpLink(path) ? (
        <>
          <a
            href={path}
            title={item.title}
            className={classNames('link', { actived: activeKey === item.id })}
            onClick={(ev) => onNavClick(item, path, ev)}
          >
            {item.shortName || item.title}
            {isWorkbench && (
              <span className={`anticon anticon-down icon`}>
                <ThreeDownIcon />
              </span>
            )}
            {isSubMunu && <DownOutlined className={`icon`} />}
          </a>
          {isNavTag && (
            <i className={`nav-tag`}>
              <span>{item.tagName}</span>
            </i>
          )}
        </>
      ) : (
        <a target={`_blank`} href={path} title={item.title}>
          {item.shortName || item.title}
        </a>
      )}
    </li>
  );
});

NavNode.displayName = 'NavNode';

export default NavNode;
