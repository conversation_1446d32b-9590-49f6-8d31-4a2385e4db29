/*
 * @Author: 焦质晔
 * @Date: 2022-12-10 21:52:16
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-02-03 00:13:54
 */
.app-dashboard {
  position: relative;
  margin: -10px !important;
  height: calc(100% + 10px);
  overflow-y: auto;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50% 0;
  .container {
    position: relative;
    .grid-item {
      micro-app-body {
        overflow-y: visible;
        overflow-x: hidden;
      }
      &.shadow {
        background-color: #fff;
        border-radius: 6px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.16);
      }
      .no-auth {
        width: 100%;
        height: 100%;
        background: url(../CustomIndex/assets/no-auth.svg) 50% 50% no-repeat;
        background-size: contain;
        position: relative;
        & > span {
          position: absolute;
          bottom: @moduleMargin;
          left: @moduleMargin;
          right: @moduleMargin;
          text-align: center;
          color: @textColorTertiary;
        }
      }
    }
  }
}
