.card {
  flex: 1 1 48%; /* 这将确保每个项目占据容器宽度的50%，从而实现每行两个项目 */
  box-sizing: border-box;
  height: 196px;
  // margin: 0 11px 10px 11px;
  border-radius: 10px;
  &:last-child:nth-child(odd) {
    flex: 0 0 calc(50% - 10px); // 保持只占一半宽度
  }
  :global {
    .ant-card-body {
      height: 100%;
      background: url('../../../../../assets/robot.png') no-repeat right bottom;
    }
  }
  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .titleTxt {
      font-size: 20px;
      font-weight: 500;
      color: #05103d;
    }
  }
  .text {
    margin-top: 6px;
    color: #767a8a;
  }
  .info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-top: 12px;
    .head {
      color: #262629;
      font-size: 14px;
    }
  }
  .imgBox {
    display: flex;
    align-items: center;
    color: #767a8a;
    .txt {
      margin-right: 8px;
    }
    .img {
      width: 14px;
      height: 14px;
      margin-right: 4px;
    }
  }
  .textWrap {
    text-wrap: nowrap;
  }
}
