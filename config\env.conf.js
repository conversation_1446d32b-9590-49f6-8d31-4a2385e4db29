/*
 * @Author: 焦质晔
 * @Date: 2020-12-03 09:16:26
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-04 20:44:34
 */
const argvs = require('../build/argv');
const appConf = require('./app.conf');

const __ENV__ = process.env.ENV_CONFIG || 'prod';

const createEnvConf = (env) => {
  // 可处理不同品牌的环境变量
  const { brand } = appConf;
  return {
    theme: '#0D5FE9',
    ...(argvs.PUBLIC_PATH ? { publicPath: `${argvs.PUBLIC_PATH}/`.replace(/\/+$/, '/') } : null),
  };
};

const config = {
  dev: {
    publicPath: '/',
  },
  sit: {
    publicPath: '/',
  },
  uat: {
    publicPath: '/',
  },
  pre: {
    publicPath: '/',
  },
  prod: {
    publicPath: '/',
  },
  gray: {
    publicPath: '/gray/',
  },
};

module.exports = Object.assign({}, config[__ENV__], createEnvConf(__ENV__));
