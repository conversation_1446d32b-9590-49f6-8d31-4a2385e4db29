import React, { useEffect, useState } from 'react';
import css from './index.module.less';
import { marked } from 'marked';
import axios from '@/api/fetch';
import { Col, Row, Space } from '@jiaozhiye/qm-design-react';
import { markedHighlight } from 'marked-highlight';
import hljs from 'highlight.js';
import 'highlight.js/styles/base16/darcula.css';

export const getUrlParam = () => {
  let param = {};
  const str = window.location.search.slice(1);
  const obj = {};
  str.split('&').forEach((it) => {
    const i = it.split('=');
    obj[i[0]] = i[1];
  });
  param = obj;
  return param;
};

const highLightButton = {
  background: '#3A2AE4',
  color: '#ffffff',
};

const flag = {
  PMD: false,
  AI: false,
};

const defaultButton = {
  background: '#ffffff',
  color: '#262626',
};

const listItemActive = { background: '#F4F4FF', color: '#3A2AE4', cursor: 'pointer' };

const listItemDefault = { background: '#FFFFFF', color: '#3D3D3D', cursor: 'pointer' };

export const Index = (props) => {
  const [curType, setCurType] = useState('PMD');

  const [level1, setLevel1] = useState<any>(0);
  const [level2, setLevel2] = useState<any>(0);

  const [list, setList] = useState({
    PMD: { level1: [], level2: [], level3: [] },
    EsLint: { level1: [], level2: [], level3: [] },
  });
  const [detailList, setDetailList] = useState([]);
  const [content, setContent] = useState('');

  const level1Change = (val) => {
    setLevel1(val);
    setDetailList(list[curType]?.[`level${val}`] || []);
  };

  const level2Change = (val, item) => {
    if (!item) {
      setLevel2(0);
      setContent('');
    } else {
      setLevel2(val);
      setContent(item.problemDesc + ' 【' + item.filePath + ' : ' + item.problemLine + '】');
    }
  };
  useEffect(() => {
    level2Change(1, detailList[0])
  }, [level1]);


  useEffect(() => {
    if (!flag[curType]) {
      flag[curType] = true;
      axios
        .post('/crs/code/selectScanResult', {
          ...getUrlParam(),
          scanType: curType,
        })
        .then((res) => {
          if (['PMD', 'EsLint'].includes(curType)) {
            const obj = {
              level1: [],
              level2: [],
              level3: [],
            };
            res.data.forEach((item) => {
              obj[`level${item.problemLevel}`].push(item);
            });
            setList({...list, [curType]: obj});
          } else {
            marked.use(
              markedHighlight({
                langPrefix: 'hljs language-',
                highlight(code, lang) {
                  const language = hljs.getLanguage(lang) ? lang : 'shell';
                  return hljs.highlight(code, { language }).value;
                },
              })
            );
            document!.getElementById('content')!.innerHTML = marked.parse(
              res.data[0].problemDesc
            ) as string;
          }
        });
    }
  }, [curType]);

  const curTypeChange = (type) => {
    setCurType(type);
    setDetailList([]);
    setLevel1(0);
  };

  return (
    <div className={css.page}>
      <div className={css.pageTitle}>AI检测报告</div>
      <Space size={16} style={{ marginBottom: '24px' }}>
        {
          ['PMD', 'AI', 'EsLint'].map(type => (
            <div
              key={type}
              className={css.tabButton}
              style={curType === type ? highLightButton : defaultButton}
              onClick={() => curTypeChange(type)}
            >
              {type}
            </div>
          ))
        }
      </Space>

      <div className={css.pageContent} style={{ display: ['EsLint', 'PMD'].includes(curType) ? 'block' : 'none' }}>
        <Row style={{ height: '100%' }}>
          <Col
            span={6}
            style={{
              height: '100%',
              padding: '16px',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <div className={css.contentTitle}>问题大类</div>
            <div className={css.list}>
              <div
                className={css.listItem}
                onClick={() => level1Change(1)}
                style={level1 === 1 ? listItemActive : listItemDefault}
              >
                <p>
                  <span className={css.tag} style={{background: '#D12325'}}>一级</span>
                  <span>BLOCKER <span style={{color: '#D12325'}}>{list[curType]?.level1?.length}</span> </span>
                </p>
                <span>{'>'}</span>
              </div>
              <div
                className={css.listItem}
                onClick={() => level1Change(2)}
                style={level1 === 2 ? listItemActive : listItemDefault}
              >
                <p>
                  <span className={css.tag} style={{background: '#FF4D4F'}}>二级</span>
                  <span>CRITICAL <span style={{color: '#FF4D4F'}}>{list[curType]?.level2?.length}</span> </span>
                </p>
                <span>{'>'}</span>
              </div>
              <div
                className={css.listItem}
                onClick={() => level1Change(3)}
                style={level1 === 3 ? listItemActive : listItemDefault}
              >
                <p>
                  <span className={css.tag} style={{background: '#FF7F4D'}}>三级</span>
                  <span>MAJOR <span style={{color: '#FF7F4D'}}>{list[curType]?.level3?.length}</span> </span>
                </p>
                <span>{'>'}</span>
              </div>
            </div>
          </Col>
          <Col
            span={6}
            style={{
              height: '100%',
              padding: '16px',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'auto',
            }}
          >
            <div className={css.contentTitle}>问题子类</div>
            <div className={css.list}>
              {detailList.map((item: any, index) => {
                return (
                  <div
                    key={item.id}
                    className={css.listItem}
                    onClick={() => level2Change(index + 1, item)}
                    style={level2 === index + 1 ? listItemActive : listItemDefault}
                  >
                    <span>{item.ruleId}</span>
                    <span>{'>'}</span>
                  </div>
                );
              })}
            </div>
          </Col>
          <Col
            span={12}
            style={{ height: '100%', padding: '16px', display: 'flex', flexDirection: 'column' }}
          >
            <div className={css.contentTitle}>问题详解</div>
            <div className={css.detailContent}>
              {content && (
                <div className={css.detailItem}>
                  <div className={css.index}>1</div>
                  <div className={css.text}>{content}</div>
                </div>
              )}
            </div>
          </Col>
        </Row>
      </div>

      <div
        id="content"
        className={css.pageContent}
        style={{ display: curType === 'AI' ? 'block' : 'none', padding: '24px' }}
      ></div>
    </div>
  );
};
export default Index;
