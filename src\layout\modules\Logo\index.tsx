/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:23:48
 */
import React from 'react';
import { Link } from 'react-router-dom';
import { useLocale } from '@/hooks';

import logo from './assets/logo.png';

import './index.less';

type IProps = {
  collapsed: boolean;
};

const Logo: React.FC<IProps> = (props) => {
  const { collapsed } = props;
  const { t } = useLocale();

  return (
    <div className={`app-logo`}>
      <Link to={`/home`} className={`link`}>
        <img className={`logo`} src={logo} alt="" />
        {!collapsed && <span className={`title`}>{t('app.global.title')}</span>}
      </Link>
    </div>
  );
};

export default Logo;
