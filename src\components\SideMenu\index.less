/*
 * @Author: 焦质晔
 * @Date: 2022-04-21 08:48:26
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-01-20 09:58:09
 */
.app-sider {
  width: 200px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  margin-right: 16px;
  background: #fff;
  position: relative;
  &.opened {
    margin-left: 0;
  }
  &.closed {
    margin-left: -200px;
  }
  .fold {
    position: absolute;
    display: flex;
    align-items: center;
    width: 12px;
    height: 52px;
    top: 50%;
    right: -12px;
    margin-top: -30px;
    border-radius: 0 3px 3px 0;
    transform: perspective(10px) rotateX(0) rotateY(10deg) translateZ(0);
    filter: drop-shadow(3px 0 2px rgba(0, 0, 0, 0.1));
    background-color: #fff;
    background-size: 16px auto;
    cursor: pointer;
    .trigger {
      font-size: 12px;
      color: @disabledColor;
      margin-left: -1px;
    }
  }
  .menu-top {
    height: 50px;
    border-bottom: 1px solid @borderColorSecondary;
    & > a {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    h2 {
      display: flex;
      justify-content: center;
      padding: 0 @modulePadding;
      font-size: @textSize + 2px;
      line-height: 1.2;
      .icon {
        padding: 4px;
        font-size: @textSize;
        color: @disabledColor;
      }
    }
    h5 {
      font-size: @textSizeSecondary;
      color: @disabledColor;
      line-height: 1.2;
    }
  }
  .menu-wrap {
    flex: 1 0;
    overflow: hidden;
  }
  .ant-menu {
    border-right: 0;
    &-inline {
      .ant-menu-item,
      .ant-menu-submenu-title {
        width: 100%;
      }
    }
    .ant-menu-item {
      margin-top: 4px;
      margin-bottom: 4px !important;
      &:hover {
        .icon {
          color: @primaryColor;
          display: block;
        }
      }
      .icon {
        position: absolute;
        top: 6px;
        right: 8px;
        padding: 6px 0;
        display: none;
        &.actived,
        &.ant-popover-open {
          color: @primaryColor;
          display: block;
        }
      }
      &::after {
        right: auto;
        left: 0;
      }
    }
    .anticon {
      color: @textColorSecondary;
    }
  }
}

.app-layout {
  &__sm {
    @item-height: 30px;
    .app-sider {
      .menu-top {
        height: 44px;
        h2 {
          font-size: @textSize;
        }
      }
      .menu-wrap {
        height: calc(100% - 44px);
      }
      .ant-menu {
        .ant-menu-submenu-title,
        .ant-menu-item {
          height: @item-height;
          line-height: @item-height;
          .icon {
            margin-top: -4px;
          }
        }
      }
    }
  }
}

.side-menu__popper {
  .ant-popover-inner-content {
    padding: 5px @modulePadding;
    .popup {
      cursor: pointer;
    }
  }
}
