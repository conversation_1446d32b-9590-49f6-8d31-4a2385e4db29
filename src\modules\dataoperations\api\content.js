// content.js
import axios from '@/api/fetch';

/**
 * 查询埋点记录
 */
export const getLxxMessageRecord = (params) => {
  return axios.post('/content/content-resource-admin/v1/ai/getLxxMessageRecord', params, {
    noAlert: true
  });
}

/**
 * 导出埋点记录
 */
export const lxxMessageRecordExcel = (params) => {
  return axios.post('/content/content-resource-admin/v1/ai/lxxMessageRecordExcel', params, {
    responseType: 'blob' // Important for file downloads
  });
}