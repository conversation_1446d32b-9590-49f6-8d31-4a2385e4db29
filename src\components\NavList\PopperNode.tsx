/*
 * @Author: 焦质晔
 * @Date: 2023-11-18 22:58:43
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-19 01:19:47
 */
import React from 'react';
import config from '@/config';

import type { INavMenu } from '@/store/reducers/app';

import Trigger from 'rc-trigger';
import NavDropdown from '../NavDropdown';
import { CloseOutlined } from '@/icons';

type IProps = {
  visible: boolean;
  item?: INavMenu;
  close: () => void;
  clearTimer2: () => void;
  onVisibleChange: (val: boolean) => void;
  children?: any;
};

export const MOUSE_ENTER_DELAY = 400;
export const MOUSE_LEAVE_DELAY = 200;

const PopperNode: React.FC<IProps> = (props) => {
  const { visible, item, close: closeHandle, clearTimer2, onVisibleChange } = props;

  const renderNavPop = () => {
    return (
      <>
        <div className={`close-btn`} onClick={() => closeHandle()}>
          <CloseOutlined />
        </div>
        <NavDropdown
          navItem={item}
          visible={visible}
          onClose={() => closeHandle()}
          onMouseEnter={() => clearTimer2()}
        />
      </>
    );
  };

  return (
    <Trigger
      action={['hover']}
      popupVisible={config.isMainApp ? visible : false}
      popup={renderNavPop()}
      popupClassName={`nav-list__popper`}
      onPopupVisibleChange={onVisibleChange}
      builtinPlacements={{
        bottomLeft: {
          points: ['tl', 'bl'],
          offset: [0, 1],
          overflow: {
            adjustX: 1,
            adjustY: 1,
          },
        },
      }}
      mouseEnterDelay={MOUSE_ENTER_DELAY / 1000}
      mouseLeaveDelay={MOUSE_LEAVE_DELAY / 1000}
      prefixCls="ant-select-dropdown"
      popupPlacement="bottomLeft"
      popupTransitionName="ant-slide-up"
    >
      {props.children}
    </Trigger>
  );
};

export default PopperNode;
