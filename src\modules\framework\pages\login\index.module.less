/*
 * @Author: 焦质晔
 * @Date: 2021-07-20 16:35:57
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-03-05 19:07:31
 */
.login_wrapper {
  width: 100%;
  height: 100vh;
  background: url(@/assets/img/login_bg.svg) no-repeat;
  background-size: cover;
  display: flex;
  .left {
    flex: 1;
    background: url(./assets/login_left_bg.jpg) no-repeat;
    background-size: cover;
    position: relative;
    .logo {
      position: absolute;
      left: 60px;
      top: 40px;
      img {
        -webkit-user-drag: none;
      }
    }
  }
  .right {
    width: 32%;
    overflow-y: auto;
  }
  .main {
    padding: 15vh 40px 20px;
    .title {
      height: 70px;
      background: url(./assets/title.png) no-repeat;
      background-size: contain;
    }
    .container {
      padding-top: 10vh;
      .login_form {
        :global(.ant-input),
        :global(.ant-input-password) {
          border-left-color: transparent;
          border-top-color: transparent;
          border-right-color: transparent;
          &:focus {
            box-shadow: none;
          }
        }
        :global(.ant-input-affix-wrapper-focused) {
          box-shadow: none;
        }
        .forget_pwd {
          text-align: right;
          margin-top: -10px;
          font-size: 13px;
          line-height: 1;
          margin-bottom: 6vh;
        }
        .btn {
          width: 100%;
        }
        .tooltip {
          margin-top: 10px;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.65);
        }
      }
      .divider {
        color: #999;
        margin-top: 12vh;
      }
    }
  }
}
