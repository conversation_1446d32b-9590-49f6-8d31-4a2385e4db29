import React, { useState, useEffect } from 'react';
import { Modal, Button, Progress, Table, Spin, message } from 'antd';
import { getAnnotationStats } from '@/modules/dataoperations/api/taskAnnotation';
import styles from './index.module.less';

const StatsModal = ({ visible, onCancel, taskData }) => {
  const [loading, setLoading] = useState(false);
  const [statsData, setStatsData] = useState(null);

  useEffect(() => {
    if (visible && taskData) {
      const fetchStats = async () => {
        setLoading(true);
        try {
          const res = await getAnnotationStats(taskData.id);
          if (res.data) {
            setStatsData(res.data);
          }
        } catch (error) {
          message.error('获取统计数据失败');
        } finally {
          setLoading(false);
        }
      };
      fetchStats();
    }
  }, [visible, taskData]);

  if (!taskData) return null;

  const { annotatedCount, totalCount } = statsData || taskData;
  const progressPercent = totalCount > 0 ? Math.round((annotatedCount / totalCount) * 100) : 0;

  const statsList = statsData?.annotationList?.flatMap((type, index) => {
    // 如果子类型列表存在，则优先展示子类型
    if (type.subtypeList && type.subtypeList.length > 0) {
      return type.subtypeList.map(sub => ({
        key: `${type.annotationType}-${sub.annotationSubtype}`,
        type: `${type.annotationTypeName} - ${sub.annotationSubtypeName}`,
        count: sub.count
      }))
    }
    // 否则展示主类型
    return {
      key: type.annotationType || index,
      type: type.annotationTypeName,
      count: type.count,
    }
  }) || [];


  const columns = [
    { title: '序号', key: 'index', render: (text, record, index) => index + 1, width: 80 },
    { title: '标注类型', dataIndex: 'type', key: 'type' },
    { title: '数量', dataIndex: 'count', key: 'count' },
  ];

  return (
    <Modal
      title="数据统计"
      open={visible}
      onCancel={onCancel}
      footer={[<Button key="confirm" type="primary" onClick={onCancel}>确定</Button>]}
    >
      <Spin spinning={loading}>
        <div className={styles.statsHeader}>
          <div className={styles.progressInfo}>
            <span>标注情况</span>
            <span className={styles.total}>已标注 {annotatedCount} / 标注总量 {totalCount}</span>
          </div>
          <Progress percent={progressPercent} showInfo={true} />
        </div>
        {statsList.length > 0 ? (
          <Table columns={columns} dataSource={statsList} pagination={false} rowKey="key" />
        ) : (
          !loading && <div className={styles.noData}>
            <div className={styles.icon}>
              <svg width="64" height="41" viewBox="0 0 64 41" xmlns="http://www.w3.org/2000/svg"><g transform="translate(0 1)" fill="none" fillRule="evenodd"><ellipse fill="#F5F5F5" cx="32" cy="33" rx="32" ry="7"></ellipse><g fillRule="nonzero" stroke="#D9D9D9"><path d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"></path><path d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z" fill="#FAFAFA"></path></g></g></svg>
            </div>
            <p>暂无数据</p>
          </div>
        )}
      </Spin>
    </Modal>
  );
};

export default StatsModal;