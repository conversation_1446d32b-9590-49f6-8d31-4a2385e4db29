/**
 * @Author: 焦质晔
 * @Date: 2019-06-20 10:00:00
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-23 12:08:15
 */
'use strict';

const path = require('path');
const utils = require('./utils');
const webpack = require('webpack');
const config = require('../config');
const createThemeColorPlugin = require('./theme.plugin');
const ModuleFederationPlugin = require('webpack').container.ModuleFederationPlugin;

function isModuleFederation() {
  const { moduleFederation = {} } = config;
  return !utils.isEmpty(moduleFederation.exposes) || !utils.isEmpty(moduleFederation.remotes);
}

function getPublicPath() {
  return process.env.NODE_ENV === 'production'
    ? config.build.assetsPublicPath
    : config.dev.assetsPublicPath;
}

const baseWebpackConfig = {
  target: ['browserslist'],
  entry: {
    app: isModuleFederation() ? utils.resolve('src/index.ts') : utils.resolve('src/bootstrap.ts'),
  },
  output: {
    path: config.build.assetsRoot,
    filename: 'js/[name].js',
    publicPath: getPublicPath(),
    // for micro-app
    ...(config.name !== 'app'
      ? {
          library: `${config.name}_micro_[name]`,
          libraryTarget: 'umd',
          chunkLoadingGlobal: `webpackJsonp_${config.name}`,
          globalObject: 'window',
        }
      : null),
  },
  resolve: {
    // 配置解析规则
    extensions: ['.ts', '.tsx', '.js', '.jsx', '.json'],
    alias: config.pathAlias,
  },
  stats: {
    warningsFilter: [/async\/await/],
  },
  module: {
    rules: [
      // js jsx
      {
        test: /\.js(x)?$/,
        use: utils.jsLoaders(),
        exclude: /node_modules\/(?!@jiaozhiye\/qm-platform-components)/,
        include: [
          utils.resolve('src'),
          utils.resolve('node_modules/@jiaozhiye/qm-platform-components'),
        ],
      },
      // mjs
      {
        test: /\.m?js/,
        resolve: {
          fullySpecified: false,
        },
        include: [/node_modules/],
      },
      // ts tsx
      {
        test: /\.ts(x)?$/,
        use: utils.tsLoaders(),
        exclude: /node_modules\/(?!@jiaozhiye\/qm-platform-components)/,
        include: [
          utils.resolve('src'),
          utils.resolve('node_modules/@jiaozhiye/qm-platform-components'),
        ],
      },
      // do not base64-inline SVG
      {
        test: /\.(svg)(\?.*)?$/i,
        type: 'asset/resource',
        generator: {
          filename: utils.assetsPath('img/[contenthash:8][ext][query]'),
        },
      },
      // images
      {
        test: /\.(png|jpe?g|gif|webp)(\?.*)?$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 1024 * 1024, // 小于 1M 表现形式为 baser64；大于 1M 文件会被生成到输出到目标目录
          },
        },
        generator: {
          filename: utils.assetsPath('img/[contenthash:8][ext][query]'),
        },
      },
      // fonts
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 1024 * 10, // 10K
          },
        },
        generator: {
          filename: utils.assetsPath('fonts/[contenthash:8][ext][query]'),
        },
      },
      // media
      {
        test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i,
        type: 'asset/resource',
        generator: {
          filename: utils.assetsPath('media/[contenthash:8][ext][query]'),
        },
      },
    ],
  },
  plugins: [
    new webpack.DefinePlugin({
      'process.argv': JSON.stringify(process.argv.slice(2)),
      'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV),
      'process.env.ENV_CONFIG': JSON.stringify(process.env.ENV_CONFIG),
      'process.env.PUBLIC_PATH': JSON.stringify(getPublicPath()),
      'process.env.ENV_BRAND': JSON.stringify(config.brand),
      'process.env.ENV_APP_NAME': JSON.stringify(config.name),
      'process.env.ENV_APP_CODE': JSON.stringify(config.code),
      'process.env.THEME_COLOR': JSON.stringify(config.theme),
    }),
    createThemeColorPlugin(),
  ],
};

if (isModuleFederation()) {
  let { exposes, remotes, ...rest } = config.moduleFederation;
  if (remotes) {
    for (const key in remotes) {
      remotes[key] = key + '@' + remotes[key].replace(/\/$/, '') + '/remoteEntry.js';
    }
  }
  const deps = require('../package.json').dependencies;
  baseWebpackConfig.plugins.push(
    new ModuleFederationPlugin({
      name: `${config.name}_app`,
      library: utils.isEmpty(remotes) ? { type: 'window', name: `${config.name}_app` } : undefined,
      filename: 'remoteEntry.js',
      exposes,
      remotes,
      shared: {
        ...deps,
        react: { singleton: true, requiredVersion: deps.react },
        'react-dom': { singleton: true, requiredVersion: deps['react-dom'] },
      },
      ...utils.omit(rest, ['name', 'library', 'filename', 'exposes', 'remotes']),
    })
  );
  if (!utils.isEmpty(remotes)) {
    baseWebpackConfig.plugins.push(
      new webpack.DefinePlugin({
        __REMOTE_CONFIG__: JSON.stringify(remotes),
      })
    );
  }
}

module.exports = baseWebpackConfig;
