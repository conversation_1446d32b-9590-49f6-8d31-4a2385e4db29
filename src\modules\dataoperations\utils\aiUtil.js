import { createUidKey } from '@/utils';
import dayjs from 'dayjs';
import { APPID, DEFAULT_TITLE } from '../pages/chat/constants';
import { getUserInfo } from '@/utils/cookies';
import { saveSessionRecord } from '../api';
import { getSessionStorageItem, removeSessionStorageItem, setSessionStorageItem } from './storageUril';

const AICHATLOGKEY = 'aiChatLog';


export const getSessionId = () => {
    let sessionId = getSessionStorageItem(AICHATLOGKEY)?.sessionId;
    if (!sessionId) {
        sessionId = renewSessionId();
    }
    return sessionId;
}
export const renewSessionId = (sessionId) => {
    if (sessionId) {
        setSessionStorageItem(AICHATLOGKEY, { sessionId });
        return sessionId;
    }
    const newid = createUidKey();
    setSessionStorageItem(AICHATLOGKEY, { sessionId: newid });
    return newid;
}

export const saveNewChatLog = (sessionId, chatLog, title, uploadFlag, id) => {
    const keyId = id === '' ? undefined : (id || getChatLog()?.keyId);
    const chatLogObj = {
        sessionId,
        chatLog,
        title,
        keyId
    }
    const onlineObj = {
        id: keyId,
        appId: APPID,
        userId: getUserInfo().loginName,
        sessionId,
        chatTitle: title,
        createdAt: chatLog?.length > 0 ? dayjs(chatLog[0]?.createdAt).format('YYYY-MM-DD HH:mm:ss') : dayjs().format('YYYY-MM-DD HH:mm:ss'),
        updateAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        detailList: chatLog?.map(item => ({
            chatId: item.id,
            type: item.isAI ? 1 : 0,
            chatText: item.content,
            likeFlag: item.likeFlag,
            createTime: dayjs(item.createdAt).format('YYYY-MM-DD HH:mm:ss'),
            updateTime: dayjs(item.updateAt).format('YYYY-MM-DD HH:mm:ss'),
        }))
    }
    if (onlineObj.chatTitle !== DEFAULT_TITLE && uploadFlag) {
        saveSessionRecord(onlineObj).then(
            (res => {
                const log = getChatLog();
                res.data.id && (log['keyId'] = res.data.id);
                updateChatLog(log);
               
            })).catch(err => {
                console.log('err', err);
            }
        )
    }
    const updateTime = dayjs().valueOf();
    chatLogObj['updateTime'] = updateTime;
    setSessionStorageItem(AICHATLOGKEY, chatLogObj);
    return [chatLogObj];
}
export const updateChatLog = (chatObj) => {
    setSessionStorageItem(AICHATLOGKEY, chatObj);
}

export const getChatLog = () => {
    return getSessionStorageItem(AICHATLOGKEY);
}
export const cleanChatLog = () => {
    removeSessionStorageItem(AICHATLOGKEY);
}

