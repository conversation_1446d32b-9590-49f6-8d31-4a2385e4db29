/*
 * @Author: 焦质晔
 * @Date: 2021-07-07 11:06:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-05-09 14:56:44
 */
import React from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import { useSelector, useDispatch } from '@/store';
import {
  createTheme,
  createLocaleLang,
  createComponentSize,
  createSiteInfo,
} from '@/store/actions';
import { isIframe } from '@/router';
import { changeLocale } from '@/locale';
import { getMicroEvent } from '@/utils/mitt';
import { useApplication, useEvent, useUpdateEffect } from '@/hooks';
import { useCompact } from '@/layout/modules/SizeSetting/useCompact';
import { local } from '@/utils/storage';
import * as types from '@/store/types';
import config from '@/config';
import { getComponentConfig, saveTableColumnsConfig } from '@/api/application';

import DeferLoader from '@/layout/modules/DeferLoader';
import type { AppState } from '@/store/reducers/app';
import type { ComponentSize, Language } from '@/utils/types';

import { QmConfigProvider, notification, message } from '@jiaozhiye/qm-design-react';
import '@jiaozhiye/qm-design-react/lib/style/index.less';
import '@/assets/css/reset.less';
import '@/assets/css/style.less';
import '@/assets/css/antd-ui.less';
import '@/assets/css/iconfont.less';

notification.config({
  duration: 4.5,
  maxCount: 1,
});

message.config({
  duration: 2,
  maxCount: 3,
});

type IProps = {
  children?: React.ReactNode;
};

const UseProvider: React.FC<IProps> = (props) => {
  const { themeColor, lang, size } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const history = useHistory();
  const { pathname } = useLocation();
  const {
    addTabMenus,
    startMicroApp,
    openView,
    refreshView,
    closeView,
    popEventHandle,
    setControlTab,
    setLocalStore,
    emitOutsideClick,
    dispatchMouseClick,
  } = useApplication();

  useCompact(size);

  const globalConfig = React.useRef({
    autoInsertSpaceInButton: false,
    tinymce: {
      scriptSrc: config.baseUrl.replace(/\/$/, '') + '/static/tinymce/tinymce.min.js',
    },
    // getComponentConfigApi: getComponentConfig,
    // saveComponentConfigApi: saveTableColumnsConfig,
  });

  React.useLayoutEffect(() => {
    registerMicroApp();
    window.addEventListener('message', messageEventHandle);
    const localTheme = localStorage.getItem('theme_color');
    if (localTheme && localTheme !== themeColor) {
      setThemeColor(localTheme);
    }
    const microEvent = getMicroEvent();
    if (config.powerByMicro) {
      microEvent?.$on(types.COMP_SIZE, setComponentSize);
      microEvent?.$on(types.LOCALE_LANG, setLocaleLang);
      microEvent?.$on(types.THEME_COLOR, setThemeColor);
    }
    if (isIframe(pathname)) {
      document.addEventListener('click', clickEventHandle);
    }
    let unlisten;
    if (config.microType === 'micro-app') {
      unlisten = history.listen(popEventHandle);
    }
    return () => {
      if (config.powerByMicro) {
        microEvent?.$off(types.COMP_SIZE, setComponentSize);
        microEvent?.$off(types.LOCALE_LANG, setLocaleLang);
        microEvent?.$off(types.THEME_COLOR, setThemeColor);
      }
      if (config.microType === 'micro-app') {
        unlisten?.();
      }
      window.removeEventListener('message', messageEventHandle);
      document.removeEventListener('click', clickEventHandle);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useUpdateEffect(() => {
    addTabMenus(pathname);
  }, [pathname]);

  const registerMicroApp = () => {
    startMicroApp();
  };

  const clickEventHandle = () => {
    emitOutsideClick();
  };

  const setComponentSize = (value: ComponentSize) => {
    dispatch(createComponentSize(value));
    localStorage.setItem('size', value);
  };

  const setLocaleLang = (value: Language) => {
    dispatch(createLocaleLang(value));
    changeLocale(value);
  };

  const setThemeColor = (value: string) => {
    dispatch<any>(createTheme(value));
  };

  const setSiteInfo = (value: Record<string, string>) => {
    dispatch(createSiteInfo(value));
  };

  const messageEventHandle = useEvent(({ data }) => {
    if (typeof data !== 'object') return;
    if (data.type === types.OUTSIDE_CLICK) {
      dispatchMouseClick();
    }
    if (data.type === types.THEME_COLOR) {
      setThemeColor(data.data);
    }
    if (data.type === types.THEME_TYPE) {
      // ...
    }
    if (data.type === types.LOCALE_LANG) {
      setLocaleLang(data.data);
    }
    if (data.type === types.COMP_SIZE) {
      setComponentSize(data.data);
    }
    if (data.type === types.SITE_INFO) {
      setSiteInfo(data.data);
    }
    if (data.type === types.SEND_LOCAL) {
      const { size: $size, lang: $lang, theme_color: $theme } = data.data;
      if (size !== $size) {
        setComponentSize($size);
      }
      if (lang !== $lang) {
        setLocaleLang($lang);
      }
      if (themeColor !== $theme) {
        setThemeColor($theme);
      }
      setLocalStore(data.data);
    }
    // micro-app 环境下，window 的 `message` 事件未被隔离，当主应用触发事件时，子应用也会被触发
    if (config.powerByMicro) return;
    if (data.type === types.OPEN_VIEW) {
      const [pathname, search = ''] = data.data.split('?');
      if (!data.reload) {
        openView(data.data);
      } else {
        refreshView(pathname, `?${search}`);
      }
    }
    if (data.type === types.CLOSE_VIEW) {
      closeView(data.data);
    }
    if (data.type === types.REFRESH_VIEW) {
      refreshView(pathname);
    }
    if (data.type === types.PREVENT_TAB) {
      setControlTab(data.data);
    }
    if (data.type === types.SIGN_OUT) {
      local.setItem('tab_menus', []);
      window.location.href = `${config.baseRoute}/login`;
    }
  });

  return (
    <QmConfigProvider locale={lang} size={size} global={globalConfig.current}>
      <DeferLoader>
        {isIframe(pathname) ? (
          <section className={`app-iframe`}>{props.children}</section>
        ) : (
          props.children
        )}
      </DeferLoader>
    </QmConfigProvider>
  );
};

export default UseProvider;
