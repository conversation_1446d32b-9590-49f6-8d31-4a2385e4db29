/*
 * @Author: 焦质晔
 * @Date: 2023-06-09 13:39:23
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-06-09 15:22:03
 */
import React from 'react';
import classNames from 'classnames';
import { isEqual } from 'lodash-es';
import { local } from '@/utils/storage';

import { ReactSortable } from 'react-sortablejs';
import { Dropdown, Checkbox } from '@jiaozhiye/qm-design-react';
import { MoreOutlined, HolderOutlined } from '@/icons';

import './index.less';

type IModuleItem = {
  key: string;
  name: string;
  visible: boolean;
  component: React.ReactNode;
};

type IProps = {
  items: IModuleItem[];
  onChange: (items: IModuleItem[]) => void;
};

const ActionSetting: React.FC<IProps> = (props) => {
  const { items, onChange } = props;

  const [visible, setVisible] = React.useState<boolean>(false);

  const handleChange = (list: IModuleItem[]) => {
    onChange(list);
    // 本地存储
    local.setItem(
      'toolbar_setting',
      list.map((x) => ({ key: x.key, visible: x.visible }))
    );
  };

  const popupRender = () => {
    return (
      <div className={`action-list`}>
        <ReactSortable
          itemKey="key"
          handle=".handle"
          tag="ul"
          animation={200}
          list={items as any[]}
          setList={(list) => {
            const fns1: string[] = list.map((x) => x.key);
            const fns2: string[] = items.map((x) => x.key);
            if (isEqual(fns1, fns2)) return;
            handleChange(list as unknown as IModuleItem[]);
          }}
        >
          {items.map((item) => (
            <li key={item.key} className="item">
              <Checkbox
                checked={item.visible}
                onChange={(ev) => {
                  const { checked } = ev.target;
                  item.visible = checked;
                  handleChange([...items]);
                }}
              />
              <HolderOutlined className="handle" />
              <span className="title">{item.name}</span>
            </li>
          ))}
        </ReactSortable>
      </div>
    );
  };

  return (
    <div className={classNames('app-action-setting')} style={{ marginRight: -10 }}>
      <Dropdown
        open={visible}
        trigger={['click']}
        dropdownRender={() => popupRender()}
        overlayClassName="ant-select-dropdown app-action-setting__popper"
        placement="bottomRight"
        onOpenChange={(visible) => {
          setVisible(visible);
        }}
      >
        <span>
          <MoreOutlined className={`icon`} />
        </span>
      </Dropdown>
    </div>
  );
};

export default ActionSetting;
