import React, { useEffect, useRef, useState } from 'react';
import css from './index.module.less';
import { Button, Col, Form, Input, QmTable, Row, Select } from '@jiaozhiye/qm-design-react';
import { Api } from '@/modules/dataoperations/api/AiScene';
import Insert from '@/modules/dataoperations/pages/AIScene/insert';
import NormalTable from '@/modules/dataoperations/pages/normalTable';

export const Index = (props) => {
  const [form] = Form.useForm();

  const [param, setParam] = useState({});

  const [columns, setColumns] = useState([]);

  const [data, setData] = useState({});

  const [showModal, setShowModal] = useState(false);

  const [curRow, setCurRow] = useState(null);

  const tableRef = useRef(null);

  const edit = (row) => {
    setCurRow(row)
    setShowModal(true)
  }

  const createColumn = () => {
    return [
      {
        title: '场景ID',
        dataIndex: 'sceneCode',
      },
      {
        title: '场景名称',
        dataIndex: 'sceneName',
      },
      {
        title: '比对结果',
        dataIndex: 'comparisonResults',
        dictItems: [
          { text: '人工', value: 1 },
          { text: 'ai', value: 0 },
        ],
      },
      {
        title: 'L3/L4流程',
        dataIndex: 'l3ProcessName',
      },
      {
        title: '业务单元',
        dataIndex: 'bizUnitName',
      },
      {
        title: '场景创建时间',
        dataIndex: 'createTime',
      },
      {
        title: '创建人',
        dataIndex: 'createUserName',
      },
      {
        title: '操作',
        dataIndex: '__action__',
        fixed: 'right',
        render: (text, row) => {
          return <Button type={'link'} onClick={() => edit(row)}>编辑</Button>
        }
      },
    ];
  };

  const reset = () => {
    setParam({})
    form.resetFields()
    tableRef.current.onSearch()
  }

  const search = () => {
    const value = form.getFieldsValue(true)
    setParam(value)
    tableRef.current.onSearch()
  }

  const insert = (row) => {
    if (row) {
      setCurRow(row);
    }
    setShowModal(true);
  };

  useEffect(() => {
    setColumns(createColumn());
  }, []);

  return (
    <div className={css.pageWrapper}>
      <div className={css.pageTitle}>AI审核场景管理</div>
      <Form form={form}>
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item name={'sceneName'}>
              <Input placeholder={'请输入场景名称'} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Button type={'primary'} onClick={() => search()}>
              搜索
            </Button>
            <Button style={{ marginLeft: '16px' }} onClick={() => reset()}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
      <NormalTable ref={tableRef} columns={columns} listApi={Api.getAiSceneList} param={param} data={'list'}
                   totalName={'totalCount'}>
        <Button type={'primary'} onClick={insert}>
          新建
        </Button>
      </NormalTable>
      <Insert
        visible={showModal}
        onClose={(val) => {
          setShowModal(false);
          setCurRow(null);
          if (val) {
            search(true);
          }
        }}
        row={curRow}
      />
    </div>
  );
};
export default Index;
