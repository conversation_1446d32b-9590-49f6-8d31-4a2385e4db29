/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 15:48:42
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-05-10 12:51:56
 */
// 工作台列表
export const WORKBENCH_LIST = 'WORKBENCH_LIST';

// 导航列表
export const NAV_LIST = 'NAV_LIST';

// 菜单列表
export const MENU_LIST = 'MENU_LIST';

// 自定义导航
export const CUSTOM_NAV = 'CUSTOM_NAV';

// 子应用菜单
export const SUB_MENU = 'SUB_MENU';

// 顶部选项卡菜单
export const TAB_MENU = 'TAB_MENU';

// iframe 菜单模式
export const IFRAME_MENU = 'IFRAME_MENU';

// micro 菜单模式
export const MICRO_MENU = 'MICRO_MENU';

// 子应用加载状态
export const MICRO_STATE = 'MICRO_STATE';

// 不允许关闭的页签
export const PREVENT_TAB = 'PREVENT_TAB';

// 数据字典
export const DICT_DATA = 'DICT_DATA';

// 界面权限
export const AUTH_DATA = 'AUTH_DATA';

// 按钮权限
export const AUTH_BTN = 'AUTH_BTN';

// 收藏菜单
export const STAR_MENU = 'STAR_MENU';

// 常用菜单
export const COMMON_MENU = 'COMMON_MENU';

// 组件尺寸
export const COMP_SIZE = 'COMP_SIZE';

// 多语言
export const LOCALE_LANG = 'LOCALE_LANG';

// 主题颜色
export const THEME_COLOR = 'THEME_COLOR';

// 主题模式
export const THEME_TYPE = 'THEME_TYPE';

// 设备类型
export const DEVICE = 'DEVICE';

// 当前工作台
export const WORKBENCH = 'WORKBENCH';

// 外部单击
export const OUTSIDE_CLICK = 'OUTSIDE_CLICK';

// 登录
export const SIGN_IN = 'SIGN_IN';

// 退出登录
export const SIGN_OUT = 'SIGN_OUT';

// 站点信息
export const SITE_INFO = 'SITE_INFO';

// 打开窗口
export const OPEN_VIEW = 'OPEN_VIEW';

// 关闭窗口
export const CLOSE_VIEW = 'CLOSE_VIEW';

// 刷新窗口
export const REFRESH_VIEW = 'REFRESH_VIEW';

// 发送本地数据
export const SEND_LOCAL = 'SEND_LOCAL';

// Widget 事件
export const WIDGET_MAIN = 'WIDGET_MAIN';
export const WIDGET_SUB = 'WIDGET_SUB';

// WebSocket 事件
export const WS_EVENT = 'WS_EVENT';

// 子应用事件
export const SUB_EVENT = 'SUB_EVENT';

// 自定义首页
export const CUSTOM_INDEX = 'CUSTOM_INDEX';
