import React, { useCallback, useEffect, useRef } from "react";
import css from './record.module.less';
import recordSvg from '../../../assets/record.svg';
import axios from "axios";
import envMaps from "@/config/envMaps";
import { getToken } from "@/utils/cookies";
import RecordingOverlay from "./recordingOverlay";
import { message } from "@jiaozhiye/qm-design-react";
export default function Record({ onRecord }) {
    const [status, setStatus] = React.useState(0); // 0: 停止 1: 录音中 2: 识别中
    const recordSound = () => {
        console.log('开始录音');
        if (status === 0) {
            // 录音逻辑
            console.log('开始录音');
            startRecord();
        } else if (status === 1) {
            // 停止录音逻辑
            console.log('停止录音');
            stopRecord();
        }
    };
    const mediaRecorder = useRef(null);
    const startRecord = async () => {
        let audioContext, sourceNode, scriptNode, pcmData;
        const TARGET_SAMPLE_RATE = 16000; // 目标采样率16kHz
        try {
            console.log('请求麦克风权限...');

            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

            // 创建音频上下文
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const originalSampleRate = audioContext.sampleRate;
            sourceNode = audioContext.createMediaStreamSource(stream);

            // 创建脚本处理节点
            scriptNode = audioContext.createScriptProcessor(4096, 1, 1);

            // 初始化PCM数据
            pcmData = [];

            // 降采样因子（例如：44100Hz → 16000Hz，factor≈2.756）
            const downsampleFactor = originalSampleRate / TARGET_SAMPLE_RATE;
            let sampleCounter = 0;

            // 处理音频数据
            scriptNode.onaudioprocess = function (e) {
                const samples = e.inputBuffer.getChannelData(0);

                // 降采样到16kHz
                for (let i = 0; i < samples.length; i++) {
                    sampleCounter += 1;
                    if (sampleCounter >= downsampleFactor) {
                        sampleCounter -= downsampleFactor;

                        // 转换为16位PCM（-32768到32767）
                        const pcmSample = Math.max(-1, Math.min(1, samples[i])) * 32767;
                        pcmData.push(pcmSample);
                    }
                }
            };

            // 连接节点
            sourceNode.connect(scriptNode);
            scriptNode.connect(audioContext.destination);

            // 设置MediaRecorder
            mediaRecorder.current = new MediaRecorder(stream);
            let audioChunks = [];

            mediaRecorder.current.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    audioChunks.push(event.data);
                }
            };

            mediaRecorder.current.onstop = () => {
                // 生成WAV文件（采样率设为16000Hz）
                const wavBlob = encodeWAV(pcmData, TARGET_SAMPLE_RATE);
                const audioUrl = URL.createObjectURL(wavBlob);

                // audioPlayer.src = audioUrl;
                // audioPlayer.style.display = 'block';

                // // 创建下载链接
                // downloadLink.href = audioUrl;
                // downloadLink.download = `recording-${new Date().toISOString()}.wav`;
                // downloadLink.style.display = 'inline-block';

                // statusDiv.textContent = "录音完成!";

                // 自动下载录音文件
                // const aTag = document.createElement('a');
                // aTag.href = audioUrl;
                const fileName = `recording-${new Date().toISOString()}.wav`;
                // aTag.download = fileName;
                // aTag.click();
                // aTag.remove();
                // 创建 FormData 对象
                const formData = new FormData();
                formData.append('file', wavBlob, fileName);

                // 上传wavBlob文件到服务器
                axios.post(`${envMaps.aiChatUrl}aiChat/voiceToText`, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                        'Authorization': `Bearer ${getToken()}`
                    }
                }
                ).then(resp => {
                    const text = resp.data?.data?.sentences?.map(item => item.text).join(' ');
                    onRecord(text);
                    setStatus(0);
                }).catch(err => {
                    message.error('语音识别服务异常，请稍后再试');
                    setStatus(0);
                })

                // 关闭音频流和上下文
                stream.getTracks().forEach(track => track.stop());
                if (audioContext) {
                    audioContext.close();
                }
            };

            mediaRecorder.current.start();
            console.log('录音中')
            setStatus(1);
            // startBtn.disabled = true;
            // stopBtn.disabled = false;

        } catch (error) {
            console.error("录音错误:", error);
            // statusDiv.textContent = `错误: ${error.message}`;
        }
    }
    const stopRecord = () => {
        mediaRecorder.current.stop();
        setStatus(2);
    }
    // 将PCM数据编码为WAV格式
    function encodeWAV(samples, sampleRate) {
        const numChannels = 1; // 单声道
        const bytesPerSample = 2; // 16位PCM
        const blockAlign = numChannels * bytesPerSample;
        const byteRate = sampleRate * blockAlign;
        const dataSize = samples.length * bytesPerSample;

        const buffer = new ArrayBuffer(44 + dataSize);
        const view = new DataView(buffer);

        // 写入WAV文件头
        writeString(view, 0, 'RIFF'); // RIFF标识
        view.setUint32(4, 36 + dataSize, true); // 文件总长度
        writeString(view, 8, 'WAVE'); // WAVE标识
        writeString(view, 12, 'fmt '); // fmt块标识
        view.setUint32(16, 16, true); // fmt块长度
        view.setUint16(20, 1, true); // PCM格式
        view.setUint16(22, numChannels, true); // 声道数
        view.setUint32(24, sampleRate, true); // 采样率
        view.setUint32(28, byteRate, true); // 字节率
        view.setUint16(32, blockAlign, true); // 块对齐
        view.setUint16(34, bytesPerSample * 8, true); // 位深度
        writeString(view, 36, 'data'); // data块标识
        view.setUint32(40, dataSize, true); // data块长度

        // 写入PCM数据
        let offset = 44;
        for (let i = 0; i < samples.length; i++) {
            const s = Math.max(-32768, Math.min(32767, samples[i]));
            view.setInt16(offset, s, true);
            offset += 2;
        }

        return new Blob([view], { type: 'audio/wav' });
    }

    // 辅助函数：写入字符串到DataView
    function writeString(view, offset, string) {
        for (let i = 0; i < string.length; i++) {
            view.setUint8(offset + i, string.charCodeAt(i));
        }
    }
    return (
        <>
            <img
                src={recordSvg}
                width={30}
                className={css.sendBtn}
                onClick={() => {
                    recordSound();
                }}
            />
            {(status === 1 || status === 2) && <RecordingOverlay onStopRecording={stopRecord} status = {status}/>}
        </>
    )
}