/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 14:09:00
 */
import React from 'react';
import { Widget } from '@/components';
import { nextTick } from '@/utils';

import { QmButton, Switch } from '@jiaozhiye/qm-design-react';
import V2 from '@framework/pages/dashboard/charts/Chart2';

import { getTableData } from '@test/api/demo';

const Chart2 = () => {
  const widgetRef = React.useRef(null);
  const extraRef = React.useRef(null);

  const [collapsed, setCollapsed] = React.useState(false);

  const clickHandle = () => {
    widgetRef.current.dispatch({
      code: 'DE-0505_APP_BUC__002', // Widget 编号
      payload: { a: 9 },
    });
  };

  return (
    <Widget ref={widgetRef} cols={2} rows={4}>
      <QmButton
        type="primary"
        style={{ position: 'absolute', right: 70, top: 10, zIndex: 9 }}
        onClick={() => clickHandle()}
      >
        发送事件
      </QmButton>
      <Switch
        checked={collapsed}
        style={{ position: 'absolute', right: 10, top: 14, zIndex: 9 }}
        onChange={(val) => {
          setCollapsed(val);
          nextTick(() => {
            // 参数是要显示元素的高度，数值类型
            widgetRef.current.doExpand(val ? extraRef.current.offsetHeight : 0);
          });
        }}
      />
      <V2 fetch={{ api: getTableData }} style={{ minHeight: 348 }} />
      <div ref={extraRef} style={{ display: !collapsed ? 'none' : 'block' }}>
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
        测试内容
        <br />
      </div>
    </Widget>
  );
};

export default Chart2;
