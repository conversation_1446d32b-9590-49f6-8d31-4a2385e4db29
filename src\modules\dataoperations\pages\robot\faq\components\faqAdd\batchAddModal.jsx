import { Input, message, Modal } from "@jiaozhiye/qm-design-react";
import React, { useEffect, useState } from "react";

const BatchAddModal = ({isOpen, setIsOpen, finish}) => {
    useEffect(() => {
        if (!isOpen) {
            setTextValue('');
        }
    }, [isOpen])
    const onConfirm = () => {
        if (!textValue) {
            message.warn('请录入相似问法')
            return;
        }
        const addList = textValue.split('\n');
        finish(addList);
        setIsOpen(false);
    }
    const [textValue, setTextValue] = useState('');
    return <Modal 
        open={isOpen}
        title={'批量添加'}
        onCancel={() => setIsOpen(false)}
        onOk={() => onConfirm()}
        okText={'确认'}
        cancelText={'取消'}
    >
        <p style={{marginBottom: '10px'}}><span style={{color: 'red'}}>*</span> 请录入相似问法，多条相似问法换行分隔</p>

        <Input.TextArea 
            rows={5}
            value={textValue}
            onChange={(e) => {
                setTextValue(e.target.value);
            }}
        />
    </Modal>
}

export default BatchAddModal;