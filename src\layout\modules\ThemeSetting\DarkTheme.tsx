/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:24:11
 */
import React from 'react';
import classNames from 'classnames';
import { useSelector, useDispatch } from '@/store';
import { createThemeType } from '@/store/actions';
import { useLocale } from '@/hooks';

import type { AppState } from '@/store/reducers/app';

import { CheckOutlined } from '@/icons';

import './index.less';

const ThemeColor: React.FC = () => {
  const { themeType } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { t } = useLocale();

  const themeTypeChangeHandle = (theme: string) => {
    dispatch(createThemeType(theme));
    localStorage.setItem('theme_type', theme);
  };

  return (
    <div className={classNames('themeColor')}>
      <div className={classNames('title')}>{t('app.theme.type')}</div>
      <div style={{ display: 'flex' }}>
        <div
          className={classNames('color-type-item', 'item-light')}
          onClick={() => themeTypeChangeHandle('light')}
        >
          <CheckOutlined
            className={classNames('selectIcon')}
            style={{ display: themeType === 'light' ? '' : 'none' }}
          />
        </div>
        <div
          className={classNames('color-type-item', 'item-dark')}
          onClick={() => themeTypeChangeHandle('dark')}
        >
          <CheckOutlined
            className={classNames('selectIcon')}
            style={{ display: themeType === 'dark' ? '' : 'none' }}
          />
        </div>
      </div>
    </div>
  );
};

export default ThemeColor;
