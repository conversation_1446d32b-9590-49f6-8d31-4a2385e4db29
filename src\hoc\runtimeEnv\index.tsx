/*
 * @Author: 焦质晔
 * @Date: 2024-03-05 19:10:53
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-23 14:50:39
 */
import React from 'react';
import '@/locale/setting';
import { useSelector } from '@/store';
import { setGlobalContext, getWorkbenchId } from '@/utils';
import config from '@/config';

import type { AppState } from '@/store/reducers/app';

import { QmConfigProvider } from '@jiaozhiye/qm-design-react';
import ErrorBoundary from '@/pages/errorBoundary';

type RuntimeEnvProps = {
  ctx?: Record<string, string | number>;
  [key: string]: any;
};

const withRuntimeEnv = <T extends RuntimeEnvProps>(
  WrappedComponent: React.ComponentType<T>
): any => {
  const C: React.FC<T> = (props) => {
    const { forwardedRef, ctx, ...rest } = props as any;
    const { lang, size } = useSelector((state: AppState) => state.app);

    const globalConfig = React.useRef({
      autoInsertSpaceInButton: false,
      tinymce: {
        scriptSrc: config.baseUrl.replace(/\/$/, '') + '/static/tinymce/tinymce.min.js',
      },
    });

    const appKeys = React.useMemo<string[]>(() => {
      return Object.keys(window).filter((x) => /(?<!_micro)_app$/.test(x));
    }, []);

    if (appKeys.length <= 1) {
      return <WrappedComponent {...rest} ref={forwardedRef} />;
    }

    setGlobalContext(Object.assign({ systemId: getWorkbenchId(), appCode: config.code }, ctx));

    return (
      <QmConfigProvider locale={lang} size={size} global={globalConfig.current}>
        <ErrorBoundary>
          <WrappedComponent {...rest} ref={forwardedRef} />
        </ErrorBoundary>
      </QmConfigProvider>
    );
  };

  C.displayName = `RunEnv(${WrappedComponent.displayName || WrappedComponent.name})`;

  return React.forwardRef<any, T>((props, ref) => <C {...(props as T)} forwardedRef={ref} />);
};

export default withRuntimeEnv;
