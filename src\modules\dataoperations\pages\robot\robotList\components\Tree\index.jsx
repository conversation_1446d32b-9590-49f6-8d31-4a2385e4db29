import React, { useEffect, useState } from 'react';
import { Input, Tree, Spin } from 'antd';
import { categorytree } from '@/modules/dataoperations/api/robot';
import css from './index.module.less';

const { Search } = Input;
const TreeIndex = ({ env, categoryList, setCategoryList }) => {
  const [treeLoading, setTreeLoading] = useState(false);
  const [treeData, setTreeData] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  useEffect(() => {
    console.log('categoryList :', categoryList);
    let targetIds = categoryList.map((item) => item.id);
    // 找到所有目标节点的路径
    const allPaths = findAllNodePathsByIds(treeData, targetIds);

    // 合并所有路径中的父节点key（不包含目标节点本身）
    const parentKeys = new Set();
    allPaths.forEach((path) => {
      // 去掉最后一个节点（目标节点本身）
      path.slice(0, -1).forEach((key) => parentKeys.add(key));
    });

    // 设置展开的节点
    setExpandedKeys(Array.from(parentKeys));
  }, [treeData, categoryList]);

  // 1. 首先定义查找所有目标节点路径的工具函数
  const findAllNodePathsByIds = (tree, targetIds) => {
    const paths = [];
    const targetIdsSet = new Set(targetIds);
    const nodeMap = new Map(); // 用于存储节点的父节点关系

    // 首先建立节点映射关系
    const buildNodeMap = (nodes, parent = null) => {
      for (const node of nodes) {
        if (parent) {
          nodeMap.set(node.key, parent);
        }
        if (node.children?.length > 0) {
          buildNodeMap(node.children, node);
        }
      }
    };

    buildNodeMap(tree);

    // 对每个目标节点，向上查找父节点路径
    targetIds.forEach((targetId) => {
      const path = [];
      let currentId = targetId;

      while (currentId) {
        path.unshift(currentId);
        currentId = nodeMap.get(currentId)?.key;
      }

      paths.push(path);
    });

    return paths;
  };

  //  勾选
  const onCheck = (checkedKeysValue, val) => {
    setCategoryList(val.checkedNodes.filter((item) => item.children.length == 0));
  };

  // 搜索
  const onSearch = (value) => {
    console.log('value :', value);
    setSearchValue(value);
    // 获取匹配的节点路径
    const expandedKeysSet = new Set();
    const getExpandedKeys = (data, value) => {
      data.forEach((item) => {
        if (item.title.toLowerCase().includes(value.toLowerCase())) {
          expandedKeysSet.add(item.key);
        }
        if (item.children) {
          getExpandedKeys(item.children, value);
        }
      });
    };

    getExpandedKeys(treeData, value);
    setExpandedKeys([...expandedKeysSet]);
    setAutoExpandParent(true);
  };

  // 展开/收起节点时的回调
  const onExpand = (keys) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };

  useEffect(() => {
    async function getTreeData() {
      setTreeLoading(true);
      let res = await categorytree({ env });
      setTreeLoading(false);
      if (res.code != 200) return;
      setTreeData(addTitleAndKey(res.data));
    }
    getTreeData();
  }, [env]);

  // 处理tree数据接口
  const addTitleAndKey = (arr) => {
    const processNode = (node, parentKey, index) => {
      // 添加 title 属性
      node.title = node.name;
      node.value = node.id;
      node.key = node.id;
      node.operation = false;
      node.isEdit = false;
      // // 生成 key
      // if (!parentKey) {
      //   // 第一层节点
      //   node.key = `0-${index}`;
      // } else {
      //   // 子节点
      //   node.key = `${parentKey}-${index}`;
      // }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        node.children.forEach((child, childIndex) => {
          processNode(child, node.key, childIndex);
        });
      }
    };

    // 处理顶层节点
    arr.forEach((item, index) => {
      processNode(item, '', index);
    });
    console.log('arr :', arr);
    return arr;
  };

  return (
    <>
      {/* <Form
        initialValues={{
          radio: '1',
        }}
      >
        <Form.Item name="radio" label="选中绑定方式" rules={[{ required: true }]}>
          <Radio.Group>
            <Radio value="1">自定义绑定</Radio>
            <Radio value="2">绑定所有自动同步更新</Radio>
          </Radio.Group>
        </Form.Item>
      </Form> */}
      <div className={css.treeBox}>
        <Search
          placeholder="搜索机器人名称"
          allowClear
          onSearch={onSearch}
          style={{ width: '100%', marginBottom: 16 }}
        />
        <Spin spinning={treeLoading}>
          <Tree
            height={350}
            checkable
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
            onCheck={onCheck}
            checkedKeys={categoryList.map((item) => item.id)}
            treeData={treeData}
            filterTreeNode={(node) => node.title.toLowerCase().includes(searchValue.toLowerCase())}
          />
        </Spin>
      </div>
    </>
  );
};
export default TreeIndex;
