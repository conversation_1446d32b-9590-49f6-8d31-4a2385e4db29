/* 知识库管理页面样式 */
.knowledgeManagement {
  height: 100%;
  width: 100%;
  background: linear-gradient(180deg, #F5F5FA 0%, #FDFDFF 100%);

  :global(.ant-layout) {
    background: transparent;
  }
}

/* 左侧边栏样式 */
.sidebar {
  background: #FDFDFD;
  border-right: 1px solid #E3E5E9;
  box-shadow: 0px 2px 8px 0px rgba(46, 93, 209, 0.08), 0px 2px 20px 0px rgba(46, 93, 209, 0.04);

  :global(.ant-layout-sider-children) {
    height: 100%;
    overflow: hidden;
  }
}

.sidebarTabs {
  height: 100%;

  :global(.ant-tabs-content-holder) {
    height: calc(100% - 46px);
    overflow-y: auto;
  }

  :global(.ant-tabs-tab) {
    font-weight: 500;
    color: #606266;
    margin-right: 8px;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;

    &:hover {
      color: #409eff;
      background: rgba(64, 158, 255, 0.1);
    }

    &.ant-tabs-tab-active {
      color: #409eff;
      background: #fff;
      border-bottom: 2px solid #409eff;
      font-weight: 600;
    }
  }

  :global(.ant-tabs-nav) {
    background: linear-gradient(180deg, #F5F5FA 0%, #FDFDFF 100%);
    border-bottom: 1px solid #E3E5E9;
    padding: 8px 16px 0;
    margin: 0;
  }
}

.tabContent {
  padding: 24px;
  height: 100%;
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f2f5;
  margin-bottom: 16px;
}

/* 类目树样式 */
.categoryTree {
  :global(.ant-tree-node-content-wrapper) {
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;

    &:hover {
      background-color: #f5f7fa;
    }

    &.ant-tree-node-selected {
      background-color: #ecf5ff;
      color: #409eff;
    }
  }

  :global(.ant-tree-title) {
    font-weight: 500;
  }
}

/* 知识库列表样式 */
.knowledgeList {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.knowledgeCard {
  margin-bottom: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #409eff, #67c23a);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: #409eff;
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.15);
    transform: translateY(-2px);

    &::before {
      opacity: 1;
    }
  }

  &.active {
    border-color: #409eff;
    background: linear-gradient(135deg, #ecf5ff, #f0f9ff);
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);

    &::before {
      opacity: 1;
    }
  }

  :global(.ant-card-meta-title) {
    font-weight: 600;
    color: #303133;
    font-size: 16px;
    line-height: 1.4;
  }

  :global(.ant-card-actions) {
    background: transparent;
    border-top: 1px solid #f0f2f5;

    li {
      margin: 4px 0;

      &:not(:last-child) {
        border-right: 1px solid #f0f2f5;
      }
    }
  }
}

.knowledgeDesc {
  font-size: 13px;
  color: #606266;
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.knowledgeMeta {
  font-size: 12px;
  color: #909399;
  display: flex;
  gap: 16px;

  span {
    display: flex;
    align-items: center;
    gap: 4px;
  }
}

/* 右侧内容区域样式 */
.content {
  padding: 12px;
  overflow-y: auto;
  background: transparent;
}

/* 数据管理内容样式 */
.dataManagement {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.searchCard {
  border-radius: 8px;
  box-shadow: 0px 2px 8px 0px rgba(46, 93, 209, 0.08), 0px 2px 20px 0px rgba(46, 93, 209, 0.04);
  border: 1px solid #E3E5E9;
  background: #FDFDFD;

  :global(.ant-card-body) {
    padding: 20px 24px;
  }
}

.searchHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.searchLeft {
  display: flex;
  align-items: center;
  flex: 1;
}

.categoryName {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.docStats {
  color: #909399;
  font-size: 14px;
}

.searchRight {
  display: flex;
  align-items: center;
}

.tableCard {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  :global(.ant-card-body) {
    padding: 0;
  }

  :global(.ant-table-thead > tr > th) {
    background-color: #fafbfc;
    color: #303133;
    font-weight: 600;
  }

  :global(.ant-table-tbody > tr > td) {
    border-bottom: 1px solid #f0f2f5;
  }

  :global(.ant-table-tbody > tr:hover > td) {
    background-color: #f5f7fa;
  }
}

.pagination {
  padding: 24px;
  text-align: center;
  border-top: 1px solid #f0f2f5;
}

/* 知识库管理内容样式 */
.knowledgeDetail {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.knowledgeHeader {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.knowledgeInfo {
  h2 {
    font-size: 24px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 8px;
  }
}

.knowledgeActions {
  display: flex;
  align-items: center;
}

.documentsCard {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;

  :global(.ant-card-head) {
    border-bottom: 1px solid #f0f2f5;
  }

  :global(.ant-card-body) {
    padding: 0;
  }
}

.emptyState {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sidebar {
    width: 280px !important;
  }
  
  .content {
    padding: 16px;
  }
  
  .searchHeader {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .sidebar {
    width: 240px !important;
  }
  
  .searchLeft {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .headerContent {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
}
