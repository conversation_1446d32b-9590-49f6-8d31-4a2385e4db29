/*
 * @Author: 焦质晔
 * @Date: 2024-07-25 11:23:02
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-07-25 14:08:48
 */
import * as React from 'react';
import useEvent from '../use-event';

type UseOutsideClickProps = {
  ref: React.RefObject<HTMLElement>;
  handler?: (ev: Event) => void;
  enabled?: boolean;
  omitClassNames?: string[];
};

const isElement = (el: any): el is Element =>
  el != null && typeof el == 'object' && 'nodeType' in el && el.nodeType === Node.ELEMENT_NODE;

const getOwnerDocument = (el?: Element | null): Document =>
  isElement(el) ? el.ownerDocument : document;

const isValidElement = (ev: MouseEvent | TouchEvent, omitCls: string[]) => {
  return !omitCls
    .map((x) => Array.from(document.querySelectorAll(x)))
    .flat()
    .some((x) => x?.contains(ev.target as HTMLElement));
};

const isValidEvent = (ev: MouseEvent | TouchEvent, ref: React.RefObject<HTMLElement>) => {
  const target = ev.target as HTMLElement;
  if ('button' in ev && ev.button > 0) return false;
  if (target) if (!getOwnerDocument(target).contains(target)) return false;
  return !ref.current?.contains(target);
};

export default function useClickAway({
  ref,
  handler,
  enabled = true,
  omitClassNames = [],
}: UseOutsideClickProps) {
  const handlerRef = useEvent(handler!);

  const state = React.useRef({
    isPointerDown: false,
    ignoreEmulatedMouseEvents: false,
  });

  React.useEffect(() => {
    if (!enabled) return;

    const onPointerDown = (ev: MouseEvent | TouchEvent) => {
      if (isValidEvent(ev, ref)) state.current.isPointerDown = true;
    };

    const onMouseUp = (ev: MouseEvent) => {
      if (state.current.ignoreEmulatedMouseEvents) {
        state.current.ignoreEmulatedMouseEvents = false;
        return;
      }

      if (
        handler &&
        state.current.isPointerDown &&
        isValidEvent(ev, ref) &&
        isValidElement(ev, omitClassNames)
      ) {
        state.current.isPointerDown = false;
        handlerRef(ev);
      }
    };

    const onTouchEnd = (ev: TouchEvent) => {
      state.current.ignoreEmulatedMouseEvents = true;

      if (
        handler &&
        state.current.isPointerDown &&
        isValidEvent(ev, ref) &&
        isValidElement(ev, omitClassNames)
      ) {
        state.current.isPointerDown = false;
        handlerRef(ev);
      }
    };

    const doc = getOwnerDocument(ref.current);

    doc.addEventListener('mousedown', onPointerDown, true);
    doc.addEventListener('mouseup', onMouseUp, true);
    doc.addEventListener('touchstart', onPointerDown, true);
    doc.addEventListener('touchend', onTouchEnd, true);

    return () => {
      doc.removeEventListener('mousedown', onPointerDown, true);
      doc.removeEventListener('mouseup', onMouseUp, true);
      doc.removeEventListener('touchstart', onPointerDown, true);
      doc.removeEventListener('touchend', onTouchEnd, true);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [handler, ref, handlerRef, state, enabled]);
}
