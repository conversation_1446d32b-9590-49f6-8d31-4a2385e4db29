/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 11:21:01
 */
import React from 'react';
import classNames from 'classnames';
import { useDispatch } from '@/store';
import { createSignOut } from '@/store/actions';
import { useLocale } from '@/hooks';
import { getUserInfo } from '@/utils/cookies';

import { Dropdown, Menu, Avatar, QmDrawer } from '@jiaozhiye/qm-design-react';
import { UserOutlined, SettingOutlined, ClearOutlined, LogoutOutlined } from '@/icons';

import Center from './Center';
import Setting from './Setting';

import './index.less';

const UserCenter: React.FC = () => {
  const dispatch = useDispatch();
  const { t } = useLocale();

  const [visibleUserCenter, setVisibleUserCenter] = React.useState<boolean>(false);
  const [visibleUserSetting, setVisibleUserSetting] = React.useState<boolean>(false);

  const doClearCache = () => {
    window.history.go(0);
  };

  const doLogout = () => {
    // 需要走后台接口
    dispatch<any>(createSignOut());
  };

  const renderMenus = () => {
    const items = [
      {
        key: 1,
        label: getUserInfo().name || t('app.settings.admin'),
        style: { pointerEvents: 'none' } as React.CSSProperties,
      },
      {
        key: 2,
        type: 'divider',
      },
      {
        key: 3,
        icon: <UserOutlined />,
        label: t('app.settings.usercenter'),
        onClick: () => setVisibleUserCenter(true),
      },
      {
        key: 4,
        icon: <SettingOutlined />,
        label: t('app.settings.usersetting'),
        onClick: () => setVisibleUserSetting(true),
      },
      {
        key: 5,
        icon: <ClearOutlined />,
        label: t('app.settings.clearcache'),
        onClick: () => doClearCache(),
      },
      {
        key: 6,
        type: 'divider',
      },
      {
        key: 7,
        icon: <LogoutOutlined />,
        label: t('app.settings.logout'),
        onClick: () => doLogout(),
      },
    ];
    return <Menu items={items} />;
  };

  return (
    <div className={classNames('app-user-center')}>
      <Dropdown
        dropdownRender={() => renderMenus()}
        overlayClassName="app-user-center__popper"
        placement="bottomRight"
        trigger={['click']}
      >
        <span>
          <Avatar size={26} src={require('@/assets/img/avatar.jpg')} />
          <span className={`name icon`}>{getUserInfo().name || t('app.settings.admin')}</span>
        </span>
      </Dropdown>
      <QmDrawer
        visible={visibleUserCenter}
        title={t('app.settings.usersetting')}
        width={'40%'}
        loading={false}
        bodyStyle={{ paddingBottom: 52 }}
        onClose={() => setVisibleUserCenter(false)}
      >
        <Center onClose={() => setVisibleUserCenter(false)} />
      </QmDrawer>
      <QmDrawer
        visible={visibleUserSetting}
        title={t('app.settings.usersetting')}
        width={'40%'}
        loading={false}
        bodyStyle={{ paddingBottom: 52 }}
        onClose={() => setVisibleUserSetting(false)}
      >
        <Setting onClose={() => setVisibleUserSetting(false)} />
      </QmDrawer>
    </div>
  );
};

export default UserCenter;
