/*
 * @Author: 焦质晔
 * @Date: 2022-04-23 19:02:53
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-07-03 16:26:27
 */
import React from 'react';
import { useSelector } from '@/store';
import { useLocale, useUpdateEffect } from '@/hooks';
import { cloneDeep } from '@/utils';
import { useCustomNavs } from './useCustomNavs';
import type { AppState } from '@/store/reducers/app';

import { ReactSortable } from 'react-sortablejs';
import { Dropdown, Button, Tooltip } from '@jiaozhiye/qm-design-react';
import { CloseCircleOutlined } from '@/icons';

import './index.less';

import AppIcon from './assets/app-icon.png';

type INavItem = {
  id: string;
  title: string;
  hidden?: boolean;
};

const NavSetting: React.FC = () => {
  const { lang } = useSelector((state: AppState) => state.app);
  const { t } = useLocale();
  const { customNavList, setLocalNavList } = useCustomNavs();

  const [navItems, setNavItems] = React.useState<INavItem[]>([]);
  const [visible, setVisible] = React.useState<boolean>(false);
  const originNavList = React.useRef<INavItem[]>([]);

  React.useEffect(() => {
    const items: INavItem[] = customNavList.map((x) => ({
      id: x.id!,
      title: x.title,
      hidden: !!x.hideInNav,
    }));
    const results = [{ id: '-', title: t('app.global.home') }, ...items];
    setNavItems(results);
    originNavList.current = cloneDeep(results);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [customNavList]);

  useUpdateEffect(() => {
    setNavItems(
      navItems.map((x, i) => {
        if (i === 0) {
          return Object.assign(x, { title: t('app.global.home') });
        }
        return x;
      })
    );
  }, [lang]);

  const removeHandle = (item: INavItem) => {
    item.hidden = true;
    setNavItems([...navItems]);
  };

  const saveHandle = () => {
    setVisible(false);
    setLocalNavList(navItems.slice(1));
  };

  const renderNavwrap = () => {
    return (
      <div className={`app-nav-setting`} onClick={(ev) => ev.stopPropagation()}>
        <div className={`toper`}>
          <div className={`label`}>
            <span className="title">{t('app.workbench.customNavTitle')}</span>
            <span className="desc">{t('app.workbench.customNavDesc')}</span>
          </div>
          <div className={`nav-list`}>
            <ReactSortable
              itemKey={'id'}
              tag={'ul'}
              animation={200}
              draggable={'.handle'}
              filter={'.ignore'}
              list={navItems as any[]}
              setList={(newState) => setNavItems(newState)}
            >
              {navItems.map((x) => (
                <li
                  key={x.id}
                  className={x.id !== '-' ? 'handle' : 'ignore'}
                  style={{ display: x.hidden ? 'none' : 'block' }}
                >
                  {x.title}
                  <CloseCircleOutlined className="close" onClick={() => removeHandle(x)} />
                </li>
              ))}
            </ReactSortable>
          </div>
          <div className={`nav-btns`}>
            <Button type="primary" onClick={() => saveHandle()}>
              {t('app.button.confirm')}
            </Button>
            <Button
              style={{ marginLeft: 10 }}
              onClick={() => {
                setNavItems(cloneDeep(originNavList.current));
              }}
            >
              {t('app.button.reset')}
            </Button>
          </div>
        </div>
        <div className={`main`}>
          <div className={`label`}>
            <span className="title">{t('app.workbench.addNavTitle')}</span>
            <span className="desc">{t('app.workbench.addNavDesc')}</span>
          </div>
          <div className={`list-all`}>
            <ul>
              {navItems
                .filter((x) => x.hidden)
                .map((x) => (
                  <Tooltip key={x.id} placement="top" title={t('app.workbench.addToNavTitle')}>
                    <li
                      title={x.title}
                      onClick={() => {
                        const _navItems = navItems.filter((k) => k.id !== x.id);
                        setNavItems([..._navItems, { ...x, hidden: false }]);
                      }}
                    >
                      <img src={AppIcon} width="50%" />
                      <h5 className="text_overflow_cut">{x.title}</h5>
                    </li>
                  </Tooltip>
                ))}
            </ul>
          </div>
        </div>
      </div>
    );
  };

  return (
    <Dropdown
      open={visible}
      dropdownRender={() => renderNavwrap()}
      overlayClassName="nav-setting__popper"
      placement="bottomRight"
      trigger={['click']}
      onOpenChange={(visible) => setVisible(visible)}
    >
      <span className={`nav-setting icon`}></span>
    </Dropdown>
  );
};

export default NavSetting;
