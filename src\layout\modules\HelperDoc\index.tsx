/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:22:56
 */
import React from 'react';
import classNames from 'classnames';
import { useLocale } from '@/hooks';

import { Menu, Dropdown } from '@jiaozhiye/qm-design-react';
import { QuestionCircleOutlined } from '@/icons';

import './index.less';

const HelperDoc: React.FC = () => {
  const { t } = useLocale();

  const renderMenus = () => {
    const items = [
      {
        key: 1,
        label: t('app.helperDoc.helpDoc'),
      },
      {
        key: 2,
        label: t('app.helperDoc.useManual'),
      },
    ];
    return <Menu items={items} />;
  };

  return (
    <div className={classNames('app-helper-doc')}>
      <Dropdown dropdownRender={() => renderMenus()} placement="bottomRight" trigger={['click']}>
        <span>
          <QuestionCircleOutlined className={`icon`} />
        </span>
      </Dropdown>
    </div>
  );
};

export default HelperDoc;
