import React, { useEffect } from 'react';
import css from './recordingOverlay.module.less';
import { createPortal } from 'react-dom';

export default function RecordingOverlay({ onStopRecording, status }) {

  return createPortal(
    <div className={css.overlay}>
      <div className={css.content}>
        <div className={css.recordingIndicator}>
          <div className={css.recordingDot}></div>
          <span>{status === 2 ? '识别中...' : '请说话...'}</span>
        </div>
        {status !== 2 && <button 
          className={css.stopButton}
          onClick={onStopRecording}
        >
          点击停止
        </button>}
      </div>
    </div>, document.body
  );
}