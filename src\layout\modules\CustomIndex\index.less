/*
 * @Author: 焦质晔
 * @Date: 2022-12-10 22:33:13
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-02-23 15:48:46
 */
.app-home {
  overflow-y: auto;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: 50% 0;
  .container {
    position: relative;
    margin: 0 50px;
    .grid-item {
      micro-app-body {
        overflow-y: visible;
        overflow-x: hidden;
      }
      &.shadow {
        background-color: #fff;
        border-radius: 6px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.16);
      }
      .no-auth {
        width: 100%;
        height: 100%;
        background: url(./assets/no-auth.svg) 50% 50% no-repeat;
        background-size: contain;
        position: relative;
        & > span {
          position: absolute;
          bottom: @moduleMargin;
          left: @moduleMargin;
          right: @moduleMargin;
          text-align: center;
          color: @textColorTertiary;
        }
      }
    }
  }
}
