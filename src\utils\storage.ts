/*
 * @Author: 焦质晔
 * @Date: 2025-03-28 16:47:14
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-29 08:26:37
 */
const storageFactory = (mode: 'localStorage' | 'sessionStorage') => {
  const storage = window[mode];
  return {
    setItem(key: string, value) {
      try {
        storage.setItem(key, JSON.stringify(value));
      } catch (err) {
        console.error(`Failed to store item in "${key}":`, err);
      }
    },
    getItem(key: string) {
      try {
        const data = storage.getItem(key);
        return data ? JSON.parse(data) : null;
      } catch (err) {
        console.error(`Failed to parse item from "${key}":`, err);
        return null;
      }
    },
    removeItem(key: string) {
      storage.removeItem(key);
    },
    clear() {
      storage.clear();
    },
  };
};

export const local = storageFactory('localStorage');
export const session = storageFactory('sessionStorage');
