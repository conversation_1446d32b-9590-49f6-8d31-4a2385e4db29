/*
 * @Author: 焦质晔
 * @Date: 2021-02-12 15:39:35
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-04 22:15:40
 */
import React from 'react';
import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
  Canceler,
} from 'axios';
import qs from 'qs';
import store from '@/store';
import { createSignOut } from '@/store/actions';
import { getToken, getUserInfo } from '@/utils/cookies';
import { getWorkbenchId, getGlobalContext } from '@/utils';
import { t } from '@/locale';
import config from '@/config';
import type { AjaxResponse } from '@/utils/types';

import InfoTemplate, { FetchNotification } from './template';

interface IRequestConfig extends AxiosRequestConfig {
  cancelable?: boolean;
  lockable?: boolean;
  noAlert?: boolean;
}

interface IAxiosResponse extends Omit<AxiosResponse, 'config'> {
  config: IRequestConfig;
}

interface IAxiosInstance extends Omit<AxiosInstance, 'get' | 'post'> {
  get<T = any, R = AjaxResponse<T>>(url: string, config?: IRequestConfig): Promise<R>;
  post<T = any, R = AjaxResponse<T>>(url: string, data?: any, config?: IRequestConfig): Promise<R>;
}

type IRequestHeader = {
  authorization: string;
  QFCTid: string;
  QFCSid: string;
  lang: string;
  userAgent: string;
  gray?: string;
};

// 自定义扩展 header 请求头
const getConfigHeaders = (): IRequestHeader => {
  const { tenantId, grayInfo } = getUserInfo();
  return {
    authorization: `Bearer ${getToken()}`, // token
    QFCTid: tenantId || '', // 租户 ID
    QFCSid: getWorkbenchId(), // 系统 ID
    lang: localStorage.getItem('lang') || 'zh-cn', // 多语言
    userAgent: 'pc', // 设备
    gray: Array.isArray(grayInfo?.userGroup) ? grayInfo.userGroup.join(',') : '', // 灰度
  };
};

const pendingRequest: Map<string, Canceler> = new Map();
const lockingRequest: Map<string, boolean> = new Map();

const generateReqKey = (config: IRequestConfig): string => {
  const { method, url } = config;
  return [method, url].join('&');
};

const addPendingRequest = (config: IRequestConfig): void => {
  if (!config.cancelable) return;
  const requestKey: string = generateReqKey(config);
  config.cancelToken = new axios.CancelToken((cancel) => {
    if (!pendingRequest.has(requestKey)) {
      pendingRequest.set(requestKey, cancel);
    }
  });
};

const removePendingRequest = (config: IRequestConfig): void => {
  const requestKey: string = generateReqKey(config);
  if (pendingRequest.has(requestKey)) {
    const cancelToken = pendingRequest.get(requestKey) as Canceler;
    cancelToken(t('app.fetch.cancelText'));
    pendingRequest.delete(requestKey);
  }
};

const addLockingRequest = (config: IRequestConfig): void => {
  if (!config.lockable) return;
  const requestKey: string = generateReqKey(config);
  if (!lockingRequest.has(requestKey)) {
    lockingRequest.set(requestKey, true);
  }
};

const removeLockingRequest = (config: IRequestConfig): void => {
  const requestKey: string = generateReqKey(config);
  if (lockingRequest.has(requestKey)) {
    lockingRequest.delete(requestKey);
  }
};

const getErrorText = (code?: number): string => {
  const statusCode = [
    200, 201, 202, 204, 400, 401, 403, 404, 406, 410, 412, 422, 500, 502, 503, 504,
  ];
  if (!statusCode.includes(code as number)) {
    return '';
  }
  return t(`app.fetch.errorCode.${code}`);
};

const createMessage = (data: Record<string, string>) => {
  const typeConf = { I: 'info', W: 'warning', E: 'error' };
  const errTypeConf = { S: 'system', B: 'business' };
  const { siteInfo } = store.getState().app;
  const tempProps = {
    type: typeConf[data.msgType] || 'error',
    message: data.message,
    detailMsg: data.errMsg,
    errCode: data.errCode,
    errType: errTypeConf[data.errType] || 'system',
    extraInfo: siteInfo,
  };
  FetchNotification(React.createElement(InfoTemplate, tempProps), tempProps);
};

const createRequestContext = <T extends IRequestConfig[`headers`]>(headers: T): T => {
  let _value = null;
  try {
    _value = JSON.parse((headers![`qfc-user-para`] as string) || '{}');
  } catch (err) {
    // ...
  }
  const _result: Record<string, string> = {};
  const _context = getGlobalContext();
  for (const k in _context) {
    _result[k] = encodeURIComponent(_context[k]);
  }
  return Object.assign({}, _value, _result);
};

const getBaseURL = (): string => {
  if (process.env.NODE_ENV === 'development') {
    return '/';
  }
  return config.baseUrl;
};

// 创建 axios 实例
const instance: IAxiosInstance = axios.create({
  baseURL: getBaseURL(),
  timeout: 1000 * 20,
  withCredentials: false, // 跨域请求是否携带 cookie
  paramsSerializer: (params): string => {
    // 序列化 GET 请求参数 -> a: [1, 2] => a[0]=1&a[1]=2
    return qs.stringify(params, { arrayFormat: 'indices' });
  },
});

// 异常处理程序
const errorHandler = (error: AxiosError): Promise<AxiosError> => {
  const { isAxiosError, code, config = {}, response = {} } = error;
  const { status, statusText = '' } = response as AxiosResponse;
  const errortext = getErrorText(status) || statusText || t('app.fetch.errorText');
  removePendingRequest(config);
  removeLockingRequest(config);
  if (code !== 'ERR_CANCELED') {
    isAxiosError && createMessage({ message: errortext });
  }
  return Promise.reject(error);
};

// 请求拦截
instance.interceptors.request.use((config: IRequestConfig) => {
  // 锁定当次请求
  if (lockingRequest.has(generateReqKey(config))) {
    return Promise.reject({ message: t('app.fetch.lockText') });
  }
  // 取消已发请求
  removePendingRequest(config);
  // 请求头信息，token 验证
  config.headers = {
    ...getConfigHeaders(),
    ...config.headers,
  };
  // 上下文参数
  if (Object.keys(getGlobalContext()).length) {
    config.headers[`qfc-user-para`] = JSON.stringify(createRequestContext(config.headers));
  }
  // 处理 GET 请求缓存
  if (config.method === 'get') {
    config.params = {
      ...config.params,
      _t: +new Date().getTime(),
    };
  }
  addPendingRequest(config);
  addLockingRequest(config);
  return config;
}, errorHandler);

// 响应拦截
instance.interceptors.response.use((response: IAxiosResponse) => {
  const { config, data } = response;
  removePendingRequest(config);
  removeLockingRequest(config);
  // 文件流，直接过
  if (config.responseType === 'blob') {
    return response;
  }
  // 请求异常提示信息
  if (data.code !== 200 && data.code !== '200') {
    // token 过期 或 无效，需要重新登录
    if (data.code === 55001 || data.code === 55002) {
      store.dispatch<any>(createSignOut());
    }
    if (!config.noAlert) {
      data.message && createMessage(data);
    }
  }
  return data;
}, errorHandler);

export { getConfigHeaders };
export default instance;
