import juice from 'juice';
import * as cheerio from 'cheerio'; // 导入 cheerio

// 1. 导入你自己的模块化样式文件
import editorStyles from './index.module.less';

// 2. 导入 CKEditor 5 的核心样式文件
import ckeditor5Styles from '!!raw-loader!ckeditor5/ckeditor5.css';
import ckcontentStyles from '!!raw-loader!ckeditor5/ckeditor5-content.css';

/**
 * 将 CKEditor 的输出内容及其所有相关样式转换为内联样式，并移除最外层的 .ck-content 包装 div
 * @param {string} html - 从 editor.getData() 获取的 HTML 字符串
 * @returns {string} - 包含了所有内联样式且去除了外层包装的 HTML 字符串
 */
export function convertToInlineStyles(html) {
    if (!html) {
        return '';
    }

    // 将所有样式源合并成一个大的 CSS 字符串
    const allCss = `
    ${ckeditor5Styles}
    ${editorStyles.toString()}
    ${ckcontentStyles}
  `;

    // 使用 juice 进行转换
    const inlinedHtml = juice.inlineContent(html, allCss, {
        removeStyleTags: true,
        preserveMediaQueries: true,
        preserveFontFaces: true,
    });

    // --- 新增的逻辑：使用 cheerio 移除外层 div ---
    if (!inlinedHtml) {
        return '';
    }

    // 1. 将内联样式后的 HTML 加载到 cheerio
    const $ = cheerio.load(inlinedHtml);

    // 2. 查找 class 为 'ck-content' 的 div
    const contentDiv = $('div.ck-content');

    // 3. 判断是否找到了这个 div
    if (contentDiv.length > 0) {
        // 4. 如果找到了，返回它的内部 HTML 内容
        return contentDiv.html();
    } else {
        // 5. 如果没找到（可能输入本身就没有这个 div），则返回原始的内联后结果
        return inlinedHtml;
    }
}