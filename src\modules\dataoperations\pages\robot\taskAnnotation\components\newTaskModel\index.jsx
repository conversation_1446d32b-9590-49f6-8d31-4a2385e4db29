import React, { useEffect, useState } from 'react';
import { Modal, Form, DatePicker, Radio, Input, Select, message, Spin } from 'antd';
import { ExclamationCircleOutlined, CalendarOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { createAnnotationTask, robotlist } from '@/modules/dataoperations/api/taskAnnotation'; // 引入API
import styles from './index.module.less';

const NewTaskModal = ({ visible, onCancel, onOk }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [robotOptions, setRobotOptions] = useState([]);

  // 获取机器人列表
  useEffect(() => {
    if (visible) {
      const fetchRobots = async () => {
        try {
          const res = await robotlist({ pageSize: 99999, pageNum: 1 });
          if (res.data) {
            const options = res.data.records.map(robot => ({ label: robot.robotName, value: robot.id }));
            setRobotOptions(options);
            // 设置默认值
            if (options.length > 0) {
              form.setFieldsValue({ robotId: options[0].value });
            }
          }
        } catch (error) {
          message.error('获取机器人列表失败');
        }
      };

      fetchRobots();

      // 设置默认表单值
      const now = dayjs();
      const todayStart = dayjs().startOf('day');
      const todayEnd = dayjs().endOf('day');
      form.setFieldsValue({
        taskName: `任务标注-${now.format('YYYY-MM-DD HH:mm:ss')}`,
        timeRange: [todayStart, todayEnd],
        dataSource: 'all',
        callType: 'all',
      });
    } else {
      form.resetFields(); // 关闭时重置表单
    }
  }, [visible, form]);

  const handleOk = () => {
    form
      .validateFields()
      .then(async (values) => {
        setLoading(true);
        try {
          const params = {
            taskName: values.taskName,
            robotId: values.robotId,
            dataSource: values.dataSource,
            // API 要求unanswered，前端显示“仅答非所问”，需要转换
            callType: values.callType === 'mismatch_only' ? 'unanswered' : 'all',
            startTime: values.timeRange[0].format('YYYY-MM-DDTHH:mm:ss'),
            endTime: values.timeRange[1].format('YYYY-MM-DDTHH:mm:ss'),
          };
          const res = await createAnnotationTask(params);
          if (res.code == 200) {
            onOk(); // 调用父组件的成功回调
          }
        } catch (error) {
          message.error('新建任务失败');
          console.error(error);
        } finally {
          setLoading(false);
        }
      })
      .catch((info) => {
        console.log('Validate Failed:', info);
        message.error('请检查表单必填项');
      });
  };

  const disabledDate = (current) => {
    return current && (current > dayjs().endOf('day') || current < dayjs().subtract(3, 'month').startOf('day'));
  };

  return (
    <Modal
      title="新建标注任务"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      width={600}
      okText="确认"
      cancelText="取消"
      destroyOnClose
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <Form form={form} layout="vertical" name="newTaskForm">
          <div className={styles.warningBox}>
            <ExclamationCircleOutlined className={styles.icon} />
            数据最大拉取时间为3个月，数据量最大为1000条 (此为示例，具体限制以接口为准)
          </div>
          <Form.Item
            name="timeRange"
            label="时间段"
            rules={[
              { required: true, message: '请选择时间范围' },
              () => ({
                validator(_, value) {
                  if (value && value[1].diff(value[0], 'months') > 3) {
                    return Promise.reject(new Error('时间范围不能超过3个月'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <DatePicker.RangePicker
              showTime={{ format: 'HH:mm:ss' }}
              format="YYYY-MM-DD HH:mm:ss"
              style={{ width: '100%' }}
              disabledDate={disabledDate}
              suffixIcon={<CalendarOutlined />}
            />
          </Form.Item>
          <Form.Item
            name="taskName"
            label="任务名称"
            rules={[
              { required: true, message: '请输入任务名称' },
              { max: 50, message: '任务名称不能超过50个字符' },
            ]}
          >
            <Input placeholder="请输入" />
          </Form.Item>
          <Form.Item name="robotId" label="机器人" rules={[{ required: true, message: '请选择机器人' }]}>
            <Select options={robotOptions} placeholder="请选择" showSearch optionFilterProp='label' loading={robotOptions.length === 0} />
          </Form.Item>
          <Form.Item name="dataSource" label="数据来源" rules={[{ required: true, message: '请选择数据来源' }]}>
            <Radio.Group>
              <Radio value="all">全部</Radio>
              <Radio value="prod">正式环境</Radio>
              <Radio value="test">测试环境</Radio>
            </Radio.Group>
          </Form.Item>
          {/* <Form.Item name="callType" label="通话类型" rules={[{ required: true, message: '请选择通话类型' }]}>
            <Radio.Group>
              <Radio value="all">全部通话</Radio>
              <Radio value="mismatch_only">仅“答非所问”通话</Radio>
            </Radio.Group>
          </Form.Item> */}
        </Form>
      </Spin>
    </Modal>
  );
};

export default NewTaskModal;