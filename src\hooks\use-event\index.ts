/*
 * @Author: 焦质晔
 * @Date: 2021-12-26 08:57:11
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-01-15 12:47:08
 */
import * as React from 'react';
import type { AnyFunction } from '@/utils/types';

export default function useEvent<T extends AnyFunction<any>>(callback: T): T {
  const fnRef = React.useRef<T>();

  fnRef.current = callback;

  const memoFn = React.useCallback((...args: Parameters<T>) => fnRef.current?.(...args), []);

  return memoFn as T;
}
