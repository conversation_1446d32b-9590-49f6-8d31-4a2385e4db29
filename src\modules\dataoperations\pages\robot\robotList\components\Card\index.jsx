import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Space, Button, Card, Tag, Popconfirm, message } from 'antd';
import Clock from '@/modules/dataoperations/assets/clock.png';
import Person from '@/modules/dataoperations/assets/person.png';
import { robotdelete, robotpublish, robotdetail } from '@/modules/dataoperations/api/robot';
import css from './index.module.less';
import cs from 'classnames';

// 组件定义
const Info = ({ title, status, person, date, isShow, updateFuc }) => {
  return (
    <div className={css.info}>
      <div className={cs(css.head, css.textWrap)}>{title}</div>
      <div>
        <Tag color={status == '草稿' ? 'default' : 'success'}>{status}</Tag>
      </div>
      <div className={css.imgBox}>
        <div className={cs(css.txt, css.textWrap)}>最后发布</div>
        <img src={Person} alt="" className={css.img} />
        <div className={css.textWrap}>{person}</div>
      </div>
      <div className={css.imgBox}>
        <img src={Clock} alt="" className={css.img} />
        <div className={css.textWrap}>{date}</div>
      </div>
      {isShow && (
        <Button
          type="link"
          style={{ padding: 0 }}
          onClick={() => {
            updateFuc('view');
          }}
        >
          查看
        </Button>
      )}
    </div>
  );
};

// 组件容器
const InfoContainer = ({ children }) => {
  return <div>{children}</div>;
};

const CardIndex = ({ data, handleSearch, updateFuc }) => {
  return (
    <Card className={css.card}>
      <div className={css.title}>
        <div className={css.titleTxt}>{data?.robotName}</div>
        <Space>
          <Popconfirm
            title="机器人删除后将同步删除生产环境,是否确定？"
            onConfirm={async () => {
              let res = await robotdelete(data.id);
              if (res.code != 200) return;
              message.success('删除成功');
              handleSearch();
            }}
            okText="确定"
            cancelText="取消"
          >
            <Button type="danger" ghost>
              删除
            </Button>
          </Popconfirm>
          <Button
            onClick={async () => {
              let res = await robotpublish({ robotId: data.id });
              if (res.code != 200) return;
              message.success('发布成功');
              handleSearch();
            }}
          >
            发布
          </Button>
          <Button
            onClick={() => {
              updateFuc();
            }}
          >
            编辑
          </Button>
        </Space>
      </div>
      <div className={css.text}>{`机器人ID：${data?.id}`}</div>
      <InfoContainer>
        <Info
          title="测试环境"
          status={data?.textStatus || '-'}
          person={data?.publishName || '-'}
          date={data?.publishTime || '-'}
        />
        <Info
          title="生产环境"
          status={data?.prodStatus || '-'}
          person={data?.publishName || '-'}
          date={data?.publishTime || '-'}
          isShow={true}
          updateFuc={updateFuc}
        />
      </InfoContainer>
    </Card>
  );
};

export default CardIndex;
