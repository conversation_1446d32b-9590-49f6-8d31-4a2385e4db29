/*
 * @Author: 焦质晔
 * @Date: 2021-07-07 13:44:13
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-05-24 19:33:56
 */
// @ts-nocheck
import React from 'react';
import classNames from 'classnames';
import { ContextMenu, MenuItem, ContextMenuTrigger } from 'react-contextmenu';
import { useLocation } from 'react-router-dom';
import { useSelector } from '@/store';
import { useApplication, useTool, useLocale, useUpdateEffect } from '@/hooks';
import { isRedirect } from '@/router';
import { Confirm } from '@/utils';

import type { AppState } from '@/store/reducers/app';

import { QmTabs } from '@jiaozhiye/qm-design-react';

import './index.less';

const { TabPane } = QmTabs;

const CTX_MENU_ID = 'SIMPLE';

const MultiTab: React.FC = () => {
  const { preventTabs, tabMenus } = useSelector((state: AppState) => state.app);
  const { pathname } = useLocation();
  const { t } = useLocale();
  const { refreshView } = useApplication();
  const { openView, closeView } = useTool();

  const [activeKey, setActiveKey] = React.useState<string>(pathname);

  useUpdateEffect(() => {
    if (isRedirect(pathname)) return;
    if (activeKey !== pathname) {
      setActiveKey(pathname);
    }
  }, [pathname]);

  const findCurTagIndex = (path: string) => {
    return tabMenus.findIndex((x) => x.path === path);
  };

  const closeHandler = (dir: string) => {
    const index = findCurTagIndex(activeKey);
    if (index === -1) return;
    tabMenus.forEach((x, i) => {
      if (i === 0) return;
      if (dir === 'right' && i > index) {
        removeHandler(x.path);
      }
      if (dir === 'left' && i < index) {
        removeHandler(x.path);
      }
      if (dir === 'other') {
        if (i === index) return;
        removeHandler(x.path);
      }
    });
  };

  const doRemove = (path: string) => {
    if (path === activeKey) {
      const v = findCurTagIndex(path);
      const from = tabMenus.find((x) => x.path === tabMenus[v].from) ? tabMenus[v].from : '';
      const nextActiveKey = from || tabMenus[v - 1]?.path || tabMenus[v + 1]?.path || '/home';
      changeHandler(nextActiveKey);
    }
    closeView(path);
  };

  const removeHandler = async (path: string) => {
    const preventTab = preventTabs.find((x) => x.path === path);
    try {
      if (preventTab) {
        const { title = '' } = tabMenus.find((x) => x.path === path) || {};
        await Confirm(preventTab.message || `${title}${t('app.global.leaveText')}`);
      }
      doRemove(path);
    } catch (err) {
      // ...
    }
  };

  const refreshHandler = () => {
    refreshView(activeKey);
  };

  const changeHandler = (path: string) => {
    const { search = '' } = tabMenus.find((x) => x.path === path) || {};
    openView(path + search);
  };

  const editHandler = (path: string, action: string) => {
    if (action !== 'remove') return;
    removeHandler(path);
  };

  return (
    <div className={classNames('app-multi-tab')}>
      <QmTabs
        type="editable-card"
        activeKey={activeKey}
        hideAdd
        onChange={changeHandler}
        onEdit={editHandler}
      >
        {tabMenus.map((item, index) => (
          <TabPane
            key={item.path}
            tab={
              <ContextMenuTrigger id={item.path === activeKey ? CTX_MENU_ID : ''} renderTag="span">
                {item.title}
              </ContextMenuTrigger>
            }
            closable={index > 0}
          />
        ))}
      </QmTabs>
      <ContextMenu id={CTX_MENU_ID} className={classNames('ant-dropdown-menu')}>
        <MenuItem className={classNames('ant-dropdown-menu-item')} onClick={() => refreshHandler()}>
          {t('app.multiTab.refresh')}
        </MenuItem>
        <MenuItem
          className={classNames('ant-dropdown-menu-item')}
          onClick={() => closeHandler('right')}
        >
          {t('app.multiTab.closeRight')}
        </MenuItem>
        <MenuItem
          className={classNames('ant-dropdown-menu-item')}
          onClick={() => closeHandler('left')}
        >
          {t('app.multiTab.closeLeft')}
        </MenuItem>
        {tabMenus.length > 1 && (
          <MenuItem
            className={classNames('ant-dropdown-menu-item')}
            onClick={() => closeHandler('other')}
          >
            {t('app.multiTab.closeOthers')}
          </MenuItem>
        )}
      </ContextMenu>
    </div>
  );
};

export default MultiTab;
