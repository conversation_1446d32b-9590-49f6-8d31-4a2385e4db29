.formBox {
  display: flex;
  justify-content: space-between;
}
.treeBox {
  min-height: 300px;
  border: 1px solid #f2f2f2;
  padding: 6px 10px;
}

.treeNode {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 26px;
  :global {
    .ant-btn > .anticon + span,
    .ant-btn > span + .anticon {
      margin-left: 0;
    }
    .ant-btn-sm {
      font-size: 12px;
    }
  }
  .editBox {
    display: flex;
    gap: 10px;
    align-items: center;
    background: #f2f0ff;
    padding: 0 5px;
    border-radius: 5px;
  }
}
