import React, { useEffect, useRef, useState, useCallback } from 'react';
import {
  Space,
  Input,
  Select,
  Button,
  Row,
  Col,
  Table,
  Tag,
  Tree,
  Radio,
  Drawer,
  Modal,
  message,
  DatePicker,
  Popconfirm,
  Spin,
} from 'antd';
import FaqAdd from './components/faqAdd';
import Category from './components/category';
import {
  knowledgepage,
  categorytree,
  knowledgecreate,
  knowledgeEffectiveSet,
  knowledgedelete,
  knowledgeupdate,
  knowledgetransfer,
  knowledgepublish,
} from '@/modules/dataoperations/api/robot';
import { SettingOutlined, ReloadOutlined } from '@/icons';
import css from './index.module.less';
import dayjs from 'dayjs';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;

const Faq = () => {
  const faqRef = useRef(null);
  const [btnLoading, setBtnLoading] = useState(false);
  const [zybtnLoading, setZyBtnLoading] = useState(false);
  const [rangePickerValue, setRangePickerValue] = useState([]);
  // 列表相关变量
  const [currentId, setCurrentId] = useState('');
  const [settingVisible, setSettingVisible] = useState(false);
  const [settingValue, setSettingValue] = useState(undefined);
  const [actionData, setActionData] = useState({ type: 'add', visible: false, rowData: {} });
  const [treeVisible, setTreeVisible] = useState(false);
  const [pageConfig, setPageConfig] = useState({ pageSize: 10, pageNum: 1 });
  const [total, setTotal] = useState(0);
  const [env, setEnv] = useState('test');
  const [type, setType] = useState('00');
  const [dataSource, setDataSource] = useState([]);
  const [searchInputValue, setSearchInputValue] = useState('');
  const [searchParams, setSearchParams] = useState('');
  const [loading, setLoading] = useState(false);
  const [currentRowData, setCurrentRowData] = useState({});
  // 树部分相关变量
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [treeLoading, setTreeLoading] = useState(false);
  const [zyVisible, setZyVisible] = useState(false);
  const [selectTreeNodeId, setSelectTreeNodeId] = useState('');
  const lastCheckedTreeNodeList = useRef([]);
  const [treeHeight, setTreeHeight] = useState(0);
  const treeContainerRef = useRef(null);

  useEffect(() => {
    async function getData() {
      setLoading(true);
      let res = await knowledgepage({
        ...pageConfig,
        env,
        type,
        ...searchParams,
        categoryIds: lastCheckedTreeNodeList.current,
      });
      setLoading(false);
      if (res.code != 200) return;
      setDataSource(res.data.records);
      setTotal(res.data.total);
    }
    getData();
  }, [env, searchParams, pageConfig, type]);

  const getTreeData = useCallback(async () => {
    setTreeLoading(true);
    let res = await categorytree({ env });
    setTreeLoading(false);
    if (res.code != 200) return;
    setTreeData(addTitleAndKey(res.data));
  }, [env]);

  useEffect(() => {
    getTreeData();
  }, [getTreeData]);

  useEffect(() => {
    const calculateHeight = () => {
      if (treeContainerRef.current) {
        const availableHeight = window.innerHeight - 500;
        setTreeHeight(availableHeight);
      }
    };
    calculateHeight();
    window.addEventListener('resize', calculateHeight);
    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, []);

  // 处理tree数据接口
  const addTitleAndKey = (arr) => {
    const processNode = (node, parentKey, index) => {
      // 添加 title 属性
      node.title = node.name;
      node.value = node.id;
      node.key = node.id;
      node.operation = false;
      node.isEdit = false;
      // 生成 key
      // if (!parentKey) {
      //   // 第一层节点
      //   node.key = `0-${index}`;
      // } else {
      //   // 子节点
      //   node.key = `${parentKey}-${index}`;
      // }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        node.children.forEach((child, childIndex) => {
          processNode(child, node.key, childIndex);
        });
      }
    };

    // 处理顶层节点
    arr.forEach((item, index) => {
      processNode(item, '', index);
    });
    console.log('arr :', arr);
    return arr;
  };

  const isDateBetween = (startDate, endDate) => {
    const currentDate = dayjs().format('YYYY-MM-DD');
    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const current = dayjs(currentDate);

    // 转换为时间戳进行比较
    return current.valueOf() >= start.valueOf() && current.valueOf() <= end.valueOf();
  };

  const selectBefore = (
    <Select
      defaultValue={type}
      onChange={(val) => {
        setType(val);
      }}
    >
      <Option value="00">FAQ题目</Option>
      <Option value="01">FAQ答案</Option>
    </Select>
  );

  const columns = [
    {
      title: 'FAQ ID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: 'FAQ标题',
      dataIndex: 'question',
      key: 'question',
    },
    {
      title: '生效状态',
      dataIndex: 'effectiveType',
      key: 'effectiveType',
      render: (_, record) => {
        return (
          <Tag
            color={
              record.effectiveType == '00' ||
              (record.effectiveType == '01' &&
                isDateBetween(record.effectiveStartTime, record.effectiveEndTime))
                ? 'green'
                : 'error'
            }
          >
            {record.effectiveType == '00' ||
            (record.effectiveType == '01' &&
              isDateBetween(record.effectiveStartTime, record.effectiveEndTime))
              ? '已生效'
              : '禁用'}
          </Tag>
        );
      },
    },
    {
      title: '发布状态',
      key: 'publishStatus',
      dataIndex: 'publishStatus',
      render: (_, { publishStatus }) => {
        return (
          <Tag color={publishStatus == '00' ? 'error' : 'green'}>
            {publishStatus == '00' ? '待发布' : '已发布'}
          </Tag>
        );
      },
    },
    {
      title: '编辑人',
      dataIndex: 'updatedBy',
      key: 'updatedBy',
      render: (_, { updatedBy }) => {
        return <span>{updatedBy || '-'}</span>;
      },
    },

    {
      title: '编辑时间',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (_, { updatedAt }) => {
        return <span>{updatedAt || '-'}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        return env == 'test' ? (
          <Space size="middle">
            <a
              onClick={() => {
                setActionData({ type: 'edit', visible: true, rowData: { id: record.id, env } });
              }}
            >
              编辑
            </a>
            <a
              onClick={() => {
                setSettingVisible(true);
                setSettingValue(undefined);
                setRangePickerValue([]);
                setCurrentId(record.id);
              }}
            >
              生效配置
            </a>
            <a
              onClick={() => {
                setZyVisible(true);
                setCurrentRowData(record);
              }}
            >
              转移
            </a>
            <Popconfirm
              title="此操作将同步删除正式环境知识,是否确定？"
              onConfirm={async () => {
                let res = await knowledgedelete(record.id);
                if (res.code != 200) return;
                message.success('删除成功');
                handleSearch();
              }}
              okText="确定"
              cancelText="取消"
            >
              <a style={{ color: 'red' }}>删除</a>
            </Popconfirm>
          </Space>
        ) : (
          <a
            onClick={() => {
              setActionData({ type: 'view', visible: true, rowData: { id: record.id, env } });
            }}
          >
            预览
          </a>
        );
      },
    },
  ];

  const onSelect = (selectedKeysValue, info) => {
    lastCheckedTreeNodeList.current = [];
    console.log('selectedKeysValue :', selectedKeysValue);
    console.log('onSelect', info);
    if (info.selected) {
      loopCheckedNode(info.node);
    }
    setSelectedKeys(selectedKeysValue);
    setSearchParams({ ...searchParams, categoryIds: lastCheckedTreeNodeList.current });
  };

  const loopCheckedNode = (data) => {
    console.log('data :', data);
    if (data.children.length == 0) {
      lastCheckedTreeNodeList.current.push(data.id);
    } else {
      data.children.map((item) => {
        loopCheckedNode(item);
      });
    }
  };

  // 环境切换
  const handleModeChange = (e) => {
    setEnv(e.target.value);
  };

  // 关闭抽屉
  const onClose = () => {
    setActionData({ type: 'add', visible: false, rowData: {} });
  };

  // 树弹窗关闭
  const handleCancel = () => {
    setTreeVisible(false);
  };

  // 查询方法
  const handleSearch = (text) => {
    setSearchParams({ text });
  };

  // 转移确定
  const transferConfirm = async () => {
    setZyBtnLoading(true);
    let res = await knowledgetransfer({
      knowledgeIds: [currentRowData.id],
      targetCategoryId: selectTreeNodeId,
    });
    setZyBtnLoading(false);
    if (res.code != 200) return;
    message.success('转移成功');
    setZyVisible(false);
    handleSearch();
  };

  // 转移取消
  const transferCancel = async () => {
    setZyVisible(false);
  };

  // 提交
  const handleSubmit = async (callback) => {
    faqRef.current.getData(async (val) => {
      console.log('val :', val);
      let res = null;
      setBtnLoading(true);
      if (actionData.type == 'add') {
        res = await knowledgecreate(val);
      } else {
        res = await knowledgeupdate({
          ...val,
          id: actionData.rowData.id,
        });
      }
      setBtnLoading(false);
      if (res.code != 200) return;
      if (!callback) {
        message.success('提交成功');
        onClose();
        handleSearch();
      } else {
        callback(res.data);
      }
    });
  };

  // 提交发布
  const handlePublish = () => {
    handleSubmit(async (knowledgeId) => {
      let res = await knowledgepublish({ knowledgeId: actionData.rowData.id || knowledgeId });
      if (res.code != 200) return;
      message.success('提交发布成功');
      onClose();
      handleSearch();
    });
  };

  // 类目搜索
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const onSearch = (value) => {
    setSearchValue(value);
    // 获取匹配的节点路径
    const expandedKeysSet = new Set();
    const getExpandedKeys = (data, value) => {
      data.forEach((item) => {
        if (item.title.toLowerCase().includes(value.toLowerCase())) {
          expandedKeysSet.add(item.key);
        }
        if (item.children) {
          getExpandedKeys(item.children, value);
        }
      });
    };

    getExpandedKeys(treeData, value);
    setExpandedKeys([...expandedKeysSet]);
    setAutoExpandParent(true);
  };

  // 展开/收起节点时的回调
  const onExpand = (keys) => {
    setExpandedKeys(keys);
    setAutoExpandParent(false);
  };
  return (
    <>
      <Radio.Group onChange={handleModeChange} value={env} style={{ marginBottom: 8 }}>
        <Radio.Button value="test">测试环境</Radio.Button>
        <Radio.Button value="prod">正式环境</Radio.Button>
      </Radio.Group>
      <div className={css.searchBox}>
        <Input
          addonBefore={selectBefore}
          placeholder="请搜索"
          style={{ width: 420 }}
          allowClear
          onChange={(e) => {
            setSearchInputValue(e.target.value);
          }}
        />
        <Space>
          <Button type="primary" onClick={() => handleSearch(searchInputValue)}>
            查询
          </Button>
          {env == 'test' && (
            <Button
              type="primary"
              onClick={() => {
                setActionData({ type: 'add', visible: true, rowData: { categoryId: selectedKeys[0] } });
                faqRef.current && faqRef.current.formReset();
              }}
            >
              新增FAQ
            </Button>
          )}
        </Space>
      </div>
      <Row gutter={20}>
        <Col span={5}>
          <div className={css.treeTitle}>
            <div>FAQ类目</div>
            <Space>
              {env == 'test' && (
                <SettingOutlined
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    setTreeVisible(true);
                  }}
                />
              )}
              <ReloadOutlined
                style={{ cursor: 'pointer' }}
                onClick={() => {
                  getTreeData();
                }}
              />
            </Space>
          </div>
          <div className={css.treeBox} ref={treeContainerRef} style={{ height: `${treeHeight}px` }}>
            <Search
              placeholder="请搜索"
              allowClear
              onSearch={onSearch}
              style={{ width: '100%', marginBottom: '10px' }}
            />
            <Spin spinning={treeLoading}>
              <Tree
                height={treeHeight - 60}
                onSelect={onSelect}
                selectedKeys={selectedKeys}
                treeData={treeData}
                expandedKeys={expandedKeys}
                autoExpandParent={autoExpandParent}
                onExpand={onExpand}
                filterTreeNode={(node) =>
                  node.title.toLowerCase().includes(searchValue.toLowerCase())
                }
              />
            </Spin>
          </div>
        </Col>
        <Col span={19}>
          <Table
            loading={loading}
            columns={columns}
            dataSource={dataSource}
            pagination={{
              onChange: (pageNum, pageSize) => {
                setPageConfig({ ...pageConfig, pageNum, pageSize });
              },
              showSizeChanger: true,
              pageSize: pageConfig.pageSize,
              current: pageConfig.pageNum,
              total,
            }}
          />
        </Col>
      </Row>
      <Drawer
        title="新增FAQ"
        width={'80%'}
        onClose={onClose}
        open={actionData.visible}
        bodyStyle={{ paddingBottom: 52 }}
      >
        <FaqAdd treeData={treeData} ref={faqRef} actionData={actionData} />
        <Space className="fixed-footer">
          <Button onClick={onClose}>取消</Button>
          {actionData.type != 'view' && (
            <Button type="primary" ghost loading={btnLoading} onClick={handlePublish}>
              提交发布
            </Button>
          )}
          {actionData.type != 'view' && (
            <Button loading={btnLoading} type="primary" onClick={() => handleSubmit()}>
              提交
            </Button>
          )}
        </Space>
      </Drawer>

      <Modal width={500} title="类目管理" open={treeVisible} onCancel={handleCancel} footer={null}>
        <Category data={treeData} setUpdateTreeData={getTreeData} />
      </Modal>

      <Modal
        width={500}
        title="生效配置"
        open={settingVisible}
        onOk={async () => {
          console.log('settingValue :', settingValue);
          let obj = {
            id: currentId,
            effectiveType: settingValue,
          };
          if (settingValue == '01') {
            obj.effectiveStartTime = rangePickerValue[0].format('YYYY-MM-DD');
            obj.effectiveEndTime = rangePickerValue[1].format('YYYY-MM-DD');
          }
          let res = await knowledgeEffectiveSet(obj);
          if (res.code != 200) return;
          message.success('操作成功');
          setSettingVisible(false);
          handleSearch();
        }}
        onCancel={() => {
          setSettingVisible(false);
        }}
      >
        <Select
          placeholder="请选择生效类型"
          style={{ width: '100%' }}
          onChange={(val) => {
            setSettingValue(val);
          }}
          value={settingValue}
          options={[
            { value: '00', label: '永久生效' },
            { value: '01', label: '选择时间' },
            { value: '10', label: '不生效' },
          ]}
        />

        {settingValue == '01' && (
          <div style={{ marginTop: '10px' }}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(val) => {
                console.log('val :', val);
                setRangePickerValue(val);
              }}
              value={rangePickerValue}
            />
          </div>
        )}
      </Modal>

      <Modal
        width={800}
        title="转移"
        open={zyVisible}
        onOk={transferConfirm}
        onCancel={transferCancel}
        footer={[
          <Button key="back" onClick={transferCancel}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={zybtnLoading} onClick={transferConfirm}>
            确定
          </Button>,
        ]}
      >
        <Tree
          height={300}
          onSelect={(a, info) => {
            setSelectTreeNodeId(info.node.id);
          }}
          treeData={treeData}
        />
      </Modal>
    </>
  );
};

export default Faq;
