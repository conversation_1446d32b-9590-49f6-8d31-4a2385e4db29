/*
 * @Author: 焦质晔
 * @Date: 2021-02-12 12:43:43
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-16 15:10:34
 */
import config from '../../config/app.conf';
import type { ComponentSize, Language, ThemeType } from '@/utils/types';

type IConfig = {
  system: string;
  code: string;
  isMainApp: boolean;
  microName: string;
  powerByMicro: boolean;
  baseUrl: string;
  baseRoute: string;
  lang: Language;
  size: ComponentSize;
  themeType: ThemeType;
  microType: 'iframe' | 'micro-app' | '';
  postOrigin: string;
  sideWidth: number[];
  maxCacheNum: number;
  showBreadcrumb: boolean;
  showStarNav: boolean;
  showCommonNav: boolean;
  showScreenFull: boolean;
  showCustomTheme: boolean;
  showLangSelect: boolean;
  showSizeSelect: boolean;
  showHelperDoc: boolean;
  showNotification: boolean;
  openWatermark: boolean;
  openBuryPoint: boolean;
};

export default {
  system: config.name, // 应用名
  code: config.code, // 应用 code
  isMainApp: config.name === 'app', // 是否是主应用
  microName: window.__MICRO_APP_NAME__ || window.name || '', // 微应用名
  powerByMicro: window.__MICRO_APP_ENVIRONMENT__, // 是否微应用运行时
  baseUrl: /^https?:\/\//.test(process.env.PUBLIC_PATH!)
    ? process.env.PUBLIC_PATH
    : (window.__MICRO_APP_PUBLIC_PATH__ || (window.location.origin + process.env.PUBLIC_PATH)), // prettier-ignore
  baseRoute: '', // 二级目录
  lang: 'zh-cn', // 语言
  size: 'middle', // 尺寸
  themeType: 'light', // 主题模式
  microType: '', // 微前端模式
  postOrigin: '*', // postMessage 接收源
  sideWidth: [200, 60], // 侧栏导航宽度
  maxCacheNum: 20, // 路由组件最大缓存数量
  showBreadcrumb: false, // 是否显示面包屑
  showStarNav: false, // 是否显示收藏导航
  showCommonNav: false, // 是否显示常用导航
  showScreenFull: true, // 是否显示全屏按钮
  showCustomTheme: true, // 是否显示自定义主题
  showLangSelect: true, // 是否显示多语言
  showSizeSelect: true, // 是否显示尺寸选择
  showHelperDoc: false, // 是否显示帮助
  showNotification: true, // 是否显示通知
  openWatermark: false, // 是否开启水印功能
  openBuryPoint: false, // 是否开启埋点
} as IConfig;
