.searchBox {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.treeTitle {
  border-radius: 4px 4px 0px 0px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 16px;
  justify-content: space-between;
  background: linear-gradient(90deg, #edeefa 0%, rgba(255, 255, 255, 0) 100%);
  border: 1px solid #eaeaee;
  border-bottom: 0;
}
.treeBox {
  padding: 10px;
  border: 1px solid #eaeaee;
}
