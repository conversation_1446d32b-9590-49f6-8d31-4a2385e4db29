import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Input, Button, Drawer, Space, Spin, message } from 'antd';
import Card from './components/Card';
import RobotAdd from './components/RobotAdd';
import { robotlist, robotsaveOrUpdate, robotdetail } from '@/modules/dataoperations/api/robot';
import css from './index.module.less';


const { Search } = Input;

const RobotList = ({setCurrentDetail}) => {
  const robotAddRef = useRef(null);
  const [detail, setDetail] = useState({});
  const [actionData, setActionData] = useState({ type: 'add', visible: false });
  const [pageConfig, setPageConfig] = useState({ pageSize: 99999, pageNum: 1 });
  const [loading, setLoading] = useState(false);
  const [btnloading, setBtnLoading] = useState(false);
  const [list, setList] = useState([]);
  const [h, setH] = useState(300);
  const [robotAddMode, setRobotAddMode] = useState('test');

  const getData = useCallback(
    async (robotName) => {
      setLoading(true);
      let res = await robotlist({ ...pageConfig, robotName });
      setLoading(false);
      if (res.code != 200) return;
      setList(res.data.records);
    },
    [pageConfig]
  );

  useEffect(() => {
    let h = document.documentElement.clientHeight;
    console.log('h :', h);
    setH(h - 221);
    getData();
  }, [getData]);

  const onSearch = (val) => {
    getData(val);
  };

  // 关闭抽屉
  const onClose = () => {
    setActionData({ type: '', visible: false });
  };

  // 获取详情
  const getDetail = async (item, env) => {
    let res = await robotdetail({ id: item.id, env });
    if (res.code != 200) return;
    setDetail(res.data);
    setCurrentDetail(res.data);
  };

  return (
    <>
      <div className={css.searchBox}>
        <Search
          placeholder="搜索机器人名称"
          allowClear
          onSearch={onSearch}
          style={{ width: 262 }}
        />

        <Button
          type="primary"
          onClick={() => {
            setActionData({ type: 'add', visible: true });
            setDetail({});
            setCurrentDetail({});
          }}
        >
          新增机器人
        </Button>
      </div>
      <Spin spinning={loading}>
        <div className={css.cardBox} style={{ height: h + 'px' }}>
          {list.map((item, index) => {
            return (
              <Card
                key={index}
                data={item}
                handleSearch={getData}
                updateFuc={async (val) => {
                  setActionData({ type: val || 'edit', visible: true });
                  getDetail(item, val == 'view' ? 'prod' : 'test');
                }}
              />
            );
          })}
        </div>
      </Spin>

      <Drawer
        title={`${actionData.type == 'add' ? '新增' : actionData.type == 'edit' ? '编辑' : '查看'
          }机器人`}
        width={'80%'}
        onClose={onClose}
        open={actionData.visible}
        bodyStyle={{ paddingBottom: 52 }}
      >
        <RobotAdd
          ref={robotAddRef}
          detail={detail}
          actionData={actionData}
          setChangeMode={(val, detailData) => {
            setRobotAddMode(val);
            getDetail(detailData, val);
          }}
        />
        <Space className="fixed-footer">
          <Button onClick={onClose}>取消</Button>
          {actionData.type == 'view'
            ? ''
            : robotAddMode == 'test' && (
              <Button
                loading={btnloading}
                type="primary"
                onClick={() => {
                  robotAddRef.current.getData(async (val) => {
                    console.log('val :', val);
                    setBtnLoading(true);
                    let res = await robotsaveOrUpdate(val);
                    setBtnLoading(false);
                    if (res.code != 200) return;
                    message.success('操作成功');
                    onClose();
                    getData();
                  });
                }}
              >
                提交
              </Button>
            )}
        </Space>
       
      </Drawer>
      
    </>
  );
};

export default RobotList;
