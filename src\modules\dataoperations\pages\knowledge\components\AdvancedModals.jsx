import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Form,
  Table,
  Space,
  Button,
  Input,
  Select,
  Alert,
  Progress,
  InputNumber,
  message
} from 'antd';
import {
  SearchOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import {
  documentPage,
  bindDocuments,
  initializeKnowledge,
  getInitializeStatus
} from '../../../api/knowledge';

const { Option } = Select;
const { Search } = Input;

/**
 * 绑定文档对话框
 * <AUTHOR>
 */
export const BindDocumentsModal = ({
  visible,
  onCancel,
  onSuccess,
  selectedKnowledge,
  categories
}) => {
  const [loading, setLoading] = useState(false);
  const [availableDocuments, setAvailableDocuments] = useState([]);
  const [selectedDocuments, setSelectedDocuments] = useState([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  /**
   * 加载可用文档
   * <AUTHOR>
   */
  const loadAvailableDocuments = async () => {
    setLoading(true);
    try {
      const params = {
        current: pagination.current,
        size: pagination.pageSize
      };
      
      if (searchKeyword) {
        params.fileName = searchKeyword;
      }
      if (categoryFilter) {
        params.categoryId = categoryFilter;
      }

      const response = await documentPage(params);
      if (response.code === 200) {
        setAvailableDocuments(response.data?.records || []);
        setPagination(prev => ({
          ...prev,
          total: response.data?.total || 0
        }));
      }
    } catch (error) {
      message.error('加载文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible) {
      loadAvailableDocuments();
    }
  }, [visible, pagination.current, pagination.pageSize, searchKeyword, categoryFilter]);

  /**
   * 选择文档变化处理
   * @param {Array} selectedRowKeys - 选中的行key
   * @param {Array} selectedRows - 选中的行数据
   * <AUTHOR>
   */
  const handleSelectionChange = (selectedRowKeys, selectedRows) => {
    setSelectedDocuments(selectedRows);
  };

  /**
   * 绑定文档
   * <AUTHOR>
   */
  const handleBind = async () => {
    if (selectedDocuments.length === 0) {
      message.warning('请选择要绑定的文档');
      return;
    }

    setLoading(true);
    try {
      await bindDocuments({
        knowledgeId: selectedKnowledge.id,
        documentIds: selectedDocuments.map(doc => doc.id)
      });
      message.success('绑定文档成功');
      onSuccess();
      onCancel();
    } catch (error) {
      message.error(error.message || '绑定文档失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 格式化文件大小
   * @param {number} size - 文件大小（字节）
   * @returns {string} 格式化后的大小
   * <AUTHOR>
   */
  const formatFileSize = (size) => {
    if (!size) return '0 B';
    const units = ['B', 'KB', 'MB', 'GB'];
    let index = 0;
    while (size >= 1024 && index < units.length - 1) {
      size /= 1024;
      index++;
    }
    return `${size.toFixed(2)} ${units[index]}`;
  };

  const columns = [
    {
      title: '文件名',
      dataIndex: 'fileName',
      key: 'fileName',
      render: (text) => (
        <Space>
          <FileTextOutlined />
          <span>{text}</span>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'fileType',
      key: 'fileType',
      width: 80,
      render: (text) => text?.toUpperCase()
    },
    {
      title: '大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 100,
      render: (size) => formatFileSize(size)
    },
    {
      title: '类目',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 120
    },
    {
      title: '上传时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (time) => time ? new Date(time).toLocaleString() : ''
    }
  ];

  const rowSelection = {
    selectedRowKeys: selectedDocuments.map(doc => doc.id),
    onChange: handleSelectionChange,
  };

  return (
    <Modal
      title="绑定文档"
      open={visible}
      onCancel={onCancel}
      onOk={handleBind}
      confirmLoading={loading}
      width={800}
    >
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Search
            placeholder="搜索文档名称"
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            onSearch={loadAvailableDocuments}
            style={{ width: 300 }}
            allowClear
          />
          <Select
            placeholder="选择类目"
            value={categoryFilter}
            onChange={setCategoryFilter}
            style={{ width: 200 }}
            allowClear
          >
            {categories.map(category => (
              <Option key={category.key} value={category.key}>
                {category.title}
              </Option>
            ))}
          </Select>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={availableDocuments}
        rowKey="id"
        rowSelection={rowSelection}
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          onChange: (current, pageSize) => {
            setPagination(prev => ({ ...prev, current, pageSize }));
          }
        }}
      />
    </Modal>
  );
};

/**
 * 初始化知识库对话框
 * <AUTHOR>
 */
export const InitializeModal = ({
  visible,
  onCancel,
  onSuccess,
  selectedKnowledge
}) => {
  const [loading, setLoading] = useState(false);
  const [parallelCount, setParallelCount] = useState(5);
  const [progress, setProgress] = useState({
    visible: false,
    taskId: '',
    status: '',
    totalDocuments: 0,
    successCount: 0,
    failedCount: 0,
    errorMessage: ''
  });
  
  const timerRef = useRef(null);

  /**
   * 开始初始化
   * <AUTHOR>
   */
  const handleInitialize = async () => {
    if (!parallelCount || parallelCount < 1 || parallelCount > 20) {
      message.warning('请输入有效的并行处理数量（1-20）');
      return;
    }

    setLoading(true);
    try {
      const response = await initializeKnowledge({
        knowledgeId: selectedKnowledge.id,
        parallelCount
      });

      if (response.code === 200) {
        setProgress({
          visible: true,
          taskId: response.data.taskId,
          status: response.data.status,
          totalDocuments: response.data.totalDocuments,
          successCount: response.data.successCount,
          failedCount: response.data.failedCount,
          errorMessage: ''
        });

        message.success('知识库初始化已启动');
        startPolling(response.data.taskId);
      }
    } catch (error) {
      message.error(error.message || '启动初始化失败');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 开始轮询状态
   * @param {string} taskId - 任务ID
   * <AUTHOR>
   */
  const startPolling = (taskId) => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    timerRef.current = setInterval(async () => {
      try {
        const response = await getInitializeStatus(taskId);
        if (response.code === 200) {
          setProgress(prev => ({
            ...prev,
            status: response.data.status,
            successCount: response.data.successCount,
            failedCount: response.data.failedCount,
            errorMessage: response.data.errorMessage || ''
          }));

          if (response.data.status === 'COMPLETED' || response.data.status === 'FAILED') {
            clearInterval(timerRef.current);
            timerRef.current = null;

            if (response.data.status === 'COMPLETED') {
              message.success('知识库初始化完成');
              onSuccess();
            } else {
              message.error('知识库初始化失败');
            }
          }
        }
      } catch (error) {
        console.error('获取初始化状态失败:', error);
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }, 2000);
  };

  /**
   * 取消初始化
   * <AUTHOR>
   */
  const handleCancel = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setProgress({
      visible: false,
      taskId: '',
      status: '',
      totalDocuments: 0,
      successCount: 0,
      failedCount: 0,
      errorMessage: ''
    });
    onCancel();
  };

  /**
   * 获取状态文本
   * @param {string} status - 状态
   * @returns {string} 状态文本
   * <AUTHOR>
   */
  const getStatusText = (status) => {
    const statusMap = {
      'STARTED': '已启动',
      'PROCESSING': '处理中',
      'COMPLETED': '已完成',
      'FAILED': '失败'
    };
    return statusMap[status] || status;
  };

  /**
   * 获取进度百分比
   * @returns {number} 进度百分比
   * <AUTHOR>
   */
  const getProgressPercentage = () => {
    if (progress.totalDocuments === 0) return 0;
    const processed = progress.successCount + progress.failedCount;
    return Math.round((processed / progress.totalDocuments) * 100);
  };

  return (
    <Modal
      title="初始化知识库"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={loading || (progress.visible && progress.status === 'PROCESSING')}
          disabled={progress.visible}
          onClick={handleInitialize}
        >
          {progress.visible ? '初始化中...' : '开始初始化'}
        </Button>
      ]}
      width={500}
    >
      <Alert
        message="初始化说明"
        description={
          <div>
            <p>初始化将对知识库绑定的所有文档进行以下操作：</p>
            <ul style={{ margin: '10px 0', paddingLeft: '20px' }}>
              <li>文档切分处理</li>
              <li>确认绑定关系</li>
              <li>向量化文档片段</li>
            </ul>
            <p style={{ color: '#E6A23C' }}>注意：此操作可能需要较长时间，请耐心等待。</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 20 }}
      />

      <Form layout="vertical">
        <Form.Item label="知识库名称">
          <Input value={selectedKnowledge?.name} disabled />
        </Form.Item>
        
        <Form.Item label="绑定文档数量">
          <Input value={`${selectedKnowledge?.documentCount || 0}个`} disabled />
        </Form.Item>
        
        <Form.Item
          label="并行处理数量"
          help="建议值：1-5（数量越大处理越快，但会占用更多系统资源）"
        >
          <InputNumber
            min={1}
            max={20}
            value={parallelCount}
            onChange={setParallelCount}
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>

      {progress.visible && (
        <div style={{ marginTop: 20 }}>
          <h4>处理进度</h4>
          <div style={{ marginBottom: 10 }}>
            <Space>
              <span>任务ID: {progress.taskId}</span>
              <span>状态: {getStatusText(progress.status)}</span>
            </Space>
          </div>
          <div style={{ marginBottom: 10 }}>
            <Space>
              <span>总文档: {progress.totalDocuments}个</span>
              <span>成功: {progress.successCount}个</span>
              <span>失败: {progress.failedCount}个</span>
            </Space>
          </div>
          <Progress
            percent={getProgressPercentage()}
            status={progress.status === 'FAILED' ? 'exception' : progress.status === 'COMPLETED' ? 'success' : 'active'}
            strokeWidth={20}
          />
          {progress.errorMessage && (
            <Alert
              message={progress.errorMessage}
              type="error"
              style={{ marginTop: 10 }}
            />
          )}
        </div>
      )}
    </Modal>
  );
};
