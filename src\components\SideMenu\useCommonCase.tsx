/*
 * @Author: 焦质晔
 * @Date: 2024-04-13 14:57:48
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-04-15 13:17:57
 */
import React from 'react';
import { useSelector } from '@/store';
import { getSystem, sleep } from '@/utils';
import { deepGetPath } from './index';
import { getUserInfo } from '@/utils/cookies';
import { setCommonMenu } from '@/api/application';
import config from '@/config';

import type { AppState, ISideMenu } from '@/store/reducers/app';

export const useCommonCase = () => {
  const { menuList } = useSelector((state: AppState) => state.app);

  const createCrumb = (path: string) => {
    const target = menuList.find((x) => x.system === getSystem());
    if (!target) {
      return '';
    }
    const _menus = config.isMainApp ? [target] : target.children || [];
    return deepGetPath(_menus, 'title', path)?.join('/') || '';
  };

  const createCommonCase = async (item: ISideMenu, isValidate?: boolean) => {
    if (!config.showCommonNav || process.env.MOCK_DATA === 'true') return;
    if (!item.key) return;
    await sleep(0); // 需要延迟，否则 system 的值可能不正确
    const newData = !isValidate
      ? {
          id: item.id,
          key: item.key.replace(`/${getSystem()}`, ''),
          title: item.title,
          system: getSystem(),
          crumb: createCrumb(item.key),
        }
      : item;
    try {
      const res = await setCommonMenu({
        userId: getUserInfo().id,
        value: newData,
      });
    } catch (err) {
      // ...
    }
  };

  return {
    createCrumb,
    createCommonCase,
  };
};
