/*
 * @Author: 焦质晔
 * @Date: 2022-09-18 14:00:04
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-01-04 14:24:53
 */
.app-header {
  .nav-list {
    display: flex;
    flex: auto;
    width: 0;
    .wrap {
      position: relative;
      display: flex;
      flex: auto;
      align-self: stretch;
      white-space: nowrap;
      overflow: hidden;
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        width: 10px;
        z-index: 1;
        opacity: 0;
        transition: opacity 0.3s;
        pointer-events: none;
      }
      &::before {
        left: 0;
      }
      &::after {
        right: 0;
      }
      &-ping-left::before {
        opacity: 1;
        box-shadow: inset 10px 0 8px -8px #00000014;
      }
      &-ping-right::after {
        opacity: 1;
        box-shadow: inset -10px 0 8px -8px #00000014;
      }
      & > ul {
        position: relative;
        display: flex;
        white-space: nowrap;
        transition: transform 0.3s;
        li {
          display: flex;
          align-items: center;
          flex-shrink: 0;
          margin: 0;
          position: relative;
          a {
            display: inline-flex;
            align-items: center;
            padding: 0 20px;
            height: 100%;
            color: @textColor;
            font-size: @textSize;
            transition: all 0.3s ease;
            &:hover,
            &.actived,
            &.focused {
              color: @primaryColor;
              text-shadow: 0 0 1px currentColor;
            }
            &:hover {
              .icon {
                color: @primaryColor;
              }
            }
          }
          .icon {
            position: absolute;
            right: 5px;
            margin-top: 2px;
            font-size: @textSizeSecondary;
            color: rgba(0, 0, 0, 0.65);
            transform: scaleX(0.9);
          }
          .nav-group {
            position: absolute;
            right: 12px;
            top: 14px;
            width: 0;
            height: 0;
            pointer-events: none;
            &::before {
              content: '';
              position: absolute;
              top: 0;
              right: 0;
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 4px;
              border-color: #3682f5 #3682f5 transparent transparent;
            }
            pointer-events: none;
          }
          .nav-tag {
            position: absolute;
            right: -8px;
            top: 3px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 12px;
            padding: 0 2px;
            background: #fff;
            line-height: 1;
            border: 1px solid #f64041;
            border-radius: 6px;
            border-bottom-left-radius: 0;
            box-shadow: 2px 2px 4px 0 rgba(55, 99, 170, 0.3);
            pointer-events: none;
            span {
              color: #f64041;
              font-weight: 700;
              font-size: @textSizeSecondary;
              transform: scale(0.8333);
            }
          }
        }
        .bar-dot {
          position: absolute;
          width: 0;
          height: 3px;
          border-radius: 1px;
          background-color: @primaryColor;
          bottom: 0;
          transition: left 0.3s ease;
        }
      }
    }
    .operations {
      .more {
        height: 100%;
        display: flex;
        align-items: center;
        padding: 0 15px;
        background: transparent;
        cursor: pointer;
      }
      &-hidden {
        position: absolute;
        visibility: hidden;
        pointer-events: none;
      }
    }
  }
  // size
  &__sm {
    .nav-list .wrap ul li a {
      font-size: @textSize - 1px;
    }
  }
}

.app-nav-list__popper {
  .ant-dropdown-menu {
    max-height: calc(100vh - 60px);
    overflow-y: auto;
    &-item {
      .icon {
        margin-left: 4px;
        font-size: @textSizeSecondary;
        color: rgba(0, 0, 0, 0.65);
        transform: scaleX(0.9);
      }
    }
  }
}

.nav-list__popper {
  left: 0 !important;
  z-index: 9999 !important;
  padding: 0 !important;
  width: 100%;
  min-width: @mobileSize;
  min-height: 60vh;
  background: #f0f1f2;
  box-shadow: 0px 8px 20px 0px #f2f2f2;
  &::before {
    display: none;
  }
  .close-btn {
    position: absolute;
    right: 15px;
    top: 10px;
    padding: 4px;
    color: @textColorTertiary;
    line-height: 1;
    border-radius: @borderRadius + 2px;
    transition: all 0.3s ease;
    cursor: pointer;
    &:hover {
      color: @primaryColor;
      background-color: rgba(0, 0, 0, 0.05);
    }
  }
}
