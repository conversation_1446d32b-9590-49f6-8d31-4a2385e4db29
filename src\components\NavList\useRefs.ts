/*
 * @Author: 焦质晔
 * @Date: 2023-11-18 18:16:32
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-19 01:22:08
 */
import * as React from 'react';

export const useRefs = <RefType>(): [
  (key: React.Key) => React.RefObject<RefType>,
  (key: React.Key) => void
] => {
  const cacheRefs = React.useRef(new Map<React.Key, React.RefObject<RefType>>());

  const getRef = (key: React.Key) => {
    if (!cacheRefs.current.has(key)) {
      cacheRefs.current.set(key, React.createRef<RefType>());
    }
    return cacheRefs.current.get(key)!;
  };

  const removeRef = (key: React.Key) => {
    cacheRefs.current.delete(key);
  };

  return [getRef, removeRef];
};
