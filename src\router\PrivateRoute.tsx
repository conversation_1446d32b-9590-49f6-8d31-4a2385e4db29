/*
 * @Author: 焦质晔
 * @Date: 2021-07-12 10:12:28
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-12-11 21:43:40
 */
import React from 'react';
import { Redirect } from 'react-router-dom';
import { connect } from '@/store/connect';
import { getPathName } from '@/utils';

import type { AppState, ISideMenu } from '@/store/reducers/app';
import type { IRoute } from '@/utils/types';

import Loading from '@/pages/loading';

type IProps = {
  route: IRoute;
  whiteList: string[];
  whiteAuth: string[];
  [`render-props`]: () => React.ReactNode;
};

class PrivateRoute extends React.Component<IProps & { flattenMenus: ISideMenu[] }> {
  isMatch = (arr: string[], path: string) => {
    return path === '/' || arr.some((x) => path.startsWith(x));
  };

  isAuth = (path: string): boolean => {
    return (
      this.props.route.meta?.noAuth ||
      this.props.flattenMenus.some((x) => getPathName(x.key) === path)
    );
  };

  render() {
    const { route, whiteList, whiteAuth, flattenMenus } = this.props;
    return this.props[`render-props`]();

    /*if (this.isMatch([...whiteList, ...whiteAuth], route.path) || this.isAuth(route.path)) {
      return this.props[`render-props`]();
    }

    if (!flattenMenus.length) {
      return <Loading />;
    }

    return <Redirect to={'/404'} />;*/
  }
}

export default connect(
  (state: AppState) => ({
    flattenMenus: state.app.flattenMenus,
  }),
  {}
)(PrivateRoute);
