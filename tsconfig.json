{"compilerOptions": {"target": "ESNext", "module": "ESNext", "strict": false, "declaration": true, "skipLibCheck": true, "importHelpers": true, "noImplicitAny": false, "strictNullChecks": true, "experimentalDecorators": true, "moduleResolution": "node", "esModuleInterop": true, "jsx": "preserve", "allowJs": true, "sourceMap": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "outDir": "/", "paths": {"@/*": ["src/*"]}, "lib": ["ESNext", "DOM", "DOM.Iterable"]}, "include": ["./src"], "exclude": ["node_modules", "dist", "**/*.js"]}