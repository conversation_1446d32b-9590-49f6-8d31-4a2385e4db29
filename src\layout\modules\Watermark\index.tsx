/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-08-04 08:55:22
 */
import React from 'react';
import dayjs from 'dayjs';
import { useLocale } from '@/hooks';

import { QmWatermark } from '@jiaozhiye/qm-design-react';

import './index.less';

const Watermark: React.FC = () => {
  const { t } = useLocale();

  return (
    <QmWatermark
      className={`app-watermark`}
      content={[t('app.global.title'), dayjs().format('YYYY-MM-DD')]}
    />
  );
};

export default Watermark;
