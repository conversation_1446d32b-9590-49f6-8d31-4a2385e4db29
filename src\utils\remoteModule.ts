/*
 * @Author: 焦质晔
 * @Date: 2025-04-22 19:32:39
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-23 12:58:25
 */
import React from 'react';

declare const __webpack_init_sharing__: (scope: string) => Promise<void>;
declare const __webpack_share_scopes__: { default: unknown };
declare const __REMOTE_CONFIG__: Record<string, string>;

function getRemoteUrlFromWebpack(scope: string): string | null {
  const remotes = __REMOTE_CONFIG__ || {};
  const config = remotes[scope];
  if (config) {
    const [, url] = config.split('@');
    return url || null;
  }
  return null;
}

const remoteLoadingMap = new Map<string, Promise<void>>();

async function loadRemoteComponent(scope: string, module: string, noCache?: boolean): Promise<any> {
  await __webpack_init_sharing__('default');

  const remoteUrl = getRemoteUrlFromWebpack(scope);
  if (!remoteUrl) {
    throw new Error(`未找到 ${scope} 对应的 remoteEntry 地址`);
  }

  if (!window[scope]) {
    if (!remoteLoadingMap.has(scope)) {
      const _promise = new Promise<void>((resolve, reject) => {
        const script = document.createElement('script');
        script.src = remoteUrl + (noCache ? `?_t=${+new Date().getTime()}` : '');
        script.async = true;
        script.onload = () => resolve();
        script.onerror = () => {
          remoteLoadingMap.delete(scope);
          reject(new Error(`远程模块 ${scope} 加载失败: ${remoteUrl}`));
        };
        document.head.appendChild(script);
      });
      remoteLoadingMap.set(scope, _promise);
    }
    await remoteLoadingMap.get(scope);
  }

  const container = window[scope];
  if (!container) {
    throw new Error(`远程容器 ${scope} 未定义`);
  }

  if (!container.__initialized) {
    await container.init(__webpack_share_scopes__.default);
    container.__initialized = true;
  }

  const factory = await container.get(module);
  return factory();
}

export function importRemoteModule<T = any>(
  remoteId: string,
  option?: {
    fallback?: React.ReactNode;
    noCache?: boolean;
  }
): React.LazyExoticComponent<React.ComponentType<T>> {
  const [scope, rawModule] = remoteId.split('/');
  const module = rawModule.startsWith('./') ? rawModule : `./${rawModule}`;
  return React.lazy(async () => {
    try {
      const factory = await loadRemoteComponent(scope, module, option?.noCache);
      return { default: factory.default };
    } catch (err) {
      console.error(err);
      return { default: () => option?.fallback || null };
    }
  });
}
