import React, { useEffect, useState } from 'react'
import { Tabs } from '@jiaozhiye/qm-design-react';
import AuditDetails from './auditDetails'
import Trend from './trend'
import { getSystemInfo, getTaskRules } from '@/modules/dataoperations/api'
const Operations = () => {
    const [item, setItem] = useState([])
    const [taskTypeItem, setTaskTypeItem] = useState([])
    useEffect(() => {
        getSystemInfoFn()
        getTaskRulesFn()
    }, [])
    const getSystemInfoFn = async () => {
        const res = await getSystemInfo()
        if (res.code == 200) {
            setItem(res.data?.map(i => ({
                text: i.systemName,
                value: i.systemCode,
            })))
        }
    }
    const getTaskRulesFn = async () => {
        const res = await getTaskRules()
        if (res.code == 200) {
            setTaskTypeItem(res.data?.map(i => ({
                text: i.taskName,
                value: i.taskType,
            })))
        }
    }
    return (
        <div>
            <Tabs size='large' items={[
                {
                    key: '1',
                    label: `AI审核明细`,
                    children: <AuditDetails item={item} taskTypeItem={taskTypeItem} />
                },
                /*        {
                            key: '2',
                            label:`趋势`,
                            children:<Trend />
                        }*/
            ]} />
        </div>
    )
}

export default Operations
