.detailContainer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 60vh;
  overflow-y: auto;
  padding: 12px;
}

.detailItem {
  display: flex;
  font-size: 14px;
  line-height: 1.5715;

  .detailLabel {
    width: 80px;
    color: rgba(0, 0, 0, 0.45);
    flex-shrink: 0;
    text-align: right;
    margin-right: 16px;
  }

  .detailValue {
    color: rgba(0, 0, 0, 0.85);
    flex-grow: 1;
  }
}

.similarList {
  padding-left: 20px;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.answerHeader {
  display: flex;
  justify-content: space-between;
  background-color: #fafafa;
  padding: 8px 12px;
  border-radius: 4px 4px 0 0;
  border: 1px solid #f0f0f0;
  border-bottom: none;

  .answerPerspective {
    font-weight: 500;
  }
}

.answerBody {
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 0 0 4px 4px;
  white-space: pre-wrap;
  word-break: break-word;
}
