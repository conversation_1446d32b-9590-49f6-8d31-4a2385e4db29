/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:16:09
 */
import React from 'react';
import classNames from 'classnames';
import { Link } from 'react-router-dom';
import { useSelector, useDispatch } from '@/store';
import { createStarMenu } from '@/store/actions';
import { useLocale } from '@/hooks';

import type { AppState } from '@/store/reducers/app';

import { Menu } from '@jiaozhiye/qm-design-react';
import { StarFilled } from '@/icons';

import './index.less';

const StarNav: React.FC = () => {
  const { starMenus } = useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const { t } = useLocale();

  React.useEffect(() => {
    dispatch<any>(createStarMenu());
  }, []);

  const createMenuItems = () => {
    return starMenus.map((x) => ({
      key: x.key,
      label: (
        <Link to={x.key} target={x.target}>
          <span>{x.title}</span>
        </Link>
      ),
    }));
  };

  const items = [
    {
      key: 'star-nav',
      popupClassName: 'ant-submenu-popup-dark',
      icon: <StarFilled />,
      label: t('app.sidebar.starNav'),
      children: createMenuItems(),
    },
  ];

  return (
    <div className={classNames('app-star-nav')}>
      <Menu mode="inline" theme="dark" inlineIndent={20} items={items} />
    </div>
  );
};

export default StarNav;
