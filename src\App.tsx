/*
 * @Author: 焦质晔
 * @Date: 2019-11-23 14:12:19
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-03-19 14:54:33
 */
import React from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { renderRoutes } from '@/router';
import store from '@/store';
import routes from '@/router/config';
import config from '@/config';
import UseProvider from '@/config/use';

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <Router basename={config.baseRoute}>
        <UseProvider>{renderRoutes(routes)}</UseProvider>
      </Router>
    </Provider>
  );
};

export default App;
