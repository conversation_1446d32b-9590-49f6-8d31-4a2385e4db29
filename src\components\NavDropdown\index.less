/*
 * @Author: 焦质晔
 * @Date: 2023-04-11 17:18:41
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-28 15:08:01
 */
.nav-dropdown {
  display: flex;
  flex-direction: row;
  min-height: 60vh;
  max-height: 80vh;
  .box-left {
    width: 320px;
    background: #fff url(./assets/left-bg.jpg) right bottom no-repeat;
    background-size: 400px auto;
    .main {
      height: 100%;
      width: calc(100% + 5px);
      .topper {
        padding: 40px 10px 0 40px;
        .title {
          font-size: 20px;
          font-weight: 700;
          background: linear-gradient(to bottom right, @primaryColor 0%, #4cc5ff 100%);
          user-select: none;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .description {
          min-height: 52px;
          padding-top: @modulePadding;
          font-size: @textSize - 1px;
          color: @textColorTertiary;
        }
        .subapp-list {
          margin-top: 15px;
          ul {
            li {
              margin-bottom: 12px;
              font-size: @textSize + 1px;
              .icon {
                margin-top: 2px;
                font-size: @textSize - 2px;
                transition: margin 0.4s ease;
                visibility: hidden;
              }
              a {
                display: inline-flex;
                align-items: center;
                color: @textColor;
                &:hover,
                &.actived {
                  color: @primaryColor;
                  .icon {
                    margin-left: 5px;
                    visibility: visible;
                  }
                }
              }
              .description {
                min-height: 0;
                padding-top: 4px;
                font-size: @textSize - 1px;
                color: @textColorTertiary;
              }
            }
          }
        }
      }
    }
  }
  .box-right {
    display: flex;
    flex-direction: row;
    height: 100%;
    .main {
      flex: 1 0;
      margin: @moduleMargin 0;
      .outer {
        padding: 0 @modulePadding;
        column-count: 4;
        column-gap: 0;
      }
      .wrap {
        padding: 10px 0 10px 20px;
        break-inside: avoid;
        .first {
          padding-bottom: 8px;
          border-bottom: 1px solid @borderColor;
          color: @primaryColor;
          font-weight: 700;
          &.is-case {
            border-bottom-color: transparent;
            a {
              color: @textColor;
              &:hover {
                color: @primaryColor;
                .icon {
                  margin-left: 6px;
                }
              }
            }
            .icon {
              margin-left: 2px;
              font-size: @textSizeSecondary;
              transition: margin 0.4s ease;
            }
          }
        }
        ul {
          padding-top: 6px;
          li {
            line-height: 36px;
            & > a {
              color: @textColor;
              &:hover,
              &.actived {
                color: @primaryColor;
              }
            }
            dl {
              margin-right: 10%;
              padding: 5px 10px 5px 20px;
              background-color: #e4e4e4;
              border-radius: @borderRadius + 2px;
              dd,
              p {
                line-height: 32px;
                font-size: @textSize - 1px;
                color: #636e95;
                & > a {
                  color: #636e95;
                  &:hover,
                  &.actived {
                    color: @primaryColor;
                  }
                }
                .icon {
                  margin-left: -14px;
                  margin-right: 2px;
                  font-size: @textSizeSecondary;
                }
              }
            }
          }
        }
      }
    }
    .poster {
      width: 320px;
      margin: 40px;
      margin-left: 20px;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        right: 14px;
        top: -16px;
        width: 48px;
        height: 48px;
        background: url(./assets/rank-icon.png) no-repeat;
        background-size: contain;
      }
      .inner {
        max-height: 100%;
        background-image: linear-gradient(0deg, #ffffff 0%, #f3f5f8 100%);
        border: 2px solid #fff;
        box-shadow: 8px 8px 20px 0 rgba(55, 99, 170, 0.03), -8px -8px 20px 0 #fff;
        border-radius: @borderRadius + 2px;
        .title {
          padding: 10px 15px;
          font-size: @textSize;
        }
        ul {
          padding: 5px 15px;
          li {
            height: 44px;
            margin-bottom: 12px;
            background: #fff;
            border: 2px solid #fff;
            border-radius: @borderRadius + 2px;
            box-shadow: 8px 8px 20px 0 rgba(55, 99, 170, 0.1), -8px -8px 20px 0 #fff,
              inset 0 4px 20px 0 rgba(255, 255, 255, 0.5);
            a {
              height: 100%;
              display: flex;
              align-items: center;
              padding: 0 12px;
              color: @textColor;
              font-size: @textSize - 1px;
              &:hover {
                color: @primaryColor;
              }
              .rank {
                margin-right: @moduleMargin;
                font-style: normal;
                font-family: Tahoma;
              }
            }
          }
        }
      }
    }
  }
}

@media screen and (max-width: 1200px) {
  .nav-dropdown {
    .poster {
      display: none;
    }
  }
}
