/*
 * @Author: 焦质晔
 * @Date: 2025-04-26 14:49:57
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-04-26 16:28:19
 */
import React from 'react';
import config from '@/config';

import type { ComponentSize, Nullable } from '@/utils/types';

export function useCompact(size: ComponentSize) {
  const tagRef = React.useRef<Nullable<HTMLStyleElement | HTMLLinkElement>>(null);

  const COMPACT_MODE = 'small';

  const getMicroHeader = () => {
    return document.getElementsByTagName(config.powerByMicro ? 'micro-app-head' : 'head')[0];
  };

  const getCompactStyles = (): Array<HTMLStyleElement | HTMLLinkElement> => {
    return Array.from(getMicroHeader().querySelectorAll(`style, link[rel="stylesheet"]`));
  };

  React.useEffect(() => {
    if (size === COMPACT_MODE) {
      if (!tagRef.current) {
        import('@jiaozhiye/qm-design-react/lib/style/compact.less').then(() => {
          const tag = getCompactStyles()
            .reverse()
            .find((el) => !el.hasAttribute('data-compact'));
          if (tag) {
            tag.setAttribute('data-compact', '');
            tag.disabled = false;
            tagRef.current = tag; // compact style
          }
        });
      } else {
        tagRef.current.disabled = false;
      }
    }

    return () => {
      if (tagRef.current) {
        tagRef.current.disabled = true;
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [size]);

  React.useEffect(() => {
    return () => {
      tagRef.current?.remove();
      tagRef.current = null;
    };
  }, []);
}
