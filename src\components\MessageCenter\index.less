/*
 * @Author: 焦质晔
 * @Date: 2021-07-14 16:10:40
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-06-22 09:45:05
 */
.app-message-center {
  height: 100%;
  .ant-dropdown-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 4px;
    transition: all 0.3s ease;
    cursor: pointer;
    .ant-badge {
      &-multiple-words {
        padding: 0 3px;
      }
    }
    &:hover {
      background-color: rgba(0, 0, 0, 0.045);
    }
  }
  .icon {
    color: @textColorSecondary;
    font-size: 18px;
    cursor: pointer;
  }
}

.app-message__popper {
  min-width: 300px !important;
  .container {
    padding: 0 16px;
    .msg-list {
      margin-bottom: 10px;
      ul li {
        margin: 0 -20px;
        padding: 8px 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        &:hover {
          background-color: @backgroundColorSecondary;
        }
        .list-item-meta {
          display: flex;
          .avatar {
            width: 32px;
            height: 32px;
            margin-top: 4px;
            border-radius: 50%;
            background: url(./assets/message-icon.png) 50% 50% no-repeat;
            background-size: 32px 32px;
          }
          .content {
            flex: 1;
            width: 0;
            margin-left: 12px;
            h4 {
              margin-bottom: 4px;
            }
            .description {
              font-size: 13px;
              color: @textColorTertiary;
            }
          }
        }
      }
    }
    .bottom-bar {
      margin: 0 -20px;
      height: 46px;
      line-height: 46px;
      text-align: center;
      border-top: 1px solid rgba(0, 0, 0, 0.06);
      border-radius: 0 0 2px 2px;

      & > div {
        display: inline-block;
        width: 50%;
        cursor: pointer;
        transition: all 0.3s;
        &:hover {
          background-color: #fbfbfb;
        }
        &:last-child {
          border-left: 1px solid rgba(0, 0, 0, 0.06);
        }
      }
    }
  }
}
