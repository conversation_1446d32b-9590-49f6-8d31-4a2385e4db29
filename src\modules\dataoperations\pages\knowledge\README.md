# 知识库管理功能

## 功能概述

知识库管理功能是AI智能中心的核心模块之一，提供了完整的知识库生命周期管理能力，包括：

- 📁 **类目管理**: 组织和分类知识库内容
- 🧠 **知识库管理**: 创建、编辑、删除知识库
- 📄 **文档管理**: 上传、下载、删除文档
- 🔗 **文档绑定**: 将文档与知识库关联
- ⚡ **知识库初始化**: 批量处理文档并生成向量索引

## 技术架构

### 前端技术栈
- **React 18**: 主要UI框架
- **Ant Design**: UI组件库
- **Less**: CSS预处理器
- **Axios**: HTTP客户端

### 后端接口
- **知识库API**: `/aio/rag-knowledge/*`
- **文档API**: `/aio/rag-document/*`
- **类目API**: `/aio/rag-category/*`
- **向量数据库API**: `/aio/milvus/*`

## 文件结构

```
src/modules/dataoperations/pages/knowledge/
├── index.jsx                    # 主页面组件
├── index.module.less           # 主页面样式
├── README.md                   # 功能说明文档
└── components/
    ├── KnowledgeModals.jsx     # 基础对话框组件
    └── AdvancedModals.jsx      # 高级对话框组件
```

## 主要功能

### 1. 类目管理
- **创建类目**: 支持层级结构的类目创建
- **编辑类目**: 修改类目名称和描述
- **删除类目**: 删除不需要的类目
- **树形展示**: 以树形结构展示类目层级

### 2. 知识库管理
- **创建知识库**: 配置知识库基本信息和参数
- **编辑知识库**: 修改知识库配置
- **删除知识库**: 删除知识库及其关联数据
- **搜索过滤**: 支持按名称搜索知识库

### 3. 文档管理
- **文档上传**: 支持PDF、Word、TXT格式文档
- **文档下载**: 下载已上传的文档
- **文档删除**: 删除不需要的文档
- **分页展示**: 支持分页浏览文档列表

### 4. 文档绑定
- **绑定文档**: 将文档与知识库关联
- **解绑文档**: 取消文档与知识库的关联
- **批量操作**: 支持批量绑定多个文档

### 5. 知识库初始化
- **文档处理**: 对绑定的文档进行切分处理
- **向量化**: 生成文档的向量表示
- **进度监控**: 实时显示初始化进度
- **状态轮询**: 自动获取处理状态更新

## 配置参数

### 知识库配置
- **数据源类型**: 本地数据源 / 外部数据源
- **切分策略**: 智能切分 / 固定长度切分
- **最大切片长度**: 100-4000字符
- **切片重叠长度**: 0-400字符
- **向量化模型**: text-embedding-v2/v3/v4
- **Collection**: Milvus向量数据库集合
- **相似度阈值**: 0.1-1.0

### 初始化参数
- **并行处理数量**: 1-20个文档同时处理
- **处理策略**: 智能切分，默认1000字符长度

## 使用指南

### 1. 创建知识库
1. 点击左侧"知识库管理"标签
2. 点击"新建"按钮
3. 填写知识库基本信息
4. 配置数据源和切分策略
5. 设置向量化参数
6. 点击"创建知识库"

### 2. 上传文档
1. 选择"数据管理"标签
2. 选择目标类目
3. 点击"导入数据"按钮
4. 选择类目和知识库
5. 上传文档文件
6. 确认上传

### 3. 绑定文档
1. 在知识库管理中选择目标知识库
2. 点击"绑定文档"按钮
3. 搜索和筛选可用文档
4. 选择要绑定的文档
5. 确认绑定

### 4. 初始化知识库
1. 选择已绑定文档的知识库
2. 点击"初始化知识库"按钮
3. 设置并行处理数量
4. 开始初始化
5. 监控处理进度

## API接口说明

### 知识库接口
```javascript
// 获取知识库列表
knowledgeList(params)

// 创建知识库
knowledgeCreate(params)

// 更新知识库
knowledgeUpdate(params)

// 删除知识库
knowledgeDelete(id)
```

### 文档接口
```javascript
// 获取文档列表
documentPage(params)

// 上传文档
documentUpload(formData)

// 下载文档
documentDownload(id)

// 删除文档
documentDelete(id)
```

### 初始化接口
```javascript
// 初始化知识库
initializeKnowledge(params)

// 获取初始化状态
getInitializeStatus(taskId)
```

## 注意事项

1. **文件格式限制**: 仅支持PDF、Word、TXT格式
2. **文件大小限制**: 单个文件不超过50MB
3. **并发处理**: 建议并行处理数量设置为1-5
4. **网络稳定**: 初始化过程需要稳定的网络连接
5. **浏览器兼容**: 建议使用Chrome、Firefox等现代浏览器

## 故障排除

### 常见问题
1. **上传失败**: 检查文件格式和大小
2. **初始化卡住**: 检查网络连接和服务器状态
3. **文档无法下载**: 检查文档是否存在
4. **样式异常**: 清除浏览器缓存

### 错误代码
- **200**: 操作成功
- **400**: 请求参数错误
- **401**: 未授权访问
- **404**: 资源不存在
- **500**: 服务器内部错误

## 更新日志

### v1.0.0 (2024-01-XX)
- ✨ 初始版本发布
- 🎯 完整的知识库管理功能
- 📱 响应式设计支持
- 🎨 统一的UI风格

## 开发团队

- **开发者**: wangxin213
- **项目**: SA-0214_APP_AIC_FE
- **模块**: dataoperations/knowledge
