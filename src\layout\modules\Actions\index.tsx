/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-09-19 13:50:51
 */
import React from 'react';
import config from '@/config';

import SizeSetting from '../SizeSetting';
import LangSetting from '../LangSetting';
import ScreenFull from '../ScreenFull';
import ThemeSetting from '../ThemeSetting';
import MessageCenter from '../MessageCenter';
import UserCenter from '../UserCenter';
import HelperDoc from '../HelperDoc';
import Setting from '@/modules/framework/pages/setting';

import './index.less';

const Actions: React.FC = () => {
  return (
    <div className={`app-actions`}>
      {config.showScreenFull && <ScreenFull />}
      {config.showHelperDoc && <HelperDoc />}
      <Setting />
      {config.showSizeSelect && <SizeSetting />}
      {config.showLangSelect && <LangSetting />}
      {config.showCustomTheme && <ThemeSetting />}
      {config.showNotification && <MessageCenter />}
      <UserCenter />
    </div>
  );
};

export default Actions;
