.drawerContainer {
    display: flex;
    height: 100%; // 确保抽屉内容撑满高度
}

.settingsPanel {
    width: 520px; // 根据UI调整
    padding: 20px;
    border-right: 1px solid #f0f0f0;
    display: flex;
    flex-direction: column;
    overflow-y: auto; // 如果内容过多则滚动

    .sectionTitle {
        font-weight: bold;
        margin-top: 16px;
        margin-bottom: 8px;
        font-size: 14px;
    }

    .settingItem {
        margin-bottom: 16px;
    }

    .label {
        display: block;
        margin-bottom: 8px;
        color: #595959;
    }
}

.detailDataSection {
    margin-top: 16px;

    .detailItem {
        display: flex;
        justify-content: space-between;
        align-items: start;
        background: #FFFFFF;

        border: 1px solid #E9EBF1;
        padding: 12px 16px;
        margin-bottom: 10px;
        border-radius: 8px; // 圆角
        // cursor: pointer;
        transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

        // &:hover {
        //     border-color: #1677ff; // Ant Design v5 primary color (原 #1890ff)
        //     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
        // }

        // &.active {
        //     border-color: #1677ff;
        //     background-color: #e6f0ff; // 对应 Ant Design v5 primary-1 (原 #e6f7ff)
        //     // box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2); // 可选：更明显的选中效果
        // }

        .itemContent {
            flex-grow: 1;
            margin-right: 16px; // 与右侧匹配度的间距

            .line1 {
                font-size: 14px;
                font-weight: 500; // medium weight
                color: #262626; // 深灰色，接近黑色
                margin-bottom: 6px; // 根据UI调整与第二行的间距
                line-height: 1.4;
            }

            .line2 {
                display: flex;
                align-items: center;
                font-size: 13px;
                color: #595959; // 标准灰色文字
                line-height: 1.3;

                .line2Text {
                    margin-right: 8px; // 与复制ID按钮的间距
                    color: #595959; // 确保文字颜色
                }

                .copyButton {
                    // Ant Button type="link" 会自带一些样式
                    padding: 0; // 移除内边距使其更像纯文本链接
                    height: auto; // 自适应高度
                    font-size: 13px; // 与第二行文字大小一致
                    // color: #1677ff; // 链接颜色，Ant Button type="link" 默认会是主题色
                }
            }
        }

        .itemMatchDegree {
            font-size: 13px;
            color: #595959; // "匹配度:" 文字的颜色
            white-space: nowrap; // 防止换行

            .degreeValue {
                font-weight: bold; // 匹配度数值加粗
                color: #389e0d; // 根据UI图调整的绿色 (AntD Green-6)
                margin-left: 4px; // "匹配度:" 与数值之间的间距
            }
        }
    }
}


.chatPanel {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%; // 确保聊天面板撑满抽屉高度
}

.chatHeader {
    padding: 16px 20px;
    // border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff; // 防止内容滚动时透出

    .robotName {
        font-size: 16px;
        font-weight: bold;
    }

    .actions {
        display: flex;
        align-items: center;
        gap: 16px;
    }
}

.chatContent {
    flex: 1;
    padding: 20px;
    overflow-y: auto; // 消息过多时滚动
}

.message {
    margin-bottom: 16px;
    display: flex;

    .avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: #1890ff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;
        font-size: 14px;
    }

    .messageBubble {
        padding: 10px 15px;
        max-width: 70%;
        word-wrap: break-word;
    }

    &.userMessage {
        flex-direction: row-reverse;

        .avatar {
            margin-left: 10px;
            margin-right: 0;
            background-color: #52c41a;
        }

        .messageBubble {
            color: #000;
            border-radius: 8px 0px 8px 8px;
            background: #FFF2DA;
        }
    }

    &.botMessage .messageBubble {
        background: #F5F6F9;
        border-radius: 0px 8px 8px 8px;
    }
}

.faqBlock {
    border-radius: 8px;
    width: 100%;
    background-color: #F3F4F7;
    border: 1px solid #E9EBF1;
    padding: 15px;
    margin-top: 8px; // 与机器人回复消息的间距

    .faqHeader {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .faqTitleFromResp {
            // 和UI图上的“对比方向”等对应
            font-weight: 500;
        }
    }

    .faqItem {
        margin-bottom: 6px;
        font-size: 13px;
        color: #595959;

        .label {
            // 对齐UI图的 “ChatID” “知识ID”等
            font-weight: normal;
            color: #8c8c8c;
            margin-right: 8px;
        }
    }

    .viewMore {
        text-align: center;
        margin-top: 10px;
    }

    .expandedContent {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px dashed #e8e8e8;
        font-size: 13px;
        color: #595959;
        white-space: pre-wrap; // 保留换行和空格
    }
}


.chatInputArea {
    // padding: 15px 20px;
    // border-top: 1px solid #f0f0f0;
    // background-color: #fff; // 防止内容滚动时透出
    position: relative;
    padding: 20px;
    width: 100%;

    :global {
        .ant-input:placeholder-shown {
            text-overflow: ellipsis;
            line-height: 46px;
        }
    }

    .input {
        // flex: 1;
        // margin-right: 10px;
        // padding: 12px;
        resize: none;
        font-size: 16px;
        border: 1px solid #E3E5E9;
        box-shadow: 0px 2px 8px 0px rgba(46, 93, 209, 0.04), 0px 2px 20px 0px rgba(46, 93, 209, 0.02);
        max-height: 70px;
        height: 70px;
        width: 100%;
        border-radius: 20px;
    }

    .btnArea {
        position: absolute;
        right: 20px;
        bottom: 20px;
        display: flex;
        align-items: center;
        justify-content: end;
        gap: 8px;

        .notAllowd {
            position: relative;
            cursor: not-allowed;
            right: 20px;
            bottom: 20px;
        }

        .sendImg {
            position: relative;
            cursor: pointer;
            right: 20px;
            bottom: 20px;
        }

    }
}


// 匹配度标签样式
.matchTag {
    margin-left: 8px;

    &.matched {
        color: #52c41a; // 有匹配
        border-color: #b7eb8f;
        background-color: #f6ffed;
    }

    &.notMatched {
        color: #f5222d; // 无匹配
        border-color: #ffa39e;
        background-color: #fff1f0;
    }
}

.timeInfo {
    font-size: 12px;
    color: #8c8c8c;
}



.flexBetween {
    display: flex;
    justify-content: space-between;
    align-items: center; // 确保内容垂直居中
}

.flexEnd12 {
    display: flex;
    justify-content: flex-end;
    align-items: center; // 确保内容垂直居中
    gap: 12px; // 添加右侧间距
}

.titleStart {
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0em;

    font-variation-settings: "opsz" auto;
    color: #262626;
    display: flex;
    align-items: center; // 确保标题和图标垂直居中
    gap: 12px; // 添加标题和图标之间的间距
    justify-content: start;

    &::before {
        content: '';
        width: 2px;
        height: 14px;
        background: #3A2AE4;
    }
}

.grayText {
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0px;

    color: rgba(0, 0, 0, 0.65);
}

.greenTag {
    border-radius: 1000px;
    opacity: 1;

    /* 自动布局 */
    display: flex;
    flex-direction: column;
    padding: 4px 12px;

    background: #E2F9E7;

    font-size: 13px;
    font-weight: normal;
    line-height: 16px;
    letter-spacing: 0em;

    color: #00B42A;

}

.innerHtml {
    :global {
        table {
            width: 100%;
            border-collapse: collapse;
            font-family: "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
            color: #333;
            margin: 1.5em 0;
            border: 1px solid #ccc;
        }

        table th,
        table td {
            padding: 12px 15px;
            text-align: left;
            border: 1px solid #e0e0e0;
        }

        table thead th {
            background-color: #f7f7f7;
            font-weight: 600;
            color: #111;
        }

        table tbody tr:nth-of-type(odd) {
            background-color: #fdfdfd;
        }

        table tbody tr:hover {
            background-color: #f0f8ff;
            /* AliceBlue */
        }
    }
}