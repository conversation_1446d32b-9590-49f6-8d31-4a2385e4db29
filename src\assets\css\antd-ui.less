/*
 * @Author: 焦质晔
 * @Date: 2021-07-07 12:41:27
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-01-17 09:30:13
 */
.ant-btn-text:hover,
.ant-btn-text:focus {
  background: transparent !important;
  border-color: transparent !important;
}
.ant-checkbox-disabled {
  .ant-checkbox-inner {
    background-color: @backgroundColorSecondary !important;
  }
}
.confirm-info {
  .ant-modal-body {
    padding: 20px 15px 15px 20px;
  }
}
