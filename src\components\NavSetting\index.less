/*
 * @Author: 焦质晔
 * @Date: 2022-04-23 19:02:33
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-01-04 14:25:24
 */
.app-header {
  .nav-setting {
    width: 30px;
    height: 40px;
    cursor: pointer;
    &.icon {
      background: url(./assets/set_nav_nor.png) center no-repeat;
      background-size: 14px 14px;
    }
    &.ant-dropdown-open.icon {
      background: url(./assets/set_nav_act.png) center no-repeat;
      background-size: 14px 14px;
    }
  }
}

.nav-setting__popper {
  left: 0 !important;
  width: 100%;
  min-width: @mobileSize;
  max-height: 80vh;
  background: #fff;
  overflow-y: auto;
  z-index: 9999 !important;
  box-shadow: 0px 6px 12px 0px rgba(69, 89, 120, 0.1);
  &::before {
    display: none;
  }
}

.app-nav-setting {
  .label {
    line-height: 30px;
    .title {
      font-size: 16px;
      font-weight: 700;
      color: @textColorSecondary;
    }
    .desc {
      font-size: 12px;
      color: @disabledColor;
      margin-left: @moduleMargin;
    }
  }
  .toper {
    padding: 20px 100px;
    background: #f0f2f5;
    .nav-list {
      display: flex;
      margin: 20px 0;
      dl,
      ul {
        display: flex;
        flex-wrap: wrap;
        li {
          position: relative;
          padding: 5px 15px;
          margin-right: 15px;
          margin-bottom: 15px;
          border-radius: 4px;
          box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
          background: #fff;
          user-select: none;
          cursor: move;
          &.ignore {
            pointer-events: none;
            background: @backgroundColorSecondary;
          }
          &:hover {
            .close {
              display: block;
            }
          }
          .close {
            display: none;
            position: absolute;
            right: -6px;
            top: -6px;
            color: @textColorTertiary;
            background: #fff;
            border-radius: 50%;
            cursor: pointer;
          }
        }
      }
    }
    .nav-btns {
      text-align: center;
    }
  }
  .main {
    padding: 20px 100px;
    ul {
      display: grid;
      grid-template-columns: repeat(auto-fill, 100px);
      grid-gap: 20px;
      justify-content: space-between;
      margin: 20px 0;
      li {
        width: 100px;
        padding: 20px 10px;
        border-radius: 4px;
        background: #fff;
        box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.1);
        text-align: center;
        user-select: none;
        cursor: pointer;
        transition: all 0.3s ease;
        h5 {
          margin-top: @moduleMargin;
        }
        &:hover {
          transform: translateY(-5px);
        }
      }
    }
  }
}
