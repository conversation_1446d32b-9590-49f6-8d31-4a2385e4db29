/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 15:58:50
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-30 20:23:25
 */
import {
  WORKBENCH_LIST,
  NAV_LIST,
  MENU_LIST,
  CUSTOM_NAV,
  DICT_DATA,
  AUTH_DATA,
  AUTH_BTN,
  STAR_MENU,
  COMMON_MENU,
  SUB_MENU,
  TAB_MENU,
  MICRO_STATE,
  IFRAME_MENU,
  MICRO_MENU,
  PREVENT_TAB,
  COMP_SIZE,
  LOCALE_LANG,
  THEME_COLOR,
  THEME_TYPE,
  WORKBENCH,
  SIGN_IN,
  SIGN_OUT,
  SITE_INFO,
  DEVICE,
  CUSTOM_INDEX,
} from '../types';
import {
  getMenuList,
  getDictList,
  getAuthList,
  getAuthBtn,
  getIndexDefine,
  getStarMenuList,
  getCommonMenus,
  setStarMenuList,
} from '@/api/application';
import localforage from 'localforage';
import { getUserInfo, removeToken, removeUserInfo } from '@/utils/cookies';
import { getGroupCode, getGlobalProp, setGlobalProp } from '@/utils';
import { local } from '@/utils/storage';
import localDict from '@/utils/localDict';
import config from '@/config';
import client from 'webpack-custom-theme/client';
import { getAntdSerials } from '@/layout/modules/ThemeSetting/color';

import type { Dictionary, IAuthUi } from '@/utils/types';
import type { INavMenu, ISideMenu, IWorkbench } from '@/store/reducers/app';

const formateWorkbench = (list): IWorkbench[] => {
  return list
    .filter((x) => !!x.code)
    .map((x) => ({
      id: x.id,
      title: x.title,
      code: x.code,
    }));
};

const createMenuPath = (item) => {
  const { feEngineeringName, caseCode } = item;
  if (config.isMainApp) {
    return feEngineeringName && caseCode ? `/${feEngineeringName}/${caseCode}` : '';
  }
  return caseCode ? `/${caseCode}` : '';
};

const createNavPath = (item) => {
  const { feEngineeringName } = item;
  if (!item.type || item.virtual) {
    return '';
  }
  if (config.isMainApp) {
    return feEngineeringName ? `/${feEngineeringName}/dashboard` : '';
  }
  return '/dashboard';
};

const formateNavMenus = (list): INavMenu[] => {
  return list.map((x) => {
    const item: INavMenu = {
      id: x.id,
      key: createNavPath(x),
      title: x.title,
      icon: x.icon,
      sysId: x.systemId,
      system: x.feEngineeringName,
      wbCode: x.workbenchCode,
      code: x.appCode,
      leader: x.appLeader,
      cnDesc: x.appDiscription,
      enDesc: x.appEngDiscription,
      shortName: x.appShortName,
      tagName: x.tagName,
      type: x.type ?? 1,
      sideState: x.sideState,
      virtual: x.virtual ?? 0,
      microHost: x.originHost ? `${x.originHost.trim()}/`.replace(/\/+$/, '/') : '',
    };
    if (Array.isArray(x.children)) {
      item.children = formateNavMenus(x.children);
    }
    return item;
  });
};

const formateSideMenus = (list, depth = 0): ISideMenu[] => {
  return list.map((x) => {
    const item: ISideMenu = {
      id: x.id,
      key: createMenuPath(x),
      title: x.title,
      icon: x.icon,
      target: x.target,
      loadMethod: x.loadMethod ?? 'iframe',
      caseHref: x.caseHref ?? '',
      keepAlive: !!(x.keepAlive ?? 1),
      hideInMenu: !!x.hidden,
    };
    // 一级导航
    if (depth === 0) {
      item.system = x.feEngineeringName;
      item.code = x.appCode;
    }
    if (Array.isArray(x.children)) {
      item.children = formateSideMenus(x.children, depth + 1);
    }
    return item;
  });
};

// 设置导航菜单
export const createMenus =
  (reload?: boolean) =>
  async (dispatch, getState): Promise<boolean> => {
    const {
      app: { sideMenus, workbench, lang },
    } = getState();

    if (!reload && sideMenus.length) {
      return true;
    }

    let status = true;
    let wbList: IWorkbench[] = [];
    let navList: INavMenu[] = [];
    let menuList: ISideMenu[] = [];

    if (process.env.MOCK_DATA === 'true') {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const res = require('@/mock/sideMenu').default;
      wbList = res[lang].workbenchList;
      navList = res[lang].appList;
      menuList = res[lang].sideMenus;
    } else {
      try {
        const res = await getMenuList({
          userId: getUserInfo().id,
          appCode: config.code,
          wbGroupId: getGroupCode(),
        });
        if (res.code === 200) {
          wbList = res.data?.workbenchList || [];
          navList = res.data?.appList || [];
          const _menuList = res.data?.sideMenus || [];
          menuList = _menuList.length ? _menuList : [{}];
        } else {
          status = false;
        }
      } catch (err) {
        status = false;
      }
    }

    // 以下是针对导航栏角色组获取问题的修复
    if (!workbench || workbench !== wbList) {
      dispatch({
        type: WORKBENCH,
        payload: wbList[0]?.code || '',
      });
    }

    dispatch({
      type: WORKBENCH_LIST,
      payload: formateWorkbench(wbList),
    });

    dispatch({
      type: NAV_LIST,
      payload: formateNavMenus(navList),
    });

    dispatch({
      type: MENU_LIST,
      payload: formateSideMenus(menuList),
    });

    return status;
  };

// 设置数据字典
export const createDictData =
  (reload?: boolean) =>
  async (dispatch, getState): Promise<void> => {
    if (!reload && getGlobalProp('__dict_data__')) {
      return;
    }

    let data: Record<string, Array<Dictionary> | string> = localDict;

    try {
      if (process.env.MOCK_DATA !== 'true') {
        const res = await getDictList({
          userId: getUserInfo().id,
          appCode: config.code,
          wbGroupId: getGroupCode(),
        });
        if (res.code === 200) {
          // 数据字典规则：如果有重复的 Code，服务端覆盖客户端
          data = { ...data, ...res.data };
        }
      }
    } catch (err) {
      return Promise.reject(err);
    }

    // 挂载到 window 对象
    setGlobalProp('__dict_data__', data);
    // 数据字典本地存储
    localforage.setItem('dict', data);

    // dispatch({
    //   type: DICT_DATA,
    //   payload: data,
    // });
  };

// 设置界面权限
export const createAuthData =
  (reload?: boolean) =>
  async (dispatch, getState): Promise<void> => {
    if (!reload && getGlobalProp('__auth_data__')) {
      return;
    }

    let data: Record<string, IAuthUi | string> = {};

    try {
      if (process.env.MOCK_DATA !== 'true') {
        const res = await getAuthList({
          userId: getUserInfo().id,
          appCode: config.code,
          wbGroupId: getGroupCode(),
        });
        if (res.code === 200) {
          data = { ...res.data };
        }
      }
    } catch (err) {
      return Promise.reject(err);
    }

    // 挂载到 window 对象
    setGlobalProp('__auth_data__', data);
    // 权限本地存储
    localforage.setItem('auth', data);

    // dispatch({
    //   type: AUTH_DATA,
    //   payload: data,
    // });
  };

// 设置按钮权限
export const createAuthBtn =
  (reload?: boolean) =>
  async (dispatch, getState): Promise<void> => {
    if (!reload && getGlobalProp('__auth_btn_data__')) {
      return;
    }

    let data: Record<string, string[] | string> = {};

    try {
      if (process.env.MOCK_DATA !== 'true') {
        const res = await getAuthBtn({
          userId: getUserInfo().id,
          appCode: config.code,
          wbGroupId: getGroupCode(),
        });
        if (res.code === 200) {
          data = { ...res.data };
        }
      }
    } catch (err) {
      return Promise.reject(err);
    }

    // 挂载到 window 对象
    setGlobalProp('__auth_btn_data__', data);
    // 权限本地存储
    localforage.setItem('auth_btn', data);

    // dispatch({
    //   type: AUTH_BTN,
    //   payload: data,
    // });
  };

// 获取收藏菜单
export const createStarMenu =
  () =>
  async (dispatch, getState): Promise<void> => {
    let data: ISideMenu[] = (await localforage.getItem(`${getUserInfo().id}_star_menus`)) || [];

    if (process.env.MOCK_DATA !== 'true' && !data.length) {
      const res = await getStarMenuList({
        userId: getUserInfo().id,
        appCode: config.code,
      });
      if (res.code === 200) {
        data =
          res.data?.map((x) => ({
            id: x.id,
            key: x.key,
            system: x.system,
            title: x.title,
            crumb: x.crumb,
          })) ?? [];
        // 收藏本地存储
        localforage.setItem(`${getUserInfo().id}_star_menus`, data);
      }
    }

    dispatch({
      type: STAR_MENU,
      payload: data,
    });
  };

// 设置收藏菜单
export const setStarMenu =
  (data: ISideMenu[]) =>
  async (dispatch, getState): Promise<void> => {
    if (config.showStarNav && process.env.MOCK_DATA !== 'true') {
      await setStarMenuList({
        userId: getUserInfo().id,
        appCode: config.code,
        wbGroupId: getGroupCode(),
        starMenus: data,
      });
    }
    localforage.setItem(`${getUserInfo().id}_star_menus`, data);
    dispatch({
      type: STAR_MENU,
      payload: data,
    });
  };

// 获取常用菜单
export const createCommonMenu =
  () =>
  async (dispatch, getState): Promise<void> => {
    let data: ISideMenu[] = [];

    if (process.env.MOCK_DATA === 'true') {
      data = (await localforage.getItem(`${getUserInfo().id}_common_menus`)) || [];
    } else {
      const res = await getCommonMenus({
        userId: getUserInfo().id,
      });
      if (res.code === 200) {
        data =
          res.data?.map((x) => ({
            id: x.id,
            key: x.key,
            system: x.system,
            title: x.title,
            crumb: x.crumb,
          })) ?? [];
      }
    }

    dispatch({
      type: COMMON_MENU,
      payload: data,
    });
  };

// 自定义首页
export const createCustomIndex =
  (reload?: boolean) =>
  async (dispatch, getState): Promise<void> => {
    const {
      app: { workbench, customIndex },
    } = getState();

    if (!config.isMainApp) return;

    const localData = local.getItem(`${getUserInfo().id}_customize_page`) || {};
    let data: Record<string, any> = localData.workSpace || {};

    if (process.env.MOCK_DATA !== 'true') {
      try {
        if (reload || workbench !== localData.workbenchCode || !Object.keys(customIndex).length) {
          const res = await getIndexDefine({
            workbenchCode: workbench,
            userId: getUserInfo().id,
          });
          if (res.code === 200 && res.data.value) {
            data = res.data.value;
            // 本地存储
            local.setItem(`${getUserInfo().id}_customize_page`, {
              workbenchCode: workbench,
              workSpace: data,
            });
          }
        }
      } catch (err) {
        // ...
      }
    }

    dispatch({
      type: CUSTOM_INDEX,
      payload: data,
    });
  };

// 退出登录
export const createSignOut = () => (dispatch, getState) => {
  removeToken();
  removeUserInfo();
  dispatch({
    type: SIGN_OUT,
    payload: {},
  });
  setTimeout(() => {
    window.parent.postMessage({ type: SIGN_OUT, data: '' }, config.postOrigin);
  }, 400);
};

// 创建主题色
export const createTheme = (color: string) => (dispatch, getState) => {
  const options = {
    newColors: getAntdSerials(color),
    changeUrl: (cssUrl) => `${config.baseUrl.replace(/\/$/, '')}/${cssUrl}`,
    openLocalStorage: false,
  };
  client.changer.changeColor(options, Promise).then(() => {
    dispatch({
      type: THEME_COLOR,
      payload: color,
    });
    localStorage.setItem('theme_color', color);
  });
};

// 登录
export const createSignIn = (payload) => ({ type: SIGN_IN, payload });

// 子应用加载状态
export const createMicroState = (payload) => ({ type: MICRO_STATE, payload });

// 设置工作台列表
export const createWorkbenchList = (payload) => ({ type: WORKBENCH_LIST, payload });

// 自定义导航
export const createCustomNav = (payload) => ({ type: CUSTOM_NAV, payload });

// 切换子应用菜单
export const createSubMenu = (payload) => ({ type: SUB_MENU, payload });

// 设置顶部选项卡导航
export const createTabMenu = (payload, behavior) => ({ type: TAB_MENU, payload, behavior });

// 设置 iframe 导航
export const createIframeMenu = (payload, behavior) => ({ type: IFRAME_MENU, payload, behavior });

// 设置 micro 导航
export const createMicroMenu = (payload, behavior) => ({ type: MICRO_MENU, payload, behavior });

// 设置禁止关闭页签
export const createPreventTab = (payload, behavior) => ({ type: PREVENT_TAB, payload, behavior });

// 设置尺寸
export const createComponentSize = (payload) => ({ type: COMP_SIZE, payload });

// 设置多语言
export const createLocaleLang = (payload) => ({ type: LOCALE_LANG, payload });

// 设置主题颜色
export const createThemeColor = (payload) => ({ type: THEME_COLOR, payload });

// 设置主题模式
export const createThemeType = (payload) => ({ type: THEME_TYPE, payload });

// 设置设备类型
export const createDeviceType = (payload) => ({ type: DEVICE, payload });

// 设置工作台
export const createWorkbench = (payload) => ({ type: WORKBENCH, payload });

// 设置站点信息
export const createSiteInfo = (payload) => ({ type: SITE_INFO, payload });
