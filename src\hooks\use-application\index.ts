/*
 * @Author: 焦质晔
 * @Date: 2022-01-17 10:58:59
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-25 08:48:28
 */
import * as React from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import microApp from '@micro-zoe/micro-app';
import { useSelector, useDispatch } from '@/store';
import routes from '@/router/config';
import { whiteList, whiteAuth, matchRoutes, isIframe } from '@/router';
import { deepFindNav } from '@/store/reducers/app';
import {
  createMenus,
  createTabMenu,
  createIframeMenu,
  createMicroMenu,
  createSubMenu,
  createPreventTab,
} from '@/store/actions';
import { useLocale, useEvent } from '@/hooks';
import { emitter as microEvent } from '@/utils/mitt';
import { getUserInfo } from '@/utils/cookies';
import { addSearchToURL, addUrlToken, getSystem, isHttpLink, Message } from '@/utils';
import { OUTSIDE_CLICK, SEND_LOCAL, SITE_INFO, SUB_EVENT } from '@/store/types';
import config from '@/config';
import envConf from '@/config/envMaps';

import type { AppState, INavMenu } from '@/store/reducers/app';
import type { Nullable } from '@/utils/types';

const EXCLUDE_URLS = ['http://localhost:8000', 'http://localhost:18000', '/static/tinymce/'];

export default function useApplication() {
  const { flattenMenus, navList, tabMenus, microMenus, iframeMenus, size, lang, themeColor } =
    useSelector((state: AppState) => state.app);
  const dispatch = useDispatch();
  const location = useLocation();
  const history = useHistory();
  const { t } = useLocale();

  const prevPath = React.useRef<string>('');
  const setPrevPath = (value: string) => (prevPath.current = value);

  const fetchNavMenus = async (reload?: boolean) => {
    if (!reload && (flattenMenus.length || window.__MAIM_APP_ENV__ || isIframe(location.pathname)))
      return;
    const isLoaded: boolean = await dispatch<any>(createMenus(reload));
    if (!isLoaded) {
      return console.error('应用菜单加载失败，请检查菜单接口！');
    }
    addTabMenus(location.pathname);
  };

  const addTabMenus = useEvent((pathname: string) => {
    const { search } = location;
    const { route } = matchRoutes(routes, pathname).pop()!;
    // title 非空判断 - 重要
    if (!route.meta?.title || notDisplayTab(pathname)) return;
    const title = !search
      ? route.meta.title
      : flattenMenus.find((x) => x.key === pathname + search)?.title || route.meta.title;
    // 选项卡菜单
    dispatch(
      createTabMenu(
        Object.assign(
          {},
          { path: pathname, title },
          search ? { search } : null,
          tabMenus.length ? { from: prevPath.current } : null
        ),
        'add'
      )
    );
    setPrevPath(pathname);
    // iframe 模式
    if (route.iframePath) {
      const navItem = deepFindNav(navList, (x) => x.system === pathname.match(/^\/+([^/]+)/)?.[1]);
      dispatch(
        createIframeMenu(
          {
            key: pathname,
            value: addSearchToURL(
              isHttpLink(route.iframePath)
                ? addUrlToken(route.iframePath, {
                    systemId: navItem?.sysId || '',
                    appCode: navItem?.code || '',
                    wbEnv: getUserInfo().switchedEnv || envConf.env,
                  })
                : route.iframePath,
              search
            ),
            keep: route.meta.keepAlive,
          },
          'add'
        )
      );
      getFrameByName(pathname)?.contentWindow!.postMessage(
        {
          type: `${SUB_EVENT}__${pathname.split('/').pop()}`,
          data: {
            activated: pathname,
          },
        },
        config.postOrigin
      );
      getFrameByName(pathname)?.focus();
    }
    // micro 模式
    if (route.microHost && route.microRule) {
      dispatch(
        createMicroMenu(
          { key: pathname, value: route.microHost, keep: route.meta.keepAlive, search },
          'add'
        )
      );
      microEvent.$emit(SUB_EVENT, {
        type: `${SUB_EVENT}__${pathname.split('/').pop()}`,
        data: {
          activated: pathname,
        },
      });
    }
  });

  const popEventHandle = useEvent((location, action) => {
    // 处理浏览器 前进/后退
    if (action === 'POP') {
      const _system = location.pathname.match(/^\/+([^/]+)/)?.[1] || '';
      if (_system && _system !== getSystem()) {
        dispatch(createSubMenu(_system));
      }
    }
  });

  // 最大页签数量判断
  const maxTabValidate = (pathname: string) => {
    if (tabMenus.length >= config.maxCacheNum && !tabMenus.find((x) => x.path === pathname)) {
      Message(t('app.information.maxCache', { total: config.maxCacheNum }), 'warning');
      return false;
    }
    return true;
  };

  const notDisplayTab = (pathname: string) => {
    return [...whiteList, ...whiteAuth.slice(0, -1), '/chp/']
      .filter((x) => x !== whiteAuth[2])
      .some((x) => pathname.startsWith(x));
  };

  const getFrameByName = (name: string) => {
    return document.getElementsByName(name)[0] as Nullable<HTMLIFrameElement>;
  };

  const refreshView = (pathname: string, search = '') => {
    // micro-app
    if (config.microType === 'micro-app') {
      const microItem = microMenus.find((x) => x.key === pathname);
      if (microItem) {
        dispatch(createMicroMenu(pathname, 'remove'));
      }
    }
    history.replace(`/redirect${pathname}` + (search || location.search));
    // iframe
    let $iframe = getFrameByName(pathname);
    if (!$iframe) return;
    // 释放 iframe 内存
    $iframe.src = 'about:blank';
    try {
      $iframe.contentWindow?.document.write('');
      $iframe.contentWindow?.document.clear();
    } catch {
      // ...
    }
    $iframe.parentNode?.removeChild($iframe);
    $iframe = null;
    // 释放 iframe 内存 END
    const target = iframeMenus.find((x) => x.key === pathname);
    dispatch(createIframeMenu(pathname, 'remove'));
    setTimeout(() => {
      dispatch(
        createIframeMenu({ key: pathname, value: target!.value, keep: target!.keep }, 'add')
      );
    }, 10);
  };

  const startMicroApp = () => {
    if (microApp.hasInit) return;
    microApp.start({
      'disable-memory-router': true, // 关闭虚拟路由系统
      'disable-patch-request': true, // 关闭对子应用请求的拦截
      'router-mode': 'native', // 开启路由隔离
      fetch: (url, options) => {
        const config: Record<string, unknown> = {
          // credentials: 'include', // 请求时带上cookie
        };
        return window.fetch(url, Object.assign({}, options, config)).then((res) => res.text());
      },
      excludeAssetFilter: (assetUrl) => {
        if (EXCLUDE_URLS.some((x) => assetUrl.includes(x))) {
          return true; // 不会劫持处理当前文件
        }
        return false;
      },
    });
  };

  const startPreFetch = (navList: INavMenu[]) => {
    microApp.preFetch(
      navList
        .filter((x) => !x.virtual && x.key)
        .map((x) => ({
          name: x.key.replace(/\/+/g, '-').slice(1),
          url: x.microHost!,
          level: 2,
        })),
      1000
    );
  };

  const sendLocalStore = (name: string) => {
    getFrameByName(name)?.contentWindow!.postMessage(
      {
        type: SEND_LOCAL,
        data: {
          size,
          lang,
          theme_color: themeColor,
          user_info: localStorage.getItem('user_info'),
        },
      },
      config.postOrigin
    );
  };

  const sendSiteInfo = (name: string, data: Record<string, string>) => {
    getFrameByName(name)?.contentWindow!.postMessage({ type: SITE_INFO, data }, config.postOrigin);
  };

  const setLocalStore = (data: Record<string, string>) => {
    for (const key in data) {
      if (!data[key]) continue;
      localStorage.setItem(key, data[key]);
    }
  };

  const openView = (fullpath: string) => {
    history.push(fullpath);
    // 切换子应用菜单(工作台)
    const systemName = getSystem();
    const newSystemName = fullpath.match(/^\/+([^/]+)/)?.[1] || systemName;
    if (
      config.isMainApp &&
      newSystemName !== 'home' &&
      newSystemName !== 'redirect' &&
      newSystemName !== systemName
    ) {
      dispatch(createSubMenu(newSystemName));
    }
  };

  const closeView = (fullpath: string) => {
    dispatch(createTabMenu(fullpath, 'remove'));
    dispatch(createIframeMenu(fullpath, 'remove'));
    dispatch(createMicroMenu(fullpath, 'remove'));
    dispatch(createPreventTab(fullpath, 'remove'));
  };

  const setControlTab = (data: Record<string, string | undefined>) => {
    if (data.action === 'add') {
      const { message, showConfirm = true, delay = 200 } = data;
      dispatch(createPreventTab({ path: data.path, message, showConfirm, delay }, 'add'));
    } else {
      dispatch(createPreventTab(data.path, 'remove'));
    }
  };

  const emitOutsideClick = () => {
    if (window.top === window.self) return;
    window.parent.postMessage({ type: OUTSIDE_CLICK, data: '' }, config.postOrigin);
  };

  const dispatchMouseClick = () => {
    document.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
    document.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
    document.body.click();
  };

  return {
    fetchNavMenus,
    addTabMenus,
    openView,
    closeView,
    refreshView,
    popEventHandle,
    startMicroApp,
    startPreFetch,
    sendLocalStore,
    sendSiteInfo,
    setLocalStore,
    getFrameByName,
    setControlTab,
    emitOutsideClick,
    dispatchMouseClick,
  };
}
