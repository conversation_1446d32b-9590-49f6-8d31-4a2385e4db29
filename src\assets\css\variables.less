/*
 * @Author: 焦质晔
 * @Date: 2019-11-28 16:02:24
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-01-04 14:23:34
 */
// 主要色
@primaryColor: var(--qm-primary-color);

// 标题色
@headingColor: rgba(0, 0, 0, 0.85);

// 主文本色
@textColor: rgba(0, 0, 0, 0.85);

// 次文本色
@textColorSecondary: rgba(0, 0, 0, 0.65);

// 灰阶文本色
@textColorTertiary: rgba(0, 0, 0, 0.45);

// 失效色
@disabledColor: rgba(0, 0, 0, 0.45);

// 文本大小
@textSize: 14px;

// 次文本大小
@textSizeSecondary: 12px;

// 主边框色
@borderColor: #d9d9d9;

// 次边框色
@borderColorSecondary: #e8e8e8;

// 组件/浮层圆角
@borderRadius: 2px;

// 模块之间距离
@moduleMargin: 10px;
@modulePadding: 10px;

// 响应式阈值
@mobileSize: 960px;

// 浮层阴影
@shadow-up: 0 -2px 8px rgba(0, 0, 0, 0.15);
@shadow-down: 0 2px 8px rgba(0, 0, 0, 0.15);
@shadow-left: -2px 0 8px rgba(0, 0, 0, 0.15);
@shadow-right: 2px 0 8px rgba(0, 0, 0, 0.15);
@boxShadow: @shadow-down;

// 主背景色
@backgroundColor: #f2f2f2;

// 次背景色
@backgroundColorSecondary: #f5f5f5;

// 侧栏背景色
@siderBackgroundColor: #272b2e;

@subSiderBackgroundColor: #1a1a1a;
