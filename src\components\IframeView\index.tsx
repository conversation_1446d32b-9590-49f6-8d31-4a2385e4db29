/*
 * @Author: 焦质晔
 * @Date: 2023-12-06 16:02:02
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-08-30 13:26:45
 */
import React from 'react';
import { useApplication } from '@/hooks';
import { isSameOrigin } from '@/utils';

type IProps = {
  name: string;
  url: string;
  className?: string;
  style?: React.CSSProperties;
};

const IframeView: React.FC<IProps> = (props) => {
  const { name, url, className, style } = props;
  const { sendLocalStore } = useApplication();

  return (
    <iframe
      name={name}
      src={url}
      width="100%"
      height="100%"
      frameBorder="0"
      allowFullScreen
      className={className}
      style={style}
      onLoad={(ev) => {
        const $iframe = ev.target as HTMLIFrameElement;
        if (!isSameOrigin($iframe)) {
          sendLocalStore(name);
        }
        $iframe.focus();
      }}
    />
  );
};

export default IframeView;
