/*
 * @Author: 焦质晔
 * @Date: 2022-11-25 18:41:11
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-12-24 11:42:54
 */
/** @jsxRuntime classic */
/** @jsx jsxCustomEvent */
import jsxCustomEvent from '@micro-zoe/micro-app/polyfill/jsx-custom-event';
import React from 'react';
import classNames from 'classnames';
import RGL, { WidthProvider } from 'react-grid-layout';
import { useLocale, useEvent } from '@/hooks';
import { emitter as microEvent } from '@/utils/mitt';
import { WIDGET_MAIN, WIDGET_SUB } from '@/store/types';
import config from '@/config';

import { ToCustomPage } from '@/components';

import './index.less';

const ResponsiveGridLayout = WidthProvider(RGL);
const WIDGET_ROWSPAN = 'WIDGET_ROWSPAN';
const EDITABLE_RUNTIME = false;
const DEFAULT_CONF = { cardMargin: 16, cols: 6, rowHeight: 75 };

type IProps = {
  workSpace: any;
};

const CustomDashboard: React.FC<IProps> = (props) => {
  const { workSpace } = props;
  const { t } = useLocale();

  const [layout, setLayout] = React.useState<any[]>(workSpace.layout || []);

  React.useEffect(() => {
    microEvent.$on(WIDGET_MAIN, widgetEventHandler);
    return () => microEvent.$off(WIDGET_MAIN, widgetEventHandler);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const widgetEventHandler = useEvent((data) => {
    if (data.code === WIDGET_ROWSPAN) {
      setLayout((prev) => {
        return prev.map((x) => {
          if (x.i === data.payload.name) {
            return {
              ...x,
              h: workSpace.layout.find((k) => k.i === x.i).h + data.payload.rows,
            };
          }
          return x;
        });
      });
      return;
    }
    microEvent.$emit(WIDGET_SUB, data);
  });

  const customIndexRender = (option) => {
    const { bgUrl } = option;
    const { cardMargin, cols, rowHeight } = DEFAULT_CONF;

    const _style: React.CSSProperties = {
      backgroundImage: bgUrl ? `url(${bgUrl})` : '',
    };

    const RGLProps = !EDITABLE_RUNTIME ? { isDraggable: false, isResizable: false } : {};

    return (
      <div className={classNames('app-dashboard')} style={_style}>
        <ToCustomPage
          title={t('app.customPage.dashboardEdit')}
          search={`?appCode=${config.code}`}
        />
        <div className={`container`}>
          <ResponsiveGridLayout
            layout={layout}
            autoSize={true}
            useCSSTransforms={false}
            allowOverlap={false}
            margin={[cardMargin, cardMargin]}
            cols={cols}
            rowHeight={rowHeight}
            {...RGLProps}
          >
            {layout.map((x) => {
              const { id, boxShadow = 1, noAuth = 0, url = '', ...extra } = x.children || {};
              const host = url.split(/\b(?:public|iframe)\b/)[0];
              return (
                <div key={x.i} className={classNames(`grid-item`, { shadow: boxShadow })}>
                  {!noAuth ? (
                    url && host ? (
                      <micro-app
                        key={id}
                        name={`widget-${config.system}-${x.i}`}
                        baseroute="/"
                        url={host}
                        ignore=""
                        is-widget=""
                        clear-data
                        data={{
                          isMainEnv: true,
                          isWidget: true,
                          microEvent,
                          pathRoute: url.replace(host, '/'),
                          extraProps: extra,
                        }}
                        style={{ display: 'block', height: '100%' }}
                      />
                    ) : null
                  ) : (
                    <div className={`no-auth`}>
                      <span>{t('app.global.noAuth')}</span>
                    </div>
                  )}
                </div>
              );
            })}
          </ResponsiveGridLayout>
        </div>
      </div>
    );
  };

  return customIndexRender(workSpace);
};

export default CustomDashboard;
