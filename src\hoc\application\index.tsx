/*
 * @Author: 焦质晔
 * @Date: 2021-07-18 19:57:39
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2025-03-25 08:47:53
 */
import React, { Component } from 'react';
import hoistStatics from 'hoist-non-react-statics';
import { withRouter } from 'react-router-dom';
import { connect } from '@/store/connect';
import microApp from '@micro-zoe/micro-app';
import routes from '@/router/config';
import { emitter as microEvent } from '@/utils/mitt';
import { t } from '@/locale';
import { addSearchToURL, addUrlToken, getSystem, isHttpLink, Message } from '@/utils';
import { whiteList, whiteAuth, matchRoutes, isIframe } from '@/router';
import { deepFindNav } from '@/store/reducers/app';
import {
  createMenus,
  createTabMenu,
  createIframeMenu,
  createMicroMenu,
  createSubMenu,
  createThemeColor,
  createPreventTab,
} from '@/store/actions';
import { OUTSIDE_CLICK, SEND_LOCAL, SITE_INFO, SUB_EVENT } from '@/store/types';
import store from '@/store';
import config from '@/config';

import type { AppState } from '@/store/reducers/app';
import type { Nullable } from '@/utils/types';

const EXCLUDE_URLS = ['http://localhost:8000', 'http://localhost:18000', '/static/tinymce/'];

export default (WrappedComponent: React.ComponentType<any>): any => {
  @connect(
    (state: AppState) => ({
      size: state.app.size,
      lang: state.app.lang,
      themeColor: state.app.themeColor,
      tabMenus: state.app.tabMenus,
      microMenus: state.app.microMenus,
      navList: state.app.navList,
      flattenMenus: state.app.flattenMenus,
    }),
    {
      createMenus,
      createTabMenu,
      createIframeMenu,
      createMicroMenu,
      createSubMenu,
      createThemeColor,
      createPreventTab,
    }
  )
  @withRouter
  class C extends Component<any> {
    static displayName = `App(${WrappedComponent.displayName || WrappedComponent.name})`;

    fetchNavMenus = async (reload?: boolean) => {
      const { pathname } = this.props.location;
      const { flattenMenus } = this.props;
      if (!reload && (flattenMenus.length || window.__MAIM_APP_ENV__ || isIframe(pathname))) return;
      const isLoaded: boolean = await this.props.createMenus(reload);
      if (!isLoaded) {
        return console.error('应用菜单加载失败，请检查菜单接口！');
      }
      this.addTabMenus('');
    };

    notDisplayTab = (pathname: string) => {
      return [...whiteList, ...whiteAuth.slice(0, -1), '/chp/']
        .filter((x) => x !== whiteAuth[2])
        .some((x) => pathname.startsWith(x));
    };

    addTabMenus = (prevPathname: string) => {
      const { tabMenus, navList, flattenMenus } = this.props;
      const { pathname, search } = this.props.location;
      const { route } = matchRoutes(routes, pathname).pop()!;
      // title 非空判断 - 重要
      if (!route.meta?.title || this.notDisplayTab(pathname)) return;
      // 最大数量判断
      if (tabMenus.length >= config.maxCacheNum && !tabMenus.find((x) => x.path === pathname)) {
        Message(t('app.information.maxCache', { total: config.maxCacheNum }), 'warning');
        return this.openView(prevPathname);
      }
      const title = !search
        ? route.meta.title
        : flattenMenus.find((x) => x.key === pathname + search)?.title || route.meta.title;
      // 选项卡菜单
      this.props.createTabMenu(
        Object.assign(
          {},
          { path: pathname, title },
          search ? { search } : null,
          tabMenus.length ? { from: prevPathname } : null
        ),
        'add'
      );
      // iframe 模式
      if (route.iframePath) {
        const navItem = deepFindNav(
          navList,
          (x) => x.system === pathname.match(/^\/+([^/]+)/)?.[1]
        );
        this.props.createIframeMenu(
          {
            key: pathname,
            value: addSearchToURL(
              isHttpLink(route.iframePath)
                ? addUrlToken(route.iframePath, {
                    systemId: navItem?.sysId || '',
                    appCode: navItem?.code || '',
                  })
                : route.iframePath,
              search
            ),
            keep: route.meta.keepAlive,
          },
          'add'
        );
        this.getFrameByName(pathname)?.contentWindow!.postMessage(
          {
            type: `${SUB_EVENT}__${pathname.split('/').pop()}`,
            data: {
              activated: pathname,
            },
          },
          config.postOrigin
        );
        this.getFrameByName(pathname)?.focus();
      }
      // micro 模式
      if (route.microHost && route.microRule) {
        this.props.createMicroMenu(
          { key: pathname, value: route.microHost, keep: route.meta.keepAlive, search },
          'add'
        );
        microEvent.$emit(SUB_EVENT, {
          type: `${SUB_EVENT}__${pathname.split('/').pop()}`,
          data: {
            activated: pathname,
          },
        });
      }
    };

    popEventHandle = (location, action) => {
      // 处理浏览器 前进/后退
      if (action === 'POP') {
        const _system = location.pathname.match(/^\/+([^/]+)/)?.[1] || '';
        if (_system && _system !== getSystem()) {
          this.props.createSubMenu(_system);
        }
      }
    };

    getFrameByName = (name: string) => {
      return document.getElementsByName(name)[0] as Nullable<HTMLIFrameElement>;
    };

    refreshView = (pathname: string, search = '') => {
      // micro-app
      if (config.microType === 'micro-app') {
        const microItem = this.props.microMenus.find((x) => x.key === pathname);
        if (microItem) {
          this.props.createMicroMenu(pathname, 'remove');
        }
      }
      this.props.history.replace(`/redirect${pathname}` + (search || this.props.location.search));
      // iframe
      let $iframe = this.getFrameByName(pathname);
      if (!$iframe) return;
      // 释放 iframe 内存
      $iframe.src = 'about:blank';
      try {
        $iframe.contentWindow?.document.write('');
        $iframe.contentWindow?.document.clear();
      } catch (e) {
        // ...
      }
      $iframe.parentNode?.removeChild($iframe);
      $iframe = null;
      // 释放 iframe 内存 END
      const {
        app: { iframeMenus },
      } = store.getState();
      const target = iframeMenus.find((x) => x.key === pathname);
      this.props.createIframeMenu(pathname, 'remove');
      setTimeout(() => {
        this.props.createIframeMenu(
          { key: pathname, value: target!.value, keep: target!.keep },
          'add'
        );
      }, 10);
    };

    startMicroApp = () => {
      if (microApp.hasInit) return;
      microApp.start({
        'disable-memory-router': true, // 关闭虚拟路由系统
        'disable-patch-request': true, // 关闭对子应用请求的拦截
        'router-mode': 'native', // 开启路由隔离
        fetch: (url, options) => {
          const config: Record<string, unknown> = {
            // credentials: 'include', // 请求时带上cookie
          };
          return window.fetch(url, Object.assign({}, options, config)).then((res) => res.text());
        },
        excludeAssetFilter: (assetUrl) => {
          if (EXCLUDE_URLS.some((x) => assetUrl.includes(x))) {
            return true; // 不会劫持处理当前文件
          }
          return false;
        },
      });
    };

    sendLocalStore = (name: string) => {
      this.getFrameByName(name)?.contentWindow!.postMessage(
        {
          type: SEND_LOCAL,
          data: {
            size: this.props.size,
            lang: this.props.lang,
            theme_color: this.props.themeColor,
            user_info: localStorage.getItem('user_info'),
          },
        },
        config.postOrigin
      );
    };

    sendSiteInfo = (name: string, data: Record<string, string>) => {
      this.getFrameByName(name)?.contentWindow!.postMessage(
        { type: SITE_INFO, data },
        config.postOrigin
      );
    };

    setLocalStore = (data: Record<string, string>) => {
      for (const key in data) {
        if (!data[key]) continue;
        localStorage.setItem(key, data[key]);
      }
    };

    openView = (fullpath: string) => {
      this.props.history.push(fullpath);
      // 切换子应用菜单(工作台)
      const systemName = getSystem();
      const newSystemName = fullpath.match(/^\/+([^/]+)/)?.[1] || systemName;
      if (
        config.isMainApp &&
        newSystemName !== 'home' &&
        newSystemName !== 'redirect' &&
        newSystemName !== systemName
      ) {
        this.props.createSubMenu(newSystemName);
      }
    };

    closeView = (fullpath: string) => {
      this.props.createTabMenu(fullpath, 'remove');
      this.props.createIframeMenu(fullpath, 'remove');
      this.props.createMicroMenu(fullpath, 'remove');
      this.props.createPreventTab(fullpath, 'remove');
    };

    setControlTab = (data: Record<string, string | undefined>) => {
      if (data.action === 'add') {
        const { message, showConfirm = true, delay = 200 } = data;
        this.props.createPreventTab({ path: data.path, message, showConfirm, delay }, 'add');
      } else {
        this.props.createPreventTab(data.path, 'remove');
      }
    };

    emitOutsideClick = () => {
      if (window.top === window.self) return;
      window.parent.postMessage({ type: OUTSIDE_CLICK, data: '' }, config.postOrigin);
    };

    dispatchMouseClick = () => {
      document.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
      document.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
      document.body.click();
    };

    render() {
      const { forwardedRef } = this.props;
      return (
        <WrappedComponent
          ref={forwardedRef}
          {...this.props}
          fetchNavMenus={this.fetchNavMenus}
          addTabMenus={this.addTabMenus}
          popEventHandle={this.popEventHandle}
          startMicroApp={this.startMicroApp}
          refreshView={this.refreshView}
          sendLocalStore={this.sendLocalStore}
          sendSiteInfo={this.sendSiteInfo}
          setLocalStore={this.setLocalStore}
          getFrameByName={this.getFrameByName}
          openView={this.openView}
          closeView={this.closeView}
          emitOutsideClick={this.emitOutsideClick}
          dispatchMouseClick={this.dispatchMouseClick}
          setControlTab={this.setControlTab}
        />
      );
    }
  }

  return React.forwardRef((props: any, ref) => <C {...props} forwardedRef={ref} />);
};
