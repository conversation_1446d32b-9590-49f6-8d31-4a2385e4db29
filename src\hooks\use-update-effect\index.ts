/*
 * @Author: 焦质晔
 * @Date: 2021-12-28 12:39:46
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-07-30 16:18:43
 */
import * as React from 'react';

const _useEffect = (
  callback: (mount: boolean) => void | VoidFunction,
  deps?: React.DependencyList
) => {
  const firstMountRef = React.useRef(true);

  React.useEffect(() => {
    return callback(firstMountRef.current);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, deps);

  React.useEffect(() => {
    firstMountRef.current = false;
    return () => {
      firstMountRef.current = true;
    };
  }, []);
};

export default function useUpdateEffect(effect: React.EffectCallback, deps?: React.DependencyList) {
  _useEffect((firstMount) => {
    if (!firstMount) {
      return effect();
    }
  }, deps);
}
