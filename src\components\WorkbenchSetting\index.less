/*
 * @Author: 焦质晔
 * @Date: 2022-06-03 19:19:16
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-11-18 13:17:09
 */
.app-header {
  .workbench-cut {
    width: 30px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 18px;
    margin: 0 5px;
    cursor: pointer;
    color: @textColorTertiary;
    &.ant-dropdown-open {
      color: @primaryColor;
    }
  }
}

.workbench-cut__popper {
  min-width: 205px !important;
  .content {
    max-height: calc(100vh - 60px);
    overflow-y: auto;
    .item {
      display: flex;
      justify-content: space-between;
      transition: none;
      padding-left: @modulePadding;
      .label {
        .handle {
          padding: 2px;
          margin-right: 2px;
          color: @textColorTertiary;
          cursor: s-resize;
          transform: scale(0.9);
        }
        user-select: none;
      }
      .icon {
        color: @primaryColor;
        display: none;
      }
      &.checked {
        background-color: var(--ant-primary-1);
        .icon {
          display: block;
        }
      }
    }
  }
}
