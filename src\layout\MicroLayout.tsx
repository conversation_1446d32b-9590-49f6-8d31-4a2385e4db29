/*
 * @Author: 焦质晔
 * @Date: 2022-12-12 19:43:39
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-12-06 16:08:56
 */
import React from 'react';
import classNames from 'classnames';
import { addUrlToken } from '@/utils';
import { IframeView } from '@/components';

import type { IRoute } from '@/utils/types';

type IProps = {
  route: IRoute;
  children?: React.ReactNode;
};

const MicroLayout: React.FC<IProps> = (props) => {
  const { path, iframePath } = props.route;

  return (
    <div className={classNames('app-iframe-container')}>
      <IframeView name={path} url={addUrlToken(iframePath!)} />
    </div>
  );
};

export default MicroLayout;
