/*
 * @Author: 焦质晔
 * @Date: 2021-02-12 21:03:36
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-08-23 12:54:16
 */
type IConfig = {
  prefix: string;
  aiChatUrl: string;
};

type IEnvCongig = Record<'dev' | 'sit' | 'uat' | 'pre' | 'prod' | 'gray', IConfig>;

const env = process.env.ENV_CONFIG || 'prod';

const config: IEnvCongig = {
  dev: {
    prefix: '/api-dev', // 网关请求前缀
    aiChatUrl: '/aio/'
  },
  sit: {
    prefix: '/api-dev',
    aiChatUrl: 'https://miw-aio-uat.faw.cn/'
  },
  uat: {
    prefix: '/api-dev',
    aiChatUrl: 'https://miw-aio-uat.faw.cn/'
  },
  pre: {
    prefix: '/api-dev',
    aiChatUrl: 'https://miw-aio-uat.faw.cn/'
  },
  prod: {
    prefix: '/api-dev',
    aiChatUrl: 'https://miw-aio.faw.cn/'
  },
  gray: {
    prefix: '/api-dev',
    aiChatUrl: 'https://miw-aio.faw.cn/'
  },
};

export default Object.assign({}, { env }, config[env]) as IConfig & { env: keyof IEnvCongig };
