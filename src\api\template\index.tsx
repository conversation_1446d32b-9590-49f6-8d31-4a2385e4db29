/*
 * @Author: 焦质晔
 * @Date: 2024-10-15 09:25:04
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-10-15 14:12:22
 */
import React from 'react';
import { t } from '@/locale';
import { useLocale } from '@/hooks';

import './index.less';

import { QmButton, notification } from '@jiaozhiye/qm-design-react';
import { CaretDownFilled, CaretUpFilled } from '@/icons';

type IProps = {
  type: 'success' | 'warning' | 'info' | 'error';
  errType: 'system' | 'business';
  message: string;
  errCode?: string;
  detailMsg?: string;
  extraInfo?: Record<string, string>;
  openRepair?: boolean;
};

export const FetchNotification = (msg: React.ReactNode, props: IProps) => {
  notification[props.type]({
    message: props.extraInfo?.title
      ? t('app.fetch.fetchError.0', { title: props.extraInfo.title })
      : t('app.information.title'),
    description: msg,
    duration: 5,
    className: `app-fetch-notification`,
  });
};

const InfoTemplate: React.FC<IProps> = (props) => {
  const {
    type = 'error',
    errType = 'system',
    message = '',
    detailMsg = '',
    errCode = '',
    extraInfo = {},
    openRepair,
  } = props;
  const { t } = useLocale();

  const [expand, setExpand] = React.useState<boolean>(false);

  const expandHandler = (ev: React.MouseEvent) => {
    ev.stopPropagation();
    setExpand(!expand);
  };

  const repairHandler = () => {
    // ...
  };

  if (!message) {
    return null;
  }

  return (
    <div className={`app-info-template`}>
      <div>
        {errCode && `${errCode}：`}
        <span className={`message`}>{message}</span>
        {detailMsg && (
          <>
            <button className={`fold-btn`} onClick={expandHandler}>
              {!expand ? t('app.header.unfold') : t('app.header.fold')}
              {!expand ? (
                <CaretDownFilled className={`icon`} />
              ) : (
                <CaretUpFilled className={`icon`} />
              )}
            </button>
            <div className={`detail-msg`} style={{ display: expand ? '' : 'none' }}>
              {detailMsg}
            </div>
          </>
        )}
      </div>
      {errType === 'system' && extraInfo.title && (
        <div>
          {t('app.fetch.fetchError.1', { leader: extraInfo.leader || t('app.settings.admin') })}
        </div>
      )}
      {openRepair && (
        <div className={`footer`}>
          <QmButton size="small" type="primary" onClick={repairHandler}>
            {t('app.fetch.repairText')}
          </QmButton>
        </div>
      )}
    </div>
  );
};

export default InfoTemplate;
