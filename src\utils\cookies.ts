/*
 * @Author: 焦质晔
 * @Date: 2021-02-12 15:42:01
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-01-08 13:23:10
 */
import Cookies from 'js-cookie';
import CryptoJS from 'crypto-js';
import { local } from '@/utils/storage';
import { getParentLocal } from '@/utils';

const SECRET_KEY = process.env.SECRET_KEY || 'default-secret-key';

const encrypt = (value: string): string => {
  return CryptoJS.AES.encrypt(value, SECRET_KEY).toString();
};

const decrypt = (value: string): string => {
  const bytes = CryptoJS.AES.decrypt(value, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

export const getToken = (): string => getParentLocal('jwt').value ?? '';
export const setToken = (val: string) => local.setItem('jwt', { value: val });
export const removeToken = (): void => local.removeItem('jwt');

export const getUserInfo = () => {
  const data = getParentLocal('user_info');
  const target: Record<string, any> = {};
  for (const [key, value] of Object.entries(data)) {
    const decryptedValue = decrypt(value);
    try {
      target[key] = JSON.parse(decryptedValue);
    } catch {
      target[key] = null;
    }
  }
  return target;
};
export const setUserInfo = (val: Record<string, any>) => {
  const target: Record<string, string> = {};
  for (const [key, value] of Object.entries(val)) {
    target[key] = encrypt(JSON.stringify(value));
  }
  local.setItem('user_info', target);
};
export const removeUserInfo = (): void => local.removeItem('user_info');

// 业务 cookie
