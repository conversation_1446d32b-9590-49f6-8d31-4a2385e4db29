/*
 * @Author: 焦质晔
 * @Date: 2022-01-17 11:07:49
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-12-27 11:12:58
 */
import * as React from 'react';
import { useSelector } from '@/store';

import type { AppState } from '@/store/reducers/app';

export default function useSize() {
  const { size } = useSelector((state: AppState) => state.app);

  return size || localStorage.getItem('size') || 'middle';
}
