/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 15:52:33
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2024-05-09 14:49:44
 */
import {
  WORKBENCH_LIST,
  NAV_LIST,
  MENU_LIST,
  CUSTOM_NAV,
  DICT_DATA,
  AUTH_DATA,
  AUTH_BTN,
  STAR_MENU,
  COMMON_MENU,
  SUB_MENU,
  TAB_MENU,
  MICRO_STATE,
  IFRAME_MENU,
  MICRO_MENU,
  PREVENT_TAB,
  COMP_SIZE,
  LOCALE_LANG,
  THEME_COLOR,
  THEME_TYPE,
  WORKBENCH,
  SIGN_IN,
  SIGN_OUT,
  SITE_INFO,
  DEVICE,
  CUSTOM_INDEX,
} from '../types';
import { whiteAuth } from '@/router';
import routes, { deepFindRoute } from '@/router/config';
import { t } from '@/locale';
import { local } from '@/utils/storage';
import { getSystem, getPathName, getGroupCode } from '@/utils';
import config from '@/config';

import type {
  ComponentSize,
  Device,
  Dictionary,
  IRoute,
  IAuthUi,
  Language,
  ThemeType,
} from '@/utils/types';

export type IWorkbench = {
  id: string;
  title: string;
  code: string;
  hidden?: boolean;
};

export type INavMenu = {
  key: string;
  title: string;
  id: string;
  icon?: string;
  sysId?: string;
  system?: string;
  wbCode?: string;
  code?: string;
  leader?: string;
  shortName?: string;
  cnDesc?: string;
  enDesc?: string;
  tagName?: string;
  type?: number;
  sideState?: string;
  virtual?: number;
  microHost?: string;
  hideInNav?: boolean;
  children?: Array<INavMenu>;
};

export type ISideMenu = {
  key: string;
  title: string;
  id?: string;
  icon?: string;
  target?: string;
  loadMethod?: 'micro' | 'iframe';
  caseHref?: string;
  system?: string;
  code?: string;
  keepAlive?: boolean;
  hideInMenu?: boolean;
  children?: Array<ISideMenu>;
};

export type ITabNav = {
  path: string;
  title: string;
  from?: string;
  search?: string;
};

export type IPreventTab = {
  path: string;
  message?: string;
  showConfirm?: boolean;
  delay?: number;
};

export type ICacheMenu = {
  key: string;
  value: string;
  keep?: boolean;
  search?: string;
};

type IState = {
  size: ComponentSize;
  lang: Language;
  device: Device;
  themeType: ThemeType;
  themeColor: string;
  workbench: string;
  microAppReady: boolean;
  workbenchList: IWorkbench[];
  customNavList: INavMenu[];
  navList: INavMenu[];
  menuList: ISideMenu[];
  sideMenus: ISideMenu[];
  starMenus: ISideMenu[];
  commonMenus: ISideMenu[];
  tabMenus: ITabNav[];
  flattenMenus: Omit<ISideMenu, 'children'>[];
  iframeMenus: ICacheMenu[];
  microMenus: ICacheMenu[];
  preventTabs: IPreventTab[];
  dict: Record<string, Dictionary[]>;
  auth: Record<string, IAuthUi>;
  authBtn: Record<string, string[]>;
  loginInfo: Record<string, string>;
  siteInfo: Record<string, string>;
  customIndex: Record<string, any>;
};

export type AppState = {
  app: IState;
};

export const deepFindNav = <T extends INavMenu>(list: T[], fn: (node: T) => boolean): T | null => {
  for (const item of list) {
    if (item.type && fn(item)) {
      return item;
    }
    if (Array.isArray(item.children)) {
      const res = deepFindNav(item.children, fn);
      if (res) {
        return res as T;
      }
    }
  }
  return null;
};

const subAppMap: Map<string, { microHost: string; title: string }> = new Map();

const createSubAppMap = <T extends INavMenu>(list: T[]) => {
  list.forEach((x) => {
    if (Array.isArray(x.children)) {
      createSubAppMap(x.children);
    }
    if (x.microHost && x.system) {
      subAppMap.set(x.system, { microHost: x.microHost, title: x.title });
    }
  });
};

export const createFlattenNavs = <T extends INavMenu>(list: T[]): T[] => {
  const res: T[] = [];
  list.forEach((x) => {
    if (Array.isArray(x.children)) {
      res.push(...createFlattenNavs(x.children as T[]));
    } else {
      res.push(x);
    }
  });
  return res;
};

export const createFlattenMenus = <T extends ISideMenu>(list: T[]): T[] => {
  const res: T[] = [];
  list.forEach((x) => {
    if (Array.isArray(x.children)) {
      res.push(...createFlattenMenus(x.children as T[]));
    } else {
      res.push(x);
    }
  });
  return res;
};

const setRouteMeta = <T extends ISideMenu>(list: T[]) => {
  const subRoutes: IRoute[] = routes.find((k) => k.path === '/')!.routes!;
  const mainAppRoutes: IRoute[] = []; // 主应用路由表
  if (config.isMainApp) {
    mainAppRoutes.push({
      path: `/chp/setting`,
      exact: true,
      meta: { title: t('app.settings.customIndex'), noAuth: true },
      iframePath: '',
      microHost: `${config.baseUrl.replace(/\/$/, '')}/custom-index/`,
      microRule: `/chp/setting`,
      component: () => null,
    });
    subAppMap.forEach((x, key) => {
      mainAppRoutes.push({
        path: `/${key}/dashboard`,
        exact: true,
        meta: { title: `${x.title} ${t('app.global.dashboard')}`, keepAlive: true, noAuth: true },
        ...(config.microType === 'iframe'
          ? {
              iframePath: x.microHost.slice(0, -1) + '/iframe/dashboard',
            }
          : {
              iframePath: ``,
              microHost: x.microHost,
              microRule: `/${key}/dashboard`,
            }),
        component: () => null, // 重要
      });
    });
  }
  // 处理 home 和 dashboard 国际化
  [whiteAuth[0], whiteAuth[2]].forEach((path) => {
    const route = subRoutes.find((x) => x.path === path);
    route && Object.assign(route.meta!, { title: t(`app.global.${path.slice(1)}`) });
  });
  list.forEach((x) => {
    const route = deepFindRoute(subRoutes, getPathName(x.key));
    if (route) {
      route.meta
        ? Object.assign(route.meta, { title: x.title })
        : (route.meta = { title: x.title });
    }
    if (config.isMainApp) {
      const path = getPathName(x.key);
      const host = subAppMap.get(path.slice(1).split('/')[0])?.microHost || '';
      const outlinkByIframe = x.caseHref && x.loadMethod === 'iframe';
      const outlinkByMicro = x.loadMethod === 'micro' && /\/iframe\/[\w-/]+/.test(x.caseHref!);
      if (path) {
        mainAppRoutes.push({
          path,
          exact: true,
          meta: { title: x.title, keepAlive: x.keepAlive },
          ...(outlinkByIframe || config.microType === 'iframe'
            ? {
                iframePath: x.caseHref || host.slice(0, -1) + x.key.replace(/^\/[^/]+/, '/iframe'),
              }
            : {
                iframePath: ``, // iframePath 与 microHost、microRule 不共存
                microHost: !outlinkByMicro ? host : x.caseHref!.split('iframe')[0],
                microRule: !outlinkByMicro ? path : x.caseHref!.match(/\/iframe\/[\w-/]+/)![0],
              }),
          component: () => null, // 重要
        });
      }
    }
  });
  if (config.isMainApp) {
    const _routePaths = new Set(mainAppRoutes.map((x) => x.path));
    for (let i = 0; i < subRoutes.length; i++) {
      if (_routePaths.has(subRoutes[i].path)) {
        subRoutes.splice(i, 1);
        i = i - 1;
      }
    }
    subRoutes.splice(-3, 0, ...mainAppRoutes);
    // 修正兜底路由
    subRoutes[subRoutes.length - 1].redirect = '/404';
  }
};

const setLocalSystem = (value: string) => {
  localStorage.setItem('system_name', value);
};

const getLocalTabMenus = () => {
  return local.getItem('tab_menus') || [];
};

const setLocalTabMenus = (tabMenus: ITabNav[]) => {
  local.setItem('tab_menus', tabMenus);
};

/**
 * 初始化 state
 */
const initState: IState = {
  size: (localStorage.getItem('size') || config.size) as ComponentSize, // 组件尺寸
  lang: (localStorage.getItem('lang') || config.lang) as Language, // 多语言
  device: 'desktop', // 设备类型
  themeType: config.themeType, // 主题模式
  themeColor: process.env.THEME_COLOR || '', // 主题颜色
  workbench: getGroupCode(), // 当前工作台(角色组)
  microAppReady: true, // 微应用加载状态
  workbenchList: [], // 工作台(角色组)列表
  customNavList: [], // 自定义导航
  navList: [], // 导航原始数据
  menuList: [], // 菜单原始数据
  sideMenus: [], // 侧栏菜单数据
  starMenus: [], // 收藏菜单
  commonMenus: [], // 常用菜单
  tabMenus: [], // 顶部选项卡菜单数据
  flattenMenus: [], // 展平后的三级菜单列表
  iframeMenus: [], // iframe 列表
  microMenus: [], // microApp 列表
  preventTabs: [], // 不允许关闭的页签列表
  dict: {}, // 数据字典
  auth: {}, // 界面权限
  authBtn: {}, // 按钮权限
  loginInfo: {}, // 用户登录信息
  siteInfo: {}, // 站点信息
  customIndex: {}, // 自定义首页
};

const setWorkbenchList = (state: IState, payload) => {
  return Object.assign({}, state, {
    workbenchList: payload,
  });
};

const setNavList = (state: IState, payload) => {
  subAppMap.clear();
  createSubAppMap(payload);
  const _system: string = config.isMainApp
    ? window.location.pathname.match(/^\/+([^/]+)/)?.[1]
    : payload[0]?.system;
  if (_system) {
    setLocalSystem(_system);
  }
  return Object.assign({}, state, {
    navList: payload,
  });
};

const setMenuList = (state: IState, payload) => {
  const flattenMenus = createFlattenMenus(payload);
  setRouteMeta(flattenMenus);
  // 初始化 tabMenus
  const tabMenus: ITabNav[] = [];
  getLocalTabMenus().forEach((x) => {
    const menuItem = flattenMenus.find((k) => getPathName(k.key) === x.path);
    if (x.path.endsWith('/dashboard') || menuItem) {
      if (menuItem) {
        tabMenus.push(Object.assign(x, { title: menuItem.title }));
      } else {
        const routeItem = deepFindRoute(routes, x.path);
        routeItem && tabMenus.push(Object.assign(x, { title: routeItem.meta!.title }));
      }
    }
  });
  setLocalTabMenus(tabMenus);
  return Object.assign({}, state, {
    menuList: payload,
    sideMenus: payload.find((x) => x.system === getSystem())?.children || [],
    flattenMenus,
    tabMenus,
  });
};

const setCustomNav = (state: IState, payload) => {
  return Object.assign({}, state, {
    customNavList: payload,
  });
};

// 设置子菜单
const setSubMenus = (state: IState, payload) => {
  setLocalSystem(payload);
  return Object.assign({}, state, {
    sideMenus: state.menuList.find((x) => x.system === payload)?.children || [],
  });
};

const addTabMenu = <T extends ITabNav>(tabMenus: T[], data: T) => {
  const target = tabMenus.find((x) => x.path === data.path);
  if (!target) {
    const v = tabMenus.findIndex((x) => x.path === data.from);
    if (v !== -1) {
      tabMenus.splice(v + 1, 0, data);
    } else {
      tabMenus.push(data);
    }
    return [...tabMenus];
  }
  if (data.search) {
    Object.assign(target, data);
  } else {
    delete target.search;
  }
  return [...tabMenus];
};

// 设置顶部选项卡导航
const setTabMenus = (state: IState, payload, behavior) => {
  const results: ITabNav[] =
    behavior === 'add'
      ? addTabMenu(state.tabMenus, payload)
      : state.tabMenus.filter((x) => x.path !== payload);
  setLocalTabMenus(results);
  return Object.assign({}, state, {
    tabMenus: results,
  });
};

const addIframeMenu = <T extends ICacheMenu>(iframeMenus: T[], data: T) => {
  const target = iframeMenus.find((x) => x.key === data.key);
  if (!target) {
    return [...iframeMenus, data];
  }
  if (target.value !== data.value) {
    target.value = data.value;
  }
  return [...iframeMenus];
};

// 设置 iframe 导航
const setIframeMenus = (state: IState, payload, behavior) => {
  return Object.assign({}, state, {
    iframeMenus:
      behavior === 'add'
        ? addIframeMenu(state.iframeMenus, payload)
        : state.iframeMenus.filter((x) => x.key !== payload),
  });
};

const addMicroMenu = <T extends ICacheMenu>(microMenus: T[], data: T) => {
  const target = microMenus.find((x) => x.key === data.key);
  if (!target) {
    return [...microMenus, data];
  }
  if (data.search) {
    Object.assign(target, data);
  } else {
    delete target.search;
  }
  return [...microMenus];
};

// 设置 micro 导航
const setMicroMenus = (state: IState, payload, behavior) => {
  return Object.assign({}, state, {
    microMenus:
      behavior === 'add'
        ? addMicroMenu(state.microMenus, payload)
        : state.microMenus.filter((x) => x.key !== payload),
  });
};

const addPreventTab = <T extends IPreventTab>(preventTabs: T[], data: T) => {
  const target = preventTabs.find((x) => x.path === data.path);
  if (!target) {
    return [...preventTabs, data];
  }
  if (data.message) {
    Object.assign(target, data);
  }
  return [...preventTabs];
};

// 设置阻止关闭选项卡
const setPreventTabs = (state: IState, payload, behavior) => {
  return Object.assign({}, state, {
    preventTabs:
      behavior === 'add'
        ? addPreventTab(state.preventTabs, payload)
        : state.preventTabs.filter((x) => x.path !== payload),
  });
};

// 设置子应用加载状态
const setMicroState = (state: IState, payload) => {
  return Object.assign({}, state, {
    microAppReady: payload,
  });
};

// 设置数据字典
const setDictData = (state: IState, payload) => {
  return Object.assign({}, state, {
    dict: payload,
  });
};

// 设置界面权限
const setAuthData = (state: IState, payload) => {
  return Object.assign({}, state, {
    auth: payload,
  });
};

// 设置按钮权限
const setAuthBtn = (state: IState, payload) => {
  return Object.assign({}, state, {
    authBtn: payload,
  });
};

// 设置收藏菜单
const setStarMenus = (state: IState, payload) => {
  return Object.assign({}, state, {
    starMenus: payload,
  });
};

// 设置常用菜单
const setCommonMenus = (state: IState, payload) => {
  return Object.assign({}, state, {
    commonMenus: payload,
  });
};

// 设置尺寸
const setComponentSize = (state: IState, payload) => {
  return Object.assign({}, state, {
    size: payload,
  });
};

// 设置多语言
const setLocaleLang = (state: IState, payload) => {
  return Object.assign({}, state, {
    lang: payload,
  });
};

// 设置主题颜色
const setThemeColor = (state: IState, payload) => {
  return Object.assign({}, state, {
    themeColor: payload,
  });
};

// 设置主题模式
const setThemeType = (state: IState, payload) => {
  return Object.assign({}, state, {
    themeType: payload,
  });
};

// 设置设备类型
const setDeviceType = (state: IState, payload) => {
  return Object.assign({}, state, {
    device: payload,
  });
};

// 设置工作台
const setWorkbench = (state: IState, payload) => {
  localStorage.setItem('workbench_code', payload);
  return Object.assign({}, state, {
    workbench: payload,
  });
};

// 登录
const setSignIn = (state: IState, payload) => {
  return Object.assign({}, state, {
    loginInfo: payload,
  });
};

// 退出登录
const setSignOut = (state: IState, payload) => {
  return Object.assign({}, state, {
    loginInfo: payload || {},
  });
};

// 设置站点信息
const setSiteInfo = (state: IState, payload) => {
  return Object.assign({}, state, {
    siteInfo: payload,
  });
};

// 自定义首页
const setCustomIndex = (state: IState, payload) => {
  return Object.assign({}, state, {
    customIndex: payload,
  });
};

// 必须要给 state 参数默认赋值 initState
export const appReducer = (state: IState = initState, action) => {
  switch (action.type) {
    case WORKBENCH_LIST:
      return setWorkbenchList(state, action.payload);
    case NAV_LIST:
      return setNavList(state, action.payload);
    case MENU_LIST:
      return setMenuList(state, action.payload);
    case CUSTOM_NAV:
      return setCustomNav(state, action.payload);
    case DICT_DATA:
      return setDictData(state, action.payload);
    case AUTH_DATA:
      return setAuthData(state, action.payload);
    case AUTH_BTN:
      return setAuthBtn(state, action.payload);
    case MICRO_STATE:
      return setMicroState(state, action.payload);
    case STAR_MENU:
      return setStarMenus(state, action.payload);
    case COMMON_MENU:
      return setCommonMenus(state, action.payload);
    case SUB_MENU:
      return setSubMenus(state, action.payload);
    case TAB_MENU:
      return setTabMenus(state, action.payload, action.behavior);
    case IFRAME_MENU:
      return setIframeMenus(state, action.payload, action.behavior);
    case MICRO_MENU:
      return setMicroMenus(state, action.payload, action.behavior);
    case PREVENT_TAB:
      return setPreventTabs(state, action.payload, action.behavior);
    case COMP_SIZE:
      return setComponentSize(state, action.payload);
    case LOCALE_LANG:
      return setLocaleLang(state, action.payload);
    case THEME_COLOR:
      return setThemeColor(state, action.payload);
    case THEME_TYPE:
      return setThemeType(state, action.payload);
    case DEVICE:
      return setDeviceType(state, action.payload);
    case WORKBENCH:
      return setWorkbench(state, action.payload);
    case SIGN_IN:
      return setSignIn(state, action.payload);
    case SIGN_OUT:
      return setSignOut(state, action.payload);
    case SITE_INFO:
      return setSiteInfo(state, action.payload);
    case CUSTOM_INDEX:
      return setCustomIndex(state, action.payload);
    default:
      return state;
  }
};
