import { getToken, getUserInfo } from '@/utils/cookies';
import envMaps from '@/config/envMaps';
import { getSessionId } from '../../utils/aiUtil';
import { APPID, DEFAULT_MODAL } from './constants';
import { safeStringify } from '../../utils/jsonUtil';
import { getChatSetting } from '../../utils/aiSettingUtil';
// 真实AI流式请求
// export const fetchAIStreamResponse = async (message, onChunk, onComplete, modalValue, uuid) => {
//     try {
//         const chatSettingModalValue = getChatSetting()?.modal?.value;
//         const controller = new AbortController();
//         const signal = controller.signal;
//         window.currentAbortController = controller;
//         const modalName = modalValue || chatSettingModalValue || DEFAULT_MODAL.value;
//         const sessionId = uuid || getSessionId();
//         const response = await fetch(`${envMaps.aiChatUrl}aiChat/continuousSession`, {
            
//             method: 'POST',
//             headers: {
//                 'Content-Type': 'application/json',
//                 'Authorization': `Bearer ${getToken()}` // 如果有的话
//             },
//             body: safeStringify({
//                 "userId": sessionId,
//                 "sessionId": sessionId,
//                 'appId': APPID,
//                 "modelName": modalName,
//                 "question": message
//             }),
//             signal: signal,
//         })


//         if (!response.ok) {
//             throw new Error(`HTTP error! status: ${response.status}`);
//         }

//         // 从响应中获取读取器
//         const reader = response.body.getReader();
//         const decoder = new TextDecoder();

//         let sumChunk = '';
//         let originSumChunk = '';
//         // eslint-disable-next-line no-constant-condition
//         while (true) {
//             const { done, value } = await reader.read();
//             if (done) {
//                 if (originSumChunk) {
//                     let newChunk = originSumChunk.replaceAll('\ndata:', '\n');
//                     if (newChunk.startsWith('data:')) {
//                         newChunk = newChunk.substring(5, newChunk.length);
//                     }
//                     onChunk(sumChunk += newChunk);
//                 }
//                 onComplete();
//                 break;
//             }

//             // 解码并处理数据块
//             const chunk = decoder.decode(value, { stream: true });
//             originSumChunk += chunk;
//             const arr = originSumChunk.split('\n\ndata:')
//             originSumChunk = arr.pop();
//             arr.forEach((line, index) => {
//                 const newLine = line.replaceAll('\ndata:', '\n')
//                 if (index === 0 && newLine.startsWith('data:')) {
//                     onChunk(sumChunk += newLine.substring(5, newLine.length));
//                 } else {
//                     onChunk(sumChunk += newLine)
//                 }
//             })
//             // onChunk(chunk.substring(5, chunk.length - 1));
//         }
//     } catch (error) {
//         if (error.name === 'AbortError') {
//             onComplete();
//             return;
//         }
//         console.error('Fetch error:', error.message);
//         onChunk('\n\n**Error:** 无法获取AI回复,请稍后再试。', true);
//         onComplete();
//     }
// };

export const fetchAIStreamResponse1 = async (message, onChunk, onComplete, modalValue, uuid) => {
    try {
        const chatSettingModalValue = getChatSetting()?.modal?.value;
        const controller = new AbortController();
        const signal = controller.signal;
        window.currentAbortController = controller;
        const modalName = modalValue || chatSettingModalValue || DEFAULT_MODAL.value;
        const sessionId = uuid || getSessionId();
        const userInfo = getUserInfo();
        const response = await fetch(`${envMaps.aiChatUrl}aiChat/continuousSession`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${getToken()}`
            },
            body: safeStringify({
                "userId": sessionId,
                "sessionId": sessionId,
                'appId': APPID,
                "modelName": modalName,
                "question": message,
                'userInfo': {
                    userCode: userInfo.loginName
                }
            }),
            signal: signal,
        })

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        
        let accumulatedContent = '';  // 累积所有内容的拼接结果
        let buffer = '';             // 未处理的字符串缓冲区
        
        // eslint-disable-next-line no-constant-condition
        while (true) {
            const { done, value } = await reader.read();
            if (done) {
                // 处理缓冲区中剩余的数据
                processBuffer(buffer, data => {
                    accumulatedContent += data.content || '';
                    onChunk(accumulatedContent);
                });
                onComplete();
                break;
            }
            
            // 解码并添加到缓冲区
            const chunk = decoder.decode(value, { stream: true });
            buffer += chunk;
            
            // 处理缓冲区中的所有完整事件
            buffer = processBuffer(buffer, data => {
                // 当收到数据块时，累积内容，并触发回调
                accumulatedContent += data.content || '';
                onChunk(accumulatedContent);
            });
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            onComplete();
            return;
        }
        console.error('Fetch error:', error.message);
        onChunk('\n\n**Error:** 无法获取AI回复,请稍后再试。', true);
        onComplete();
    }
};

// 辅助函数：处理缓冲区中的所有完整事件
function processBuffer(buffer, callback) {
    // 存储未处理的剩余字符串
    let remaining = '';
    // 按换行符分割事件（不同系统可能使用不同的换行符）
    const eventArray = buffer.split(/\r\n|\n|\r/);
    
    for (const line of eventArray) {
        const trimmedLine = line.trim();
        if (!trimmedLine) continue;  // 跳过空行
        
        // 处理以"data:"开头的事件
        if (trimmedLine.startsWith('data:')) {
            try {
                // 提取JSON部分并解析
                const jsonString = trimmedLine.replace(/^data:\s*/, '');
                if (jsonString) {
                    const data = JSON.parse(jsonString);
                    callback(data);
                }
            } catch (e) {
                console.error('JSON parse error:', e);
            }
        } else {
            // 如果遇到非事件行，保留在剩余缓冲区
            remaining += line + '\n';
        }
    }
    
    return remaining;
}
