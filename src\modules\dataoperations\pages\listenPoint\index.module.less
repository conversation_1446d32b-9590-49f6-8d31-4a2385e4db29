.searchFormContainer {
    padding: 24px;
    background-color: #fff;
    margin-bottom: 24px;
    border-radius: 8px;

    :global {
        .ant-form-item {
            margin-bottom: 16px; // 保持间距
        }
    }
}
.dataTableContainer {
    padding: 24px;
    background-color: #fff;
    border-radius: 8px;

    .tableHeader {
        display: flex;
        justify-content: flex-end; // 右对齐下载按钮
        margin-bottom: 16px;
    }

    .truncatedText {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 250px; // 根据实际情况调整，避免过宽
    }

    .paginationWrapper {
        margin-top: 16px;
        text-align: right; // 分页组件右对齐
    }
}
