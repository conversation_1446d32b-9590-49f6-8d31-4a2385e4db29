import React, { useState, useRef, useEffect } from 'react';
import { Tooltip } from 'antd';
import PropTypes from 'prop-types';

/**
 * 智能省略组件：
 * - 文本超出指定宽度时显示省略号。
 * - 仅在文本被省略时，鼠标悬停才显示完整的 Tooltip 提示。
 */
const SmartEllipsis = ({ children, width = 200, style = {}, className, placement = 'topLeft' }) => {
  // 状态：用于记录文本是否被截断
  const [isTruncated, setIsTruncated] = useState(false);
  // Ref：用于获取DOM元素的引用
  const textRef = useRef(null);

  // 定义用于文本溢出的基础样式
  const ellipsisStyle = {
    width: width,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    ...style, // 允许外部传入的样式覆盖或扩展
  };

  // 使用 useEffect Hook，在组件渲染完成后执行检测逻辑
  useEffect(() => {
    // 确保 ref.current 存在
    if (textRef.current) {
      // 核心判断：内容的实际宽度 > 容器的可见宽度
      const checkTruncation = textRef.current.scrollWidth > textRef.current.clientWidth;
      // 如果检测结果与当前状态不同，则更新状态
      if (checkTruncation !== isTruncated) {
          setIsTruncated(checkTruncation);
      }
    }
    // 依赖项数组：当文本内容(children)或容器宽度(width)变化时，重新执行此effect
  }, [children, width, isTruncated]);

  return (
    <Tooltip
      placement={placement}
      // 关键：只有当 isTruncated 为 true 时，才将完整文本作为 title 传入
      // 当 title 为 undefined、null 或空字符串时，Tooltip 不会渲染
      title={isTruncated ? children : ''}
    >
      <div ref={textRef} style={ellipsisStyle} className={className}>
        {children}
      </div>
    </Tooltip>
  );
};

// 为组件添加属性类型检查
SmartEllipsis.propTypes = {
  children: PropTypes.string.isRequired,
  width: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  style: PropTypes.object,
  className: PropTypes.string,
  placement: PropTypes.string,
};

export default SmartEllipsis;