/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 14:15:47
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2022-07-23 11:29:58
 */
// @font-face {
//   font-family: 'FAW Text Light';
//   src: url('../fonts/faw-font.ttf?t=1567214741873');
// }
@font-face {
  font-family: 'Qimiao Variable Type';
  src: url("../QimiaoVariableType.ttf");
}

html,
body {
  width: 100%;
  height: 100%;
}

body {
  // font-family: 'FAW Text Light';
  font-family: LarkHackSafariFont, LarkEmojiFont, LarkChineseQuote, -apple-system,
    BlinkMacSystemFont, 'Helvetica Neue', Tahoma, 'PingFang SC', 'Microsoft Yahei', Arial,
    'Hiragino Sans GB', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: none;
  overflow-x: hidden;
}

ul,
ol {
  list-style: none;
}

.text_overflow_cut {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/**
 * scrollbar style
 */
/* 滚动条整体部分 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
/* 滚动条的轨道 */
::-webkit-scrollbar-track {
  background-color: transparent;
}
/* 滚动条里面的小方块，能向上向下移动 */
::-webkit-scrollbar-thumb {
  background-color: #d0d0d0;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background-color: #b4b4b4;
}
::-webkit-scrollbar-thumb:active {
  background-color: #a0a0a0;
}
/* 边角，即两个滚动条的交汇处 */
::-webkit-scrollbar-corner {
  background-color: transparent;
}

::-ms-clear,
::-ms-reveal {
  display: none;
}

/**
 * app style
 */
#app {
  height: 100%;
}

.app-iframe {
  padding: @modulePadding @modulePadding 0;
  box-sizing: border-box;
}

.fixed-footer {
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 9;
  border-top: 1px solid @borderColor;
  padding: @modulePadding @modulePadding + 5px;
  background: #fff;
  text-align: right;
  justify-content: flex-end;
}

.qm-table {
  .cell {
    .ant-btn-text,
    .ant-btn-link {
      padding-left: 0;
      padding-right: 0;
      + .ant-btn-text,
      + .ant-btn-link {
        margin-left: @moduleMargin - 2px;
      }
    }
  }
}
