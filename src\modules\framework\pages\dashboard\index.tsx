/*
 * @Author: 焦质晔
 * @Date: 2021-07-06 12:54:20
 * @Last Modified by: 焦质晔
 * @Last Modified time: 2023-01-06 11:48:41
 */
import React from 'react';
import { getGroupCode } from '@/utils';
import { getUserInfo } from '@/utils/cookies';
import config from '@/config';
import { getDashboardDefine } from '../../api/dashboard';

import { QmSpin } from '@jiaozhiye/qm-design-react';
import CustomDashboard from '@/layout/modules/CustomDashboard';
import PageView from './PageView';

const Dashboard: React.FC = () => {
  const [loading, setLoading] = React.useState<boolean>(true);
  const [workSpace, setWorkSpace] = React.useState<any>(null);

  React.useEffect(() => {
    getCustomDashboard();
  }, []);

  const getCustomDashboard = async () => {
    if (process.env.MOCK_DATA !== 'true') {
      try {
        if (window.__MAIM_APP_ENV__) {
          const res = await getDashboardDefine({
            workbenchCode: getGroupCode(),
            userId: getUserInfo().id,
            appCode: config.code,
          });
          if (res.code === 200) {
            const data = res.data.value || null;
            setWorkSpace(data);
          }
        }
      } catch (err) {
        // ...
      }
    }
    setLoading(false);
  };

  const spinStyle: React.CSSProperties = {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  };

  return (
    <>
      {loading ? (
        <QmSpin size="large" tip={null} style={spinStyle} />
      ) : workSpace ? (
        <CustomDashboard workSpace={workSpace} />
      ) : (
        <PageView />
      )}
    </>
  );
};

export default Dashboard;
