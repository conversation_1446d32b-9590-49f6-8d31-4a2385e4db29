import React, { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { Radio, Form, Input, Button, Modal, InputNumber } from 'antd';
import Tree from '../Tree';
import css from './index.module.less';

const RobotAdd = forwardRef(({ detail, actionData, setChangeMode }, ref) => {
  const [form] = Form.useForm();
  const [mode, setMode] = useState('test');
  const [visible, setVisible] = useState(false);
  const [categoryList, setCategoryList] = useState([]);

  const handleModeChange = (e) => {
    setMode(e.target.value);
    setChangeMode(e.target.value, detail);
  };

  const handleOk = () => {
    setVisible(false);
  };

  const handleCancel = () => {
    setVisible(false);
  };

  useImperativeHandle(ref, () => ({
    getData: (callback) => {
      form.validateFields().then((values) => {
        let obj = {
          ...values,
          robotName: values.robotName,
          categoryId: categoryList.map((item) => item.id),
        };
        if (actionData.type == 'edit') {
          obj.id = detail.id;
        }
        callback(obj);
      });
    },
  }));

  useEffect(() => {
    if (detail.robotName) {
      form.setFieldsValue({
        ...detail
      })
      form.setFieldValue('robotName', detail.robotName);
      setCategoryList(detail.categoryList);
    } else {
      form.resetFields();
      setCategoryList([]);
    }
    if (actionData.type == 'view') {
      setMode('prod');
    }
  }, [detail, form, actionData]);

  return (
    <>
      <Radio.Group onChange={handleModeChange} value={mode} style={{ marginBottom: 8 }}>
        <Radio.Button value="test">测试环境</Radio.Button>
        <Radio.Button value="prod">正式环境</Radio.Button>
      </Radio.Group>
      <Form layout="vertical" form={form}>
        <Form.Item label="机器人名称" name="robotName" rules={[{ required: true }]}>
          <Input
            placeholder="请输入"
            disabled={actionData.type == 'view' || mode == 'prod'}
            maxLength={50}
          />
        </Form.Item>
        <Form.Item label="召回片段数量" name="topK" rules={[{ required: true }]} initialValue={3}>
          <InputNumber
            placeholder="请输入"
            disabled={actionData.type == 'view' || mode == 'prod'}
            step={1}
            min={0}
            max={999999}
          />
        </Form.Item>
        <Form.Item label="相似度阈值" name="similarityThreshold" rules={[{ required: true }]} initialValue={0.8}>
          <InputNumber
            placeholder="请输入"
            disabled={actionData.type == 'view' || mode == 'prod'}
            step={0.01}
            min={0}
            max={999999}
          />
        </Form.Item>
        <Form.Item label="是否开启重排序" name="rerankOpen" initialValue={0}>
          <Radio.Group
            disabled={actionData.type === 'view' || mode === 'prod'}
          >
            <Radio value={1}>是</Radio>
            <Radio value={0}>否</Radio>
          </Radio.Group>
        </Form.Item>
      </Form>
      {actionData.type == 'view'
        ? ''
        : mode == 'test' && (
          <Button
            type="primary"
            onClick={() => {
              setVisible(true);
            }}
          >
            绑定知识类目
          </Button>
        )}
      <ul className={css.ulBox}>
        {categoryList.map((item) => {
          return <li key={item.key}>{item.name}</li>;
        })}
      </ul>
      <Modal width="50%" title="绑定FAQ类目" open={visible} onOk={handleOk} onCancel={handleCancel}>
        <Tree
          env={mode}
          categoryList={categoryList}
          setCategoryList={(val) => setCategoryList(val)}
        />
      </Modal>
    </>
  );
});

RobotAdd.displayName = 'RobotAdd';
export default RobotAdd;
