.page {
  width: 100%;
  height: 100%;
  background: #F6F7FF url("./assets/<EMAIL>") no-repeat top;
  background-size: 100% auto;
  padding: 80px;
  display: flex;
  flex-direction: column;

  .pageTitle {
    font-family: Qimiao Variable Type;
    font-size: 24px;
    font-weight: 600;
    line-height: 24px;
    letter-spacing: 0;
    color: #262626;
    margin-bottom: 40px;
  }

  .tabButton {
    border-radius: 2px;
    opacity: 1;
    height: 32px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    gap: 12px;
    box-sizing: border-box;
    border: 1px solid rgba(255, 255, 255, 0.92);
    cursor: pointer;
  }

  .pageContent {
    flex-grow: 1;
    background: #ffffff;
    border-radius: 8px;
    opacity: 1;
    overflow: scroll;

    .contentTitle {
      width: 100%;
      height: 24px;
      display: flex;
      gap: 10px;

      font-family: Qimiao Variable Type;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      letter-spacing: 0;
      color: #262626;
      margin-bottom: 8px;
    }

    .contentTitle::before {
      width: 24px;
      height: 24px;
      content: '';
      background: url("./assets/容器@1x.png");
    }

    .list {
      flex-grow: 1;
      width: 100%;
      border-right: 1px solid #EEEFF1;
      padding-right: 16px;

      .listItem {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        border-radius: 4px;
        align-items: center;
        padding: 9px 12px;
        background: #F4F4FF;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        letter-spacing: 0;
        color: #3A2AE4;
        max-width: 100%;
        overflow: hidden;

        span {
          white-space: pre-wrap;
          word-break: break-all;
        }

        .tag {
          color: #ffffff;
          border-radius: 14px;
          padding: 3px 4px;
          font-size: 12px;
          font-weight: 500;
          margin-right: 8px;
        }
      }
    }

    .detailContent {
      flex-grow: 1;
      width: 100%;
      padding: 0 16px;
      background: linear-gradient(180deg, #F4F4FF 0%, rgba(255, 255, 255, 0) 100%);
      border-radius: 4px;
      display: flex;
      flex-direction: column;
      overflow-y: scroll;

      .detailItem {
        border-bottom: 1px solid #EEEFF1;
        display: flex;
        gap: 16px;
        padding: 20px;
        .index {
          width: 22px;
          height: 22px;
          display: flex;
          flex-direction: row;
          border-radius: 4px;
          justify-content: center;
          align-items: center;
          color: #3A2AE4;
          padding: 0 7px;
          gap: 10px;
          background: rgba(58, 42, 228, 0.1);
          box-sizing: border-box;
          border: 1px solid rgba(58, 42, 228, 0.2);
        }

        .text {
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          letter-spacing: 0;
          color: #262626;
        }
      }
    }
  }
}